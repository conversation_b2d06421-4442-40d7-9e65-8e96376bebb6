#!/usr/bin/env python3
"""
Tesla Bot - Monitor and Open Browser Version
Continuously monitors Tesla inventory and opens order page when Juniper Model Y becomes available
"""

import time
import sys
import threading
import keyboard
from datetime import datetime
from typing import List, Dict, Optional

from config import get_config
from utils import (
    setup_logging, make_api_request, check_vehicle_availability,
    open_order_page, format_vehicle_info, play_notification_sound,
    get_user_confirmation, print_status, handle_keyboard_interrupt,
    log_vehicle_found, create_session
)

class TeslaMonitorBot:
    """Tesla inventory monitoring bot that opens browser when vehicle is found"""
    
    def __init__(self):
        self.config = get_config()
        self.logger = setup_logging("tesla_monitor_bot")
        self.session = create_session()
        self.is_running = False
        self.is_paused = False
        self.found_vehicles = []
        
        # Setup keyboard shortcuts
        self.setup_keyboard_shortcuts()
        
        print("🚗 Tesla Monitor Bot - Browser Version")
        print("=" * 50)
        print(f"Target: {self.config['target_vehicle']['variant_keywords']} Model Y")
        print(f"Max Price: {self.config['target_vehicle']['target_price']:,} TL")
        print(f"Check Interval: {self.config['monitoring']['check_interval']} seconds")
        print("=" * 50)
        print("Keyboard Shortcuts:")
        print("  Ctrl+R: Reinitialize/Resume monitoring")
        print("  Ctrl+P: Pause/Resume monitoring")
        print("  Ctrl+Q: Quit")
        print("=" * 50)
    
    def setup_keyboard_shortcuts(self):
        """Setup keyboard shortcuts for bot control"""
        try:
            keyboard.add_hotkey('ctrl+r', self.reinitialize_monitoring)
            keyboard.add_hotkey('ctrl+p', self.toggle_pause)
            keyboard.add_hotkey('ctrl+q', self.quit_bot)
        except Exception as e:
            self.logger.warning(f"Could not setup keyboard shortcuts: {e}")
    
    def reinitialize_monitoring(self):
        """Reinitialize monitoring (Ctrl+R)"""
        if not self.is_running:
            print_status("Reinitializing monitoring...", "INFO")
            self.start_monitoring()
        else:
            print_status("Restarting vehicle search...", "INFO")
            self.is_paused = False
    
    def toggle_pause(self):
        """Toggle pause state (Ctrl+P)"""
        if self.is_running:
            self.is_paused = not self.is_paused
            status = "paused" if self.is_paused else "resumed"
            print_status(f"Monitoring {status}", "INFO")
    
    def quit_bot(self):
        """Quit the bot (Ctrl+Q)"""
        print_status("Shutting down bot...", "INFO")
        self.is_running = False
        sys.exit(0)
    
    def fetch_inventory(self) -> Optional[Dict]:
        """Fetch current inventory using browser automation (fallback from blocked API)"""
        try:
            # First try API (will likely fail but worth trying)
            params = self.config["api_params"]["query"]
            response = make_api_request(
                self.config["urls"]["api"],
                params=params,
                timeout=self.config["monitoring"]["timeout"]
            )

            if response:
                self.logger.debug(f"API response received: {len(response.get('results', []))} vehicles")
                return response
            else:
                self.logger.warning("API blocked, using browser-based checking...")
                return self.fetch_inventory_browser()

        except Exception as e:
            self.logger.error(f"Error fetching inventory: {e}")
            self.logger.info("Falling back to browser-based checking...")
            return self.fetch_inventory_browser()

    def fetch_inventory_browser(self) -> Optional[Dict]:
        """Fetch inventory using browser automation as fallback"""
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC

            # Setup browser options
            chrome_options = Options()
            chrome_options.add_argument("--headless")  # Run in background
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            driver = webdriver.Chrome(options=chrome_options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            # Navigate to inventory page
            driver.get(self.config["urls"]["inventory"])
            time.sleep(3)  # Wait for page load

            # Check if page loaded successfully
            if "inventory" not in driver.current_url.lower():
                driver.quit()
                return None

            # Look for vehicle elements or price indicators
            page_source = driver.page_source.lower()

            # Check for Juniper Model Y indicators
            juniper_indicators = ["juniper", "model y", "1.900.000", "1,900,000", "1900000"]
            found_indicators = [indicator for indicator in juniper_indicators if indicator in page_source]

            driver.quit()

            if found_indicators:
                # Return a mock response indicating potential vehicles found
                return {
                    "results": [{
                        "Model": "my",
                        "TitleStatus": "new",
                        "Price": 1900000,
                        "VIN": "BROWSER_DETECTED",
                        "TRIM": "Juniper",
                        "AvailabilityStatus": "Available",
                        "browser_detection": True,
                        "found_indicators": found_indicators
                    }],
                    "browser_mode": True
                }
            else:
                return {"results": [], "browser_mode": True}

        except ImportError:
            self.logger.warning("Selenium not available for browser fallback")
            return None
        except Exception as e:
            self.logger.error(f"Browser fallback failed: {e}")
            return None
    
    def check_for_vehicles(self) -> List[Dict]:
        """Check for target vehicles in inventory"""
        inventory_data = self.fetch_inventory()

        if not inventory_data:
            # If API fails, try simple page check
            print_status("API unavailable, checking inventory page directly...", "WARNING")
            if self.check_inventory_page_simple():
                # Return a dummy vehicle to trigger browser opening
                return [{
                    "vehicle_data": {"VIN": "UNKNOWN"},
                    "price": 1900000,
                    "price_difference": 0,
                    "vin": "Check manually",
                    "trim": "Model Y",
                    "availability": "Detected on page"
                }]
            return []

        vehicles = check_vehicle_availability(inventory_data)

        if vehicles:
            self.logger.info(f"Found {len(vehicles)} matching vehicle(s)")
            log_vehicle_found(self.logger, vehicles)

        return vehicles

    def check_inventory_page_simple(self) -> bool:
        """Simple check of inventory page when API is unavailable"""
        try:
            import requests

            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                "Accept-Language": "tr-TR,tr;q=0.9,en;q=0.8"
            }

            response = requests.get(self.config["urls"]["inventory"], headers=headers, timeout=30)

            if response.status_code == 200:
                content = response.text.lower()

                # Look for signs of available vehicles
                positive_indicators = ["available", "mevcut", "model y", "inventory"]
                negative_indicators = ["no results", "sonuç bulunamadı", "araç bulunamadı"]

                has_positive = any(indicator in content for indicator in positive_indicators)
                has_negative = any(indicator in content for indicator in negative_indicators)

                return has_positive and not has_negative

            return False

        except Exception as e:
            self.logger.warning(f"Simple page check failed: {e}")
            return False
    
    def handle_vehicle_found(self, vehicles: List[Dict]):
        """Handle when target vehicles are found"""
        # Check if this is browser mode detection
        is_browser_mode = any(vehicle.get('browser_mode') for vehicle in vehicles)

        if is_browser_mode:
            print_status(f"POTENTIAL JUNIPER MODEL Y DETECTED! ({len(vehicles)} detection(s))", "FOUND")
            print("⚠️  Browser mode detection - manual verification required!")
        else:
            print_status(f"JUNIPER MODEL Y FOUND! ({len(vehicles)} vehicle(s))", "FOUND")

        # Play notification sound
        if self.config["monitoring"]["notification_sound"]:
            play_notification_sound()

        # Display vehicle information
        for i, vehicle in enumerate(vehicles, 1):
            print(f"\n🚗 Detection {i}:" if is_browser_mode else f"\n🚗 Vehicle {i}:")
            print(f"   {format_vehicle_info(vehicle)}")

        if is_browser_mode:
            print("\n" + "="*60)
            print("🔍 BROWSER MODE DETECTION")
            print("="*60)
            print("The bot detected potential vehicles using browser automation.")
            print("Please manually verify in the browser window that will open.")
            print("Look for:")
            print("  • Juniper Model Y around 1.9M TL")
            print("  • Available status")
            print("  • Order/Reserve button")
            print("="*60)

        # Ask user which vehicle to open (if multiple)
        selected_vehicle = None
        if len(vehicles) == 1:
            selected_vehicle = vehicles[0]
        else:
            detection_word = "detection" if is_browser_mode else "vehicle"
            print(f"\nFound {len(vehicles)} {detection_word}s. Which one would you like to open?")
            for i, vehicle in enumerate(vehicles, 1):
                print(f"{i}. {format_vehicle_info(vehicle)}")

            while True:
                try:
                    choice = input(f"Enter choice (1-{len(vehicles)}) or 'all' for all: ").strip().lower()
                    if choice == 'all':
                        # Open all vehicles
                        for vehicle in vehicles:
                            open_order_page(vehicle["vehicle_data"])
                            time.sleep(2)  # Small delay between opens
                        return
                    elif choice.isdigit() and 1 <= int(choice) <= len(vehicles):
                        selected_vehicle = vehicles[int(choice) - 1]
                        break
                    else:
                        print("Invalid choice. Please try again.")
                except (ValueError, KeyboardInterrupt):
                    print("Invalid input or cancelled.")
                    return

        # Open browser for selected vehicle
        if selected_vehicle:
            if is_browser_mode:
                print_status("Opening Tesla inventory page for manual verification...", "SUCCESS")
                # For browser mode, always open the main inventory page
                success = open_order_page()  # No specific vehicle data
            else:
                print_status("Opening order page in browser...", "SUCCESS")
                success = open_order_page(selected_vehicle["vehicle_data"])

            if success:
                if is_browser_mode:
                    print("💡 Please manually check the opened page for Juniper Model Y vehicles!")
                    print("💡 Look for vehicles around 1.9M TL and act quickly if found!")

                # Ask if user wants to continue monitoring
                if get_user_confirmation("Continue monitoring for more vehicles?"):
                    print_status("Continuing to monitor...", "INFO")
                else:
                    print_status("Monitoring stopped by user choice.", "INFO")
                    self.is_running = False
            else:
                print_status("Failed to open browser. Continuing monitoring...", "WARNING")
    
    def monitoring_loop(self):
        """Main monitoring loop"""
        check_count = 0

        while self.is_running:
            try:
                if self.is_paused:
                    time.sleep(1)
                    continue

                check_count += 1

                print_status(f"Check #{check_count} - Scanning inventory...", "INFO")

                # Check for vehicles
                vehicles = self.check_for_vehicles()

                if vehicles:
                    self.found_vehicles.extend(vehicles)
                    self.handle_vehicle_found(vehicles)

                    if not self.is_running:  # User chose to stop
                        break
                else:
                    print_status("No matching vehicles found. Continuing to monitor...", "INFO")

                # Wait for next check
                interval = self.config["monitoring"]["check_interval"]
                wait_start = time.time()
                while time.time() - wait_start < interval:
                    if not self.is_running or self.is_paused:
                        break
                    time.sleep(1)
                
            except KeyboardInterrupt:
                handle_keyboard_interrupt()
                break
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                print_status(f"Error occurred: {e}", "ERROR")
                time.sleep(5)  # Wait before retrying
    
    def start_monitoring(self):
        """Start the monitoring process"""
        if self.is_running:
            print_status("Monitoring is already running!", "WARNING")
            return
        
        self.is_running = True
        self.is_paused = False
        
        print_status("Starting Tesla inventory monitoring...", "SUCCESS")
        print_status(f"Monitoring for: {', '.join(self.config['target_vehicle']['variant_keywords'])}", "INFO")
        print_status(f"Target price: {self.config['target_vehicle']['target_price']:,} TL", "INFO")
        
        try:
            self.monitoring_loop()
        except Exception as e:
            self.logger.error(f"Fatal error in monitoring: {e}")
            print_status(f"Fatal error: {e}", "ERROR")
        finally:
            self.is_running = False
            print_status("Monitoring stopped.", "INFO")
    
    def run(self):
        """Run the bot"""
        try:
            # Initial confirmation
            if get_user_confirmation("Start monitoring Tesla inventory?"):
                self.start_monitoring()
            else:
                print_status("Monitoring cancelled by user.", "INFO")
                
        except KeyboardInterrupt:
            handle_keyboard_interrupt()
        except Exception as e:
            self.logger.error(f"Unexpected error: {e}")
            print_status(f"Unexpected error: {e}", "ERROR")
        finally:
            print("\n🙏 Thank you for using Tesla Monitor Bot!")
            if self.found_vehicles:
                print(f"📊 Session Summary: Found {len(self.found_vehicles)} vehicle(s)")

def main():
    """Main entry point"""
    try:
        bot = TeslaMonitorBot()
        bot.run()
    except Exception as e:
        print(f"Failed to start bot: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
