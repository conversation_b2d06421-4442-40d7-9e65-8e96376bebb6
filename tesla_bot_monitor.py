#!/usr/bin/env python3
"""
Tesla Bot - Monitor and Open Browser Version
Continuously monitors Tesla inventory and opens order page when Juniper Model Y becomes available
"""

import time
import sys
import threading
import keyboard
from datetime import datetime
from typing import List, Dict, Optional

from config import get_config
from utils import (
    setup_logging, check_vehicle_availability,
    open_order_page, format_vehicle_info, play_notification_sound,
    get_user_confirmation, print_status, handle_keyboard_interrupt,
    log_vehicle_found, create_session
)

class TeslaMonitorBot:
    """Tesla inventory monitoring bot that opens browser when vehicle is found"""
    
    def __init__(self):
        self.config = get_config()
        self.logger = setup_logging("tesla_monitor_bot")
        self.session = create_session()
        self.is_running = False
        self.is_paused = False
        self.found_vehicles = []
        
        # Setup keyboard shortcuts
        self.setup_keyboard_shortcuts()
        
        print("🚗 Tesla Monitor Bot - Browser Version")
        print("=" * 50)
        print(f"Target: {self.config['target_vehicle']['variant_keywords']} Model Y")
        print(f"Max Price: {self.config['target_vehicle']['target_price']:,} TL")
        print(f"Check Interval: {self.config['monitoring']['check_interval']} seconds")
        print("=" * 50)
        print("Keyboard Shortcuts:")
        print("  Ctrl+R: Reinitialize/Resume monitoring")
        print("  Ctrl+P: Pause/Resume monitoring")
        print("  Ctrl+Q: Quit")
        print("=" * 50)
    
    def setup_keyboard_shortcuts(self):
        """Setup keyboard shortcuts for bot control"""
        try:
            keyboard.add_hotkey('ctrl+r', self.reinitialize_monitoring)
            keyboard.add_hotkey('ctrl+p', self.toggle_pause)
            keyboard.add_hotkey('ctrl+q', self.quit_bot)
        except Exception as e:
            self.logger.warning(f"Could not setup keyboard shortcuts: {e}")
    
    def reinitialize_monitoring(self):
        """Reinitialize monitoring (Ctrl+R)"""
        if not self.is_running:
            print_status("Reinitializing monitoring...", "INFO")
            self.start_monitoring()
        else:
            print_status("Restarting vehicle search...", "INFO")
            self.is_paused = False
    
    def toggle_pause(self):
        """Toggle pause state (Ctrl+P)"""
        if self.is_running:
            self.is_paused = not self.is_paused
            status = "paused" if self.is_paused else "resumed"
            print_status(f"Monitoring {status}", "INFO")
    
    def quit_bot(self):
        """Quit the bot (Ctrl+Q)"""
        print_status("Shutting down bot...", "INFO")
        self.is_running = False
        sys.exit(0)
    
    def fetch_inventory(self) -> Optional[Dict]:
        """Fetch current inventory using successful HTML parsing method"""
        try:
            from utils import make_inventory_request

            response = make_inventory_request(timeout=self.config["monitoring"]["timeout"])

            if response:
                results_count = len(response.get('results', []))
                self.logger.debug(f"Inventory response received: {results_count} vehicles")
                print_status(f"Found {results_count} vehicles on inventory page", "INFO")
                return response
            else:
                self.logger.warning("No response from Tesla inventory page")
                return None

        except Exception as e:
            self.logger.error(f"Error fetching inventory: {e}")
            return None
    
    def check_for_vehicles(self) -> List[Dict]:
        """Check for target vehicles in inventory"""
        inventory_data = self.fetch_inventory()

        if not inventory_data:
            print_status("Could not fetch inventory data", "WARNING")
            return []

        vehicles = check_vehicle_availability(inventory_data)

        if vehicles:
            self.logger.info(f"Found {len(vehicles)} matching vehicle(s)")
            log_vehicle_found(self.logger, vehicles)

        return vehicles
    
    def handle_vehicle_found(self, vehicles: List[Dict]):
        """Handle when target vehicles are found"""
        print_status(f"JUNIPER MODEL Y FOUND! ({len(vehicles)} vehicle(s))", "FOUND")
        
        # Play notification sound
        if self.config["monitoring"]["notification_sound"]:
            play_notification_sound()
        
        # Display vehicle information
        for i, vehicle in enumerate(vehicles, 1):
            print(f"\n🚗 Vehicle {i}:")
            print(f"   {format_vehicle_info(vehicle)}")
        
        # Ask user which vehicle to open (if multiple)
        selected_vehicle = None
        if len(vehicles) == 1:
            selected_vehicle = vehicles[0]
        else:
            print(f"\nFound {len(vehicles)} vehicles. Which one would you like to open?")
            for i, vehicle in enumerate(vehicles, 1):
                print(f"{i}. {format_vehicle_info(vehicle)}")
            
            while True:
                try:
                    choice = input(f"Enter choice (1-{len(vehicles)}) or 'all' for all: ").strip().lower()
                    if choice == 'all':
                        # Open all vehicles
                        for vehicle in vehicles:
                            open_order_page(vehicle["vehicle_data"])
                            time.sleep(2)  # Small delay between opens
                        return
                    elif choice.isdigit() and 1 <= int(choice) <= len(vehicles):
                        selected_vehicle = vehicles[int(choice) - 1]
                        break
                    else:
                        print("Invalid choice. Please try again.")
                except (ValueError, KeyboardInterrupt):
                    print("Invalid input or cancelled.")
                    return
        
        # Open browser for selected vehicle
        if selected_vehicle:
            print_status("Opening order page in browser...", "SUCCESS")
            success = open_order_page(selected_vehicle["vehicle_data"])
            
            if success:
                # Ask if user wants to continue monitoring
                if get_user_confirmation("Continue monitoring for more vehicles?"):
                    print_status("Continuing to monitor...", "INFO")
                else:
                    print_status("Monitoring stopped by user choice.", "INFO")
                    self.is_running = False
            else:
                print_status("Failed to open browser. Continuing monitoring...", "WARNING")
    
    def monitoring_loop(self):
        """Main monitoring loop"""
        check_count = 0

        while self.is_running:
            try:
                if self.is_paused:
                    time.sleep(1)
                    continue

                check_count += 1

                print_status(f"Check #{check_count} - Scanning inventory...", "INFO")

                # Check for vehicles
                vehicles = self.check_for_vehicles()

                if vehicles:
                    self.found_vehicles.extend(vehicles)
                    self.handle_vehicle_found(vehicles)

                    if not self.is_running:  # User chose to stop
                        break
                else:
                    print_status("No matching vehicles found. Continuing to monitor...", "INFO")

                # Wait for next check
                interval = self.config["monitoring"]["check_interval"]
                wait_start = time.time()
                while time.time() - wait_start < interval:
                    if not self.is_running or self.is_paused:
                        break
                    time.sleep(1)
                
            except KeyboardInterrupt:
                handle_keyboard_interrupt()
                break
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                print_status(f"Error occurred: {e}", "ERROR")
                time.sleep(5)  # Wait before retrying
    
    def start_monitoring(self):
        """Start the monitoring process"""
        if self.is_running:
            print_status("Monitoring is already running!", "WARNING")
            return
        
        self.is_running = True
        self.is_paused = False
        
        print_status("Starting Tesla inventory monitoring...", "SUCCESS")
        print_status(f"Monitoring for: {', '.join(self.config['target_vehicle']['variant_keywords'])}", "INFO")
        print_status(f"Target price: {self.config['target_vehicle']['target_price']:,} TL", "INFO")
        
        try:
            self.monitoring_loop()
        except Exception as e:
            self.logger.error(f"Fatal error in monitoring: {e}")
            print_status(f"Fatal error: {e}", "ERROR")
        finally:
            self.is_running = False
            print_status("Monitoring stopped.", "INFO")
    
    def run(self):
        """Run the bot"""
        try:
            # Initial confirmation
            if get_user_confirmation("Start monitoring Tesla inventory?"):
                self.start_monitoring()
            else:
                print_status("Monitoring cancelled by user.", "INFO")
                
        except KeyboardInterrupt:
            handle_keyboard_interrupt()
        except Exception as e:
            self.logger.error(f"Unexpected error: {e}")
            print_status(f"Unexpected error: {e}", "ERROR")
        finally:
            print("\n🙏 Thank you for using Tesla Monitor Bot!")
            if self.found_vehicles:
                print(f"📊 Session Summary: Found {len(self.found_vehicles)} vehicle(s)")

def main():
    """Main entry point"""
    try:
        bot = TeslaMonitorBot()
        bot.run()
    except Exception as e:
        print(f"Failed to start bot: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
