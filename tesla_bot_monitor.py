#!/usr/bin/env python3
"""
Tesla Model Y Juniper Monitor Bot - Version 1
Monitors Tesla Turkey inventory and opens order pages for manual completion.

This bot continuously monitors Tesla Model Y Juniper inventory in Turkey
and automatically opens order pages in browser when vehicles become available.
"""

import time
import json
import logging
import webbrowser
from datetime import datetime
from typing import Dict, List, Optional
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, WebDriverException
import keyboard
import winsound

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tesla_monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TeslaInventoryMonitor:
    """Tesla Model Y Juniper inventory monitoring bot."""

    def __init__(self):
        self.base_url = "https://www.tesla.com"
        self.inventory_url = "https://www.tesla.com/tr_TR/inventory/new/my"
        self.api_url = "https://www.tesla.com/coinorder/api/v4/inventory-results"

        # Target specifications for Model Y Juniper
        self.target_specs = {
            "model": "my",  # Model Y
            "condition": "new",
            "market": "TR",
            "currency": "TRY",
            "max_price": 2000000,  # 2M TL max (target is 1.9M)
            "target_price": 1900000,  # 1.9M TL target
        }

        # Headers to mimic real browser
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'tr-TR,tr;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': 'https://www.tesla.com/tr_TR/inventory/new/my',
            'Origin': 'https://www.tesla.com',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
        }

        self.session = requests.Session()
        self.session.headers.update(self.headers)

        # Monitoring state
        self.is_monitoring = False
        self.found_vehicles = []
        self.last_check_time = None

        # Browser driver for opening order pages
        self.driver = None

    def setup_driver(self) -> bool:
        """Setup Chrome WebDriver with stealth options."""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")

            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            logger.info("Chrome WebDriver initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to setup WebDriver: {e}")
            return False

    def get_inventory_data(self) -> Optional[Dict]:
        """Fetch current inventory data from Tesla API."""
        try:
            # Parameters for Model Y in Turkey
            params = {
                'query': json.dumps({
                    "model": "my",
                    "condition": "new",
                    "options": {},
                    "arrangeby": "Price",
                    "order": "asc",
                    "market": "TR",
                    "language": "tr",
                    "super_region": "north_america",
                    "lng": 32.8597,  # Ankara coordinates
                    "lat": 39.9334,
                    "zip": "06100",
                    "range": 0
                })
            }

            response = self.session.get(self.api_url, params=params, timeout=30)
            response.raise_for_status()

            data = response.json()
            logger.info(f"Successfully fetched inventory data: {len(data.get('results', []))} vehicles found")
            return data

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to fetch inventory data: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse inventory response: {e}")
            return None

    def filter_juniper_vehicles(self, inventory_data: Dict) -> List[Dict]:
        """Filter vehicles to find Model Y Juniper variants."""
        if not inventory_data or 'results' not in inventory_data:
            return []

        juniper_vehicles = []

        for vehicle in inventory_data['results']:
            try:
                # Check if it's a Model Y
                if vehicle.get('Model') != 'MY':
                    continue

                # Check price range (around 1.9M TL for Juniper)
                price = vehicle.get('Price', 0)
                if not (1800000 <= price <= 2100000):  # 1.8M - 2.1M TL range
                    continue

                # Check if it's new
                if vehicle.get('TitleStatus') != 'new':
                    continue

                # Look for Juniper indicators in trim or options
                trim = vehicle.get('TRIM', [])
                trim_name = vehicle.get('TrimName', '').lower()

                # Juniper is typically the latest Model Y variant
                # Check for recent model year and specific trim codes
                model_year = vehicle.get('Year', 0)
                if model_year >= 2024:  # Juniper is 2024+
                    juniper_vehicles.append(vehicle)
                    logger.info(f"Found potential Juniper: {vehicle.get('VIN', 'Unknown VIN')} - {price} TL")

            except Exception as e:
                logger.warning(f"Error processing vehicle: {e}")
                continue

        return juniper_vehicles

    def notify_vehicle_found(self, vehicles: List[Dict]):
        """Notify user when vehicles are found."""
        logger.info(f"🚗 FOUND {len(vehicles)} Model Y Juniper vehicle(s)!")

        # Play notification sound
        try:
            winsound.Beep(1000, 1000)  # 1000Hz for 1 second
        except:
            pass

        # Print vehicle details
        for i, vehicle in enumerate(vehicles, 1):
            vin = vehicle.get('VIN', 'Unknown')
            price = vehicle.get('Price', 0)
            trim = vehicle.get('TrimName', 'Unknown')

            print(f"\n🎯 Vehicle {i}:")
            print(f"   VIN: {vin}")
            print(f"   Price: {price:,} TL")
            print(f"   Trim: {trim}")
            print(f"   Year: {vehicle.get('Year', 'Unknown')}")

    def open_order_page(self, vehicle: Dict):
        """Open the order page for a specific vehicle."""
        try:
            vin = vehicle.get('VIN')
            if not vin:
                logger.error("No VIN found for vehicle")
                return False

            # Construct order URL
            order_url = f"https://www.tesla.com/tr_TR/my/design#{vin}"

            logger.info(f"Opening order page for VIN: {vin}")

            if self.driver:
                # Use WebDriver to open in controlled browser
                self.driver.get(order_url)
                time.sleep(3)  # Wait for page to load
            else:
                # Fallback to default browser
                webbrowser.open(order_url)

            return True

        except Exception as e:
            logger.error(f"Failed to open order page: {e}")
            return False

    def monitor_inventory(self):
        """Main monitoring loop."""
        logger.info("🚀 Starting Tesla Model Y Juniper monitoring...")
        logger.info("Press Ctrl+R to reinitialize, Ctrl+C to stop")

        self.is_monitoring = True
        check_interval = 30  # Check every 30 seconds

        while self.is_monitoring:
            try:
                self.last_check_time = datetime.now()
                logger.info(f"🔍 Checking inventory at {self.last_check_time.strftime('%H:%M:%S')}")

                # Fetch inventory data
                inventory_data = self.get_inventory_data()
                if not inventory_data:
                    logger.warning("Failed to fetch inventory data, retrying...")
                    time.sleep(10)
                    continue

                # Filter for Juniper vehicles
                juniper_vehicles = self.filter_juniper_vehicles(inventory_data)

                if juniper_vehicles:
                    # New vehicles found!
                    self.notify_vehicle_found(juniper_vehicles)

                    # Ask user which vehicle to order
                    if len(juniper_vehicles) == 1:
                        print(f"\n🎯 Opening order page for the available vehicle...")
                        self.open_order_page(juniper_vehicles[0])
                    else:
                        print(f"\n🎯 Multiple vehicles found! Choose which one to order:")
                        for i, vehicle in enumerate(juniper_vehicles, 1):
                            price = vehicle.get('Price', 0)
                            print(f"   {i}. VIN: {vehicle.get('VIN', 'Unknown')} - {price:,} TL")

                        try:
                            choice = input("\nEnter vehicle number (1-{}) or 'all' for all: ".format(len(juniper_vehicles)))
                            if choice.lower() == 'all':
                                for vehicle in juniper_vehicles:
                                    self.open_order_page(vehicle)
                                    time.sleep(2)  # Small delay between opens
                            else:
                                idx = int(choice) - 1
                                if 0 <= idx < len(juniper_vehicles):
                                    self.open_order_page(juniper_vehicles[idx])
                        except (ValueError, IndexError):
                            logger.warning("Invalid choice, opening first vehicle")
                            self.open_order_page(juniper_vehicles[0])

                    # Store found vehicles
                    self.found_vehicles.extend(juniper_vehicles)

                    # Ask if user wants to continue monitoring
                    continue_monitoring = input("\n🔄 Continue monitoring? (y/n): ").lower().strip()
                    if continue_monitoring != 'y':
                        self.is_monitoring = False
                        break

                else:
                    logger.info("No Model Y Juniper vehicles available")

                # Wait before next check
                logger.info(f"⏰ Next check in {check_interval} seconds...")
                time.sleep(check_interval)

            except KeyboardInterrupt:
                logger.info("Monitoring stopped by user")
                self.is_monitoring = False
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(10)  # Wait before retrying

    def cleanup(self):
        """Clean up resources."""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("WebDriver closed")
            except:
                pass

    def run(self):
        """Main entry point."""
        try:
            # Setup WebDriver
            if not self.setup_driver():
                logger.warning("WebDriver setup failed, will use default browser for order pages")

            # Start monitoring
            self.monitor_inventory()

        except Exception as e:
            logger.error(f"Fatal error: {e}")
        finally:
            self.cleanup()

def main():
    """Main function with keyboard shortcuts."""
    monitor = TeslaInventoryMonitor()

    def reinitialize():
        """Reinitialize the monitoring."""
        logger.info("🔄 Reinitializing Tesla bot...")
        monitor.cleanup()
        monitor.__init__()
        monitor.run()

    # Register keyboard shortcuts
    keyboard.add_hotkey('ctrl+r', reinitialize)

    try:
        monitor.run()
    except KeyboardInterrupt:
        logger.info("Program terminated by user")
    finally:
        monitor.cleanup()

if __name__ == "__main__":
    main()