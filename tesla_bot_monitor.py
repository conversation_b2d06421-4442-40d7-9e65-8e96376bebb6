#!/usr/bin/env python3
"""
Tesla Model Y Juniper Monitor Bot - Version 1
Monitors Tesla Turkey inventory and opens order pages for manual completion.

This bot continuously monitors Tesla Model Y Juniper inventory in Turkey
and automatically opens order pages in browser when vehicles become available.
"""

import time
import json
import logging
import webbrowser
from datetime import datetime
from typing import Dict, List, Optional
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, WebDriverException, NoSuchElementException
from selenium.webdriver.common.keys import Keys
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import keyboard
import winsound
import random

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tesla_monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TeslaInventoryMonitor:
    """Tesla Model Y Juniper inventory monitoring bot."""

    def __init__(self):
        self.base_url = "https://www.tesla.com"
        self.inventory_url = "https://www.tesla.com/tr_TR/inventory/new/my"

        # Target specifications for Model Y Juniper
        self.target_specs = {
            "model": "my",  # Model Y
            "condition": "new",
            "market": "TR",
            "currency": "TRY",
            "max_price": 2000000,  # 2M TL max (target is 1.9M)
            "target_price": 1900000,  # 1.9M TL target
            "min_price": 1800000,  # 1.8M TL min for Juniper range
        }

        # Monitoring state
        self.is_monitoring = False
        self.found_vehicles = []
        self.last_check_time = None
        self.previous_vehicle_count = 0

        # Browser driver for monitoring and opening order pages
        self.driver = None
        self.wait = None

    def setup_driver(self) -> bool:
        """Setup Chrome WebDriver with stealth options."""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")

            # Use webdriver-manager to automatically handle ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 20)

            logger.info("Chrome WebDriver initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to setup WebDriver: {e}")
            return False

    def get_inventory_data(self) -> List[Dict]:
        """Scrape current inventory data from Tesla website using browser."""
        try:
            logger.info("Loading Tesla inventory page...")
            self.driver.get(self.inventory_url)

            # Wait for page to load
            time.sleep(3 + random.uniform(1, 3))  # Random delay to appear human

            # Wait for inventory results to load
            try:
                self.wait.until(EC.presence_of_element_located((By.CLASS_NAME, "result")))
            except TimeoutException:
                # Try alternative selectors
                try:
                    self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "[data-testid='inventory-card']")))
                except TimeoutException:
                    logger.warning("No inventory results found or page didn't load properly")
                    return []

            # Scroll to load all results
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)

            # Extract vehicle data from the page
            vehicles = []

            # Try multiple selectors for vehicle cards
            vehicle_cards = []
            selectors = [
                "[data-testid='inventory-card']",
                ".result",
                ".inventory-card",
                ".vehicle-card"
            ]

            for selector in selectors:
                vehicle_cards = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if vehicle_cards:
                    logger.info(f"Found {len(vehicle_cards)} vehicle cards using selector: {selector}")
                    break

            if not vehicle_cards:
                logger.warning("No vehicle cards found on page")
                return []

            for card in vehicle_cards:
                try:
                    vehicle_data = self.extract_vehicle_data(card)
                    if vehicle_data:
                        vehicles.append(vehicle_data)
                except Exception as e:
                    logger.warning(f"Error extracting vehicle data: {e}")
                    continue

            logger.info(f"Successfully extracted {len(vehicles)} vehicles from page")
            return vehicles

        except Exception as e:
            logger.error(f"Failed to get inventory data: {e}")
            return []

    def extract_vehicle_data(self, card_element) -> Optional[Dict]:
        """Extract vehicle data from a vehicle card element."""
        try:
            vehicle_data = {}

            # Extract price
            price_selectors = [
                "[data-testid='price']",
                ".price",
                ".vehicle-price",
                ".result-price"
            ]

            price_text = None
            for selector in price_selectors:
                try:
                    price_element = card_element.find_element(By.CSS_SELECTOR, selector)
                    price_text = price_element.text
                    break
                except NoSuchElementException:
                    continue

            if price_text:
                # Extract numeric price from text like "1.900.000 TL" or "₺1,900,000"
                price_numbers = re.findall(r'[\d.,]+', price_text.replace('.', '').replace(',', ''))
                if price_numbers:
                    try:
                        price = int(price_numbers[0])
                        vehicle_data['Price'] = price
                    except ValueError:
                        pass

            # Extract VIN or ID
            vin_selectors = [
                "[data-testid='vin']",
                ".vin",
                ".vehicle-id"
            ]

            for selector in vin_selectors:
                try:
                    vin_element = card_element.find_element(By.CSS_SELECTOR, selector)
                    vehicle_data['VIN'] = vin_element.text
                    break
                except NoSuchElementException:
                    continue

            # Extract year from text content
            card_text = card_element.text
            year_match = re.search(r'20(2[4-9]|[3-9]\d)', card_text)  # 2024 and later
            if year_match:
                vehicle_data['Year'] = int(year_match.group())

            # Extract model info
            if 'model y' in card_text.lower() or 'modely' in card_text.lower():
                vehicle_data['Model'] = 'MY'

            # Check if it's new (default assumption for inventory page)
            vehicle_data['TitleStatus'] = 'new'

            # Extract trim/variant info
            vehicle_data['TrimName'] = self.extract_trim_info(card_text)

            return vehicle_data if vehicle_data.get('Price') else None

        except Exception as e:
            logger.warning(f"Error extracting vehicle data: {e}")
            return None

    def extract_trim_info(self, text: str) -> str:
        """Extract trim information from vehicle text."""
        text_lower = text.lower()

        # Look for common Model Y trim indicators
        if 'performance' in text_lower:
            return 'Performance'
        elif 'long range' in text_lower:
            return 'Long Range'
        elif 'rwd' in text_lower or 'rear' in text_lower:
            return 'RWD'
        elif 'awd' in text_lower or 'all wheel' in text_lower:
            return 'AWD'
        else:
            return 'Standard'

    def filter_juniper_vehicles(self, vehicles: List[Dict]) -> List[Dict]:
        """Filter vehicles to find Model Y Juniper variants."""
        juniper_vehicles = []

        for vehicle in vehicles:
            try:
                # Check if it's a Model Y
                if vehicle.get('Model') != 'MY':
                    continue

                # Check price range (around 1.9M TL for Juniper)
                price = vehicle.get('Price', 0)
                min_price = self.target_specs['min_price']
                max_price = self.target_specs['max_price']

                if not (min_price <= price <= max_price):
                    continue

                # Check if it's new
                if vehicle.get('TitleStatus') != 'new':
                    continue

                # Juniper is typically the latest Model Y variant
                # Check for recent model year (2024+ for Juniper)
                model_year = vehicle.get('Year', 0)
                if model_year >= 2024:  # Juniper is 2024+
                    juniper_vehicles.append(vehicle)
                    logger.info(f"Found potential Juniper: {vehicle.get('VIN', 'Unknown VIN')} - {price:,} TL - {vehicle.get('TrimName', 'Unknown trim')}")

            except Exception as e:
                logger.warning(f"Error processing vehicle: {e}")
                continue

        return juniper_vehicles

    def notify_vehicle_found(self, vehicles: List[Dict]):
        """Notify user when vehicles are found."""
        logger.info(f"🚗 FOUND {len(vehicles)} Model Y Juniper vehicle(s)!")

        # Play notification sound
        try:
            winsound.Beep(1000, 1000)  # 1000Hz for 1 second
        except:
            pass

        # Print vehicle details
        for i, vehicle in enumerate(vehicles, 1):
            vin = vehicle.get('VIN', 'Unknown')
            price = vehicle.get('Price', 0)
            trim = vehicle.get('TrimName', 'Unknown')

            print(f"\n🎯 Vehicle {i}:")
            print(f"   VIN: {vin}")
            print(f"   Price: {price:,} TL")
            print(f"   Trim: {trim}")
            print(f"   Year: {vehicle.get('Year', 'Unknown')}")

    def open_order_page(self, vehicle: Dict):
        """Open the order page for a specific vehicle."""
        try:
            vin = vehicle.get('VIN')
            if not vin:
                logger.error("No VIN found for vehicle")
                return False

            # Try to find and click the order button directly
            try:
                # First try to find the card for this specific vehicle
                vehicle_cards = self.driver.find_elements(By.CSS_SELECTOR, "[data-testid='inventory-card']")
                if not vehicle_cards:
                    vehicle_cards = self.driver.find_elements(By.CSS_SELECTOR, ".result")

                for card in vehicle_cards:
                    if vin in card.text:
                        # Found the right card, now look for order button
                        order_buttons = card.find_elements(By.TAG_NAME, "button")
                        for button in order_buttons:
                            if any(word in button.text.lower() for word in ['order', 'sipariş', 'satın', 'buy']):
                                logger.info(f"Clicking order button for VIN: {vin}")
                                button.click()
                                time.sleep(3)  # Wait for page to load
                                return True

                        # If no button found, try clicking the card itself
                        logger.info(f"Clicking vehicle card for VIN: {vin}")
                        card.click()
                        time.sleep(3)  # Wait for page to load
                        return True

            except Exception as e:
                logger.warning(f"Failed to click order button directly: {e}")

            # Fallback: Construct order URL
            order_url = f"https://www.tesla.com/tr_TR/my/design#{vin}"

            logger.info(f"Opening order page for VIN: {vin}")

            # Use WebDriver to open in controlled browser
            self.driver.get(order_url)
            time.sleep(3)  # Wait for page to load

            return True

        except Exception as e:
            logger.error(f"Failed to open order page: {e}")

            # Last resort fallback to default browser
            try:
                order_url = f"https://www.tesla.com/tr_TR/my/design#{vin}"
                webbrowser.open(order_url)
                logger.info("Opened order page in default browser")
                return True
            except:
                return False

    def monitor_inventory(self):
        """Main monitoring loop."""
        logger.info("🚀 Starting Tesla Model Y Juniper monitoring...")
        logger.info("Press Ctrl+R to reinitialize, Ctrl+C to stop")

        self.is_monitoring = True
        check_interval = 30  # Check every 30 seconds

        while self.is_monitoring:
            try:
                self.last_check_time = datetime.now()
                logger.info(f"🔍 Checking inventory at {self.last_check_time.strftime('%H:%M:%S')}")

                # Fetch inventory data using browser
                vehicles = self.get_inventory_data()
                if not vehicles:
                    logger.warning("Failed to fetch inventory data, retrying...")
                    time.sleep(10)
                    continue

                # Filter for Juniper vehicles
                juniper_vehicles = self.filter_juniper_vehicles(vehicles)

                if juniper_vehicles:
                    # New vehicles found!
                    self.notify_vehicle_found(juniper_vehicles)

                    # Ask user which vehicle to order
                    if len(juniper_vehicles) == 1:
                        print(f"\n🎯 Opening order page for the available vehicle...")
                        self.open_order_page(juniper_vehicles[0])
                    else:
                        print(f"\n🎯 Multiple vehicles found! Choose which one to order:")
                        for i, vehicle in enumerate(juniper_vehicles, 1):
                            price = vehicle.get('Price', 0)
                            print(f"   {i}. VIN: {vehicle.get('VIN', 'Unknown')} - {price:,} TL")

                        try:
                            choice = input("\nEnter vehicle number (1-{}) or 'all' for all: ".format(len(juniper_vehicles)))
                            if choice.lower() == 'all':
                                for vehicle in juniper_vehicles:
                                    self.open_order_page(vehicle)
                                    time.sleep(2)  # Small delay between opens
                            else:
                                idx = int(choice) - 1
                                if 0 <= idx < len(juniper_vehicles):
                                    self.open_order_page(juniper_vehicles[idx])
                        except (ValueError, IndexError):
                            logger.warning("Invalid choice, opening first vehicle")
                            self.open_order_page(juniper_vehicles[0])

                    # Store found vehicles
                    self.found_vehicles.extend(juniper_vehicles)

                    # Ask if user wants to continue monitoring
                    continue_monitoring = input("\n🔄 Continue monitoring? (y/n): ").lower().strip()
                    if continue_monitoring != 'y':
                        self.is_monitoring = False
                        break

                else:
                    logger.info("No Model Y Juniper vehicles available")

                # Wait before next check
                logger.info(f"⏰ Next check in {check_interval} seconds...")
                time.sleep(check_interval)

            except KeyboardInterrupt:
                logger.info("Monitoring stopped by user")
                self.is_monitoring = False
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(10)  # Wait before retrying

    def cleanup(self):
        """Clean up resources."""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("WebDriver closed")
            except:
                pass

    def run(self):
        """Main entry point."""
        try:
            # Setup WebDriver
            if not self.setup_driver():
                logger.warning("WebDriver setup failed, will use default browser for order pages")

            # Start monitoring
            self.monitor_inventory()

        except Exception as e:
            logger.error(f"Fatal error: {e}")
        finally:
            self.cleanup()

def main():
    """Main function with keyboard shortcuts."""
    monitor = TeslaInventoryMonitor()

    def reinitialize():
        """Reinitialize the monitoring."""
        logger.info("🔄 Reinitializing Tesla bot...")
        monitor.cleanup()
        monitor.__init__()
        monitor.run()

    # Register keyboard shortcuts
    keyboard.add_hotkey('ctrl+r', reinitialize)

    try:
        monitor.run()
    except KeyboardInterrupt:
        logger.info("Program terminated by user")
    finally:
        monitor.cleanup()

if __name__ == "__main__":
    main()