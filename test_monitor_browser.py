#!/usr/bin/env python3
"""
Test Monitor Bot with Browser Fallback
Quick test of the updated monitor bot functionality
"""

import sys
import time

def test_monitor_browser():
    """Test the monitor bot with browser fallback"""
    print("🧪 Testing Monitor Bot with Browser Fallback")
    print("=" * 50)
    
    try:
        # Import the monitor bot components
        from tesla_bot_monitor import TeslaMonitorBot
        from config import get_config
        from utils import check_vehicle_availability
        
        print("✅ All imports successful")
        
        # Create bot instance
        bot = TeslaMonitorBot()
        print("✅ Bot instance created")
        
        # Test configuration
        config = get_config()
        print(f"✅ Configuration loaded - Target: {config['target_vehicle']['variant_keywords']}")
        
        # Test inventory fetching (this should fall back to browser)
        print("\n🔍 Testing inventory fetch (will try API first, then browser)...")
        inventory_data = bot.fetch_inventory()
        
        if inventory_data:
            print("✅ Inventory data retrieved")
            print(f"   Mode: {'Browser' if inventory_data.get('browser_mode') else 'API'}")
            print(f"   Results: {len(inventory_data.get('results', []))}")
            
            # Test vehicle checking
            vehicles = check_vehicle_availability(inventory_data)
            print(f"   Matching vehicles: {len(vehicles)}")
            
            if vehicles:
                print("🚗 Found potential vehicles:")
                for i, vehicle in enumerate(vehicles, 1):
                    print(f"   {i}. {vehicle.get('trim', 'Unknown')} - {vehicle.get('price', 0):,} TL")
                    if vehicle.get('browser_mode'):
                        print(f"      Browser mode detection: {vehicle.get('found_indicators', [])}")
            else:
                print("   No matching vehicles found (normal if no inventory)")
                
        else:
            print("❌ Failed to retrieve inventory data")
            return False
        
        print("\n✅ Monitor bot browser fallback test completed successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def main():
    """Main test function"""
    print("🚗 Tesla Monitor Bot - Browser Fallback Test")
    print("Testing the updated monitor bot with browser automation fallback")
    print()
    
    success = test_monitor_browser()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 MONITOR BOT BROWSER FALLBACK TEST PASSED!")
        print("💡 The updated monitor bot should now work around Tesla's blocking")
        print("💡 It will use browser automation when API calls are blocked")
        print("\n📋 What this means:")
        print("   • API calls will be tried first (fast but likely blocked)")
        print("   • Browser automation will be used as fallback (slower but works)")
        print("   • You'll get notifications when potential vehicles are detected")
        print("   • Manual verification will be required for browser detections")
        print("\n🚀 Ready to use:")
        print("   python tesla_bot_monitor.py")
    else:
        print("❌ MONITOR BOT BROWSER FALLBACK TEST FAILED")
        print("💡 Check Selenium installation and ChromeDriver")
        print("💡 Fall back to manual monitoring if needed")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)
