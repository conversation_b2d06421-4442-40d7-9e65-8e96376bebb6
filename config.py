"""
Tesla Bot Configuration
Configuration settings for Tesla inventory monitoring bots
"""

import os
from typing import Dict, List, Optional

# Tesla URLs and Endpoints
TESLA_BASE_URL = "https://www.tesla.com"
TESLA_INVENTORY_URL = "https://www.tesla.com/tr_TR/inventory/new/my"
TESLA_API_BASE = "https://www.tesla.com/coinorder/api/v4"
TESLA_INVENTORY_API = f"{TESLA_API_BASE}/inventory-results"
TESLA_ORDER_BASE = "https://www.tesla.com/tr_TR/modely/design"

# Target Vehicle Configuration
TARGET_VEHICLE = {
    "model": "my",  # Model Y
    "condition": "new",
    "market": "TR",  # Turkey
    "locale": "tr_TR",
    "currency": "TRY",
    "target_price": 1900000,  # 1.9M TL for Juniper
    "variant_keywords": ["juniper", "model y"],
    "max_price_tolerance": 50000  # Allow 50k TL tolerance
}

# Request Headers
DEFAULT_HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Accept-Language": "tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7",
    "Accept-Encoding": "gzip, deflate, br, zstd",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
    "Sec-Fetch-Dest": "document",
    "Sec-Fetch-Mode": "navigate",
    "Sec-Fetch-Site": "none",
    "Sec-Fetch-User": "?1",
    "Cache-Control": "max-age=0"
}

# API specific headers (when making API calls)
API_HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "tr-TR,tr;q=0.9,en;q=0.8",
    "Accept-Encoding": "gzip, deflate, br",
    "Connection": "keep-alive",
    "Referer": TESLA_INVENTORY_URL,
    "Origin": TESLA_BASE_URL,
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-origin",
    "X-Requested-With": "XMLHttpRequest"
}

# Monitoring Configuration
MONITORING_CONFIG = {
    "check_interval": 10,  # seconds between checks (increased to be more respectful)
    "max_retries": 3,
    "retry_delay": 5,  # seconds (increased delay)
    "timeout": 30,  # request timeout
    "log_level": "INFO",
    "save_responses": True,  # Save API responses for debugging
    "notification_sound": True,
    "use_fallback_scraping": True,  # Use HTML scraping if API fails
    "session_refresh_interval": 300  # Refresh session every 5 minutes
}

# Bot Behavior Settings
BOT_SETTINGS = {
    "auto_refresh_page": True,
    "keyboard_shortcuts": {
        "reinitialize": "ctrl+r",
        "pause_monitoring": "ctrl+p",
        "quit": "ctrl+q"
    },
    "confirmation_prompts": {
        "use_short_prompts": True,  # y/n instead of YES/NO
        "skip_payment_confirmation": True,  # Skip after CAPTCHA
        "keyboard_confirmation": True  # Use keyboard instead of countdown
    }
}

# Order Form Configuration (for full automation bot)
ORDER_FORM_CONFIG = {
    "personal_info": {
        "first_name": "",  # To be filled by user
        "last_name": "",
        "email": "",
        "phone": "",
        "address": "",
        "city": "",
        "postal_code": "",
        "country": "Turkey"
    },
    "payment_info": {
        "preorder_amount": 175000,  # 175,000 TL preorder payment
        "payment_method": "credit_card",  # or "bank_transfer"
        "auto_fill_payment": False  # Require manual payment entry for security
    },
    "delivery_preferences": {
        "delivery_method": "pickup",  # or "delivery"
        "preferred_location": "",  # Tesla center location
        "delivery_notes": ""
    }
}

# Logging Configuration
LOGGING_CONFIG = {
    "log_file": "tesla_bot.log",
    "max_log_size": 10 * 1024 * 1024,  # 10MB
    "backup_count": 5,
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "date_format": "%Y-%m-%d %H:%M:%S"
}

# File Paths
PATHS = {
    "logs": "logs",
    "data": "data",
    "screenshots": "screenshots",
    "responses": "responses"
}

# Create directories if they don't exist
for path in PATHS.values():
    os.makedirs(path, exist_ok=True)

# API Request Parameters
API_PARAMS = {
    "query": {
        "model": "my",
        "condition": "new",
        "options": {},
        "arrangeby": "Price",
        "order": "asc",
        "market": "TR",
        "language": "tr",
        "super_region": "north_america",
        "lng": 28.9784,  # Istanbul coordinates
        "lat": 41.0082,
        "zip": "34000",  # Istanbul postal code
        "range": 200
    }
}

# Success/Error Messages
MESSAGES = {
    "vehicle_found": "🚗 JUNIPER MODEL Y FOUND! Price: {price} TL",
    "opening_browser": "🌐 Opening order page in browser...",
    "auto_ordering": "🤖 Starting automatic order process...",
    "monitoring_started": "👀 Monitoring Tesla inventory for Juniper Model Y...",
    "monitoring_paused": "⏸️ Monitoring paused. Press Ctrl+R to resume.",
    "no_vehicles": "❌ No matching vehicles found. Continuing to monitor...",
    "api_error": "⚠️ API Error: {error}",
    "connection_error": "🔌 Connection error. Retrying in {delay} seconds...",
    "captcha_required": "🔐 CAPTCHA detected. Please solve manually.",
    "order_success": "✅ Order placed successfully!",
    "order_failed": "❌ Order failed: {error}"
}

def get_config() -> Dict:
    """Get complete configuration dictionary"""
    return {
        "target_vehicle": TARGET_VEHICLE,
        "headers": DEFAULT_HEADERS,
        "api_headers": API_HEADERS,
        "monitoring": MONITORING_CONFIG,
        "bot_settings": BOT_SETTINGS,
        "order_form": ORDER_FORM_CONFIG,
        "logging": LOGGING_CONFIG,
        "paths": PATHS,
        "api_params": API_PARAMS,
        "messages": MESSAGES,
        "urls": {
            "base": TESLA_BASE_URL,
            "inventory": TESLA_INVENTORY_URL,
            "api": TESLA_INVENTORY_API,
            "order": TESLA_ORDER_BASE
        }
    }

def validate_config() -> bool:
    """Validate configuration settings"""
    required_fields = [
        "TARGET_VEHICLE",
        "DEFAULT_HEADERS", 
        "MONITORING_CONFIG"
    ]
    
    for field in required_fields:
        if field not in globals():
            print(f"Missing required configuration: {field}")
            return False
    
    return True

if __name__ == "__main__":
    if validate_config():
        print("✅ Configuration validated successfully")
        config = get_config()
        print(f"Target: {config['target_vehicle']['variant_keywords']} - {config['target_vehicle']['target_price']} TL")
    else:
        print("❌ Configuration validation failed")
