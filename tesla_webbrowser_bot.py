#!/usr/bin/env python3
"""
Tesla WebBrowser Bot - Hybrid Automation
Uses webbrowser.open() (which works) + automation for monitoring and notifications
"""

import time
import sys
import webbrowser
import keyboard
import threading
from datetime import datetime
from typing import Optional

def print_status(message: str, level: str = "INFO"):
    """Print status message with timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    symbols = {
        "INFO": "ℹ️",
        "SUCCESS": "✅", 
        "WARNING": "⚠️",
        "ERROR": "❌",
        "FOUND": "🚗",
        "REFRESH": "🔄"
    }
    symbol = symbols.get(level, "ℹ️")
    print(f"[{timestamp}] {symbol} {message}")

def play_notification():
    """Play notification sound"""
    try:
        import winsound
        # Play multiple beeps for attention
        for _ in range(3):
            winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
            time.sleep(0.3)
    except:
        print("\a\a\a")

class TeslaWebBrowserBot:
    """Tesla bot using webbrowser.open() approach"""
    
    def __init__(self):
        self.is_running = False
        self.is_paused = False
        self.check_count = 0
        self.refresh_interval = 30  # seconds
        self.browser_tabs_opened = False
        
        # Setup keyboard shortcuts
        self.setup_keyboard_shortcuts()
        
        print("🚗 Tesla WebBrowser Bot - Hybrid Automation")
        print("=" * 60)
        print("💡 This bot uses webbrowser.open() which Tesla allows!")
        print("💡 It opens real browser tabs and reminds you to check them.")
        print("=" * 60)
        print("🎯 Target: Juniper Model Y (~1.9M TL)")
        print("📱 Keyboard Shortcuts:")
        print("   Ctrl+R: Force refresh reminder")
        print("   Ctrl+P: Pause/Resume")
        print("   Ctrl+Q: Quit")
        print("=" * 60)
    
    def setup_keyboard_shortcuts(self):
        """Setup keyboard shortcuts"""
        try:
            keyboard.add_hotkey('ctrl+r', self.force_refresh)
            keyboard.add_hotkey('ctrl+p', self.toggle_pause)
            keyboard.add_hotkey('ctrl+q', self.quit_bot)
        except Exception as e:
            print_status(f"Keyboard shortcuts setup failed: {e}", "WARNING")
    
    def force_refresh(self):
        """Force refresh reminder (Ctrl+R)"""
        if self.is_running:
            print_status("🔄 MANUAL REFRESH TRIGGERED!", "REFRESH")
            self.send_refresh_reminder()
    
    def toggle_pause(self):
        """Toggle pause state (Ctrl+P)"""
        if self.is_running:
            self.is_paused = not self.is_paused
            status = "paused" if self.is_paused else "resumed"
            print_status(f"Monitoring {status}", "INFO")
    
    def quit_bot(self):
        """Quit the bot (Ctrl+Q)"""
        print_status("Shutting down bot...", "INFO")
        self.is_running = False
        sys.exit(0)
    
    def open_tesla_tabs(self):
        """Open Tesla inventory tabs in browser"""
        if self.browser_tabs_opened:
            return True
        
        print_status("Opening Tesla inventory tabs in your browser...", "INFO")
        
        urls = [
            "https://www.tesla.com/tr_TR/inventory/new/my",
            "https://www.tesla.com/tr_TR/modely/design"
        ]
        
        try:
            for i, url in enumerate(urls, 1):
                print_status(f"Opening tab {i}: Tesla inventory page", "INFO")
                webbrowser.open(url)
                time.sleep(2)  # Small delay between opens
            
            self.browser_tabs_opened = True
            print_status("✅ Browser tabs opened successfully!", "SUCCESS")
            print()
            print("🌐 BROWSER TABS OPENED:")
            print("   Tab 1: Tesla Model Y Inventory")
            print("   Tab 2: Tesla Model Y Design Studio")
            print()
            print("💡 WHAT TO DO:")
            print("   • Keep these tabs open")
            print("   • The bot will remind you to refresh them")
            print("   • Look for Juniper Model Y around 1.9M TL")
            print("   • Act quickly when you find vehicles!")
            print()
            
            return True
            
        except Exception as e:
            print_status(f"Failed to open browser tabs: {e}", "ERROR")
            return False
    
    def send_refresh_reminder(self):
        """Send refresh reminder to user"""
        self.check_count += 1
        
        # Play notification sound
        play_notification()
        
        print()
        print("🔔" + "=" * 58 + "🔔")
        print(f"           REFRESH REMINDER #{self.check_count}")
        print("🔔" + "=" * 58 + "🔔")
        print()
        print("🔄 TIME TO CHECK TESLA INVENTORY!")
        print()
        print("📋 QUICK CHECKLIST:")
        print("   1. Switch to Tesla browser tabs")
        print("   2. Press F5 to refresh both tabs")
        print("   3. Look for Juniper Model Y")
        print("   4. Check price around 1.9M TL")
        print("   5. Click 'Order' immediately if found!")
        print()
        print("🎯 WHAT TO LOOK FOR:")
        print("   • Model Y vehicles")
        print("   • Price: ~1,900,000 TL")
        print("   • 'Juniper' in description")
        print("   • 'Available' or 'Mevcut' status")
        print("   • 'Order Now' or 'Sipariş Ver' button")
        print()
        print("⚡ QUICK ACTION STEPS:")
        print("   1. Click vehicle → 2. Click Order → 3. Fill info → 4. Pay 175k TL")
        print()
        print("🔔" + "=" * 58 + "🔔")
        print()
    
    def ask_user_status(self) -> Optional[str]:
        """Ask user about their findings"""
        try:
            print("❓ Did you find any Juniper Model Y vehicles?")
            print("   y = Yes, found vehicles!")
            print("   n = No vehicles found")
            print("   s = Skip this check")
            print("   q = Quit monitoring")
            
            response = input("Your response (y/n/s/q): ").strip().lower()
            return response
        except KeyboardInterrupt:
            return 'q'
    
    def handle_user_response(self, response: str) -> bool:
        """Handle user's response about findings"""
        if response == 'y':
            print_status("🎉 GREAT! Vehicle found!", "FOUND")
            play_notification()
            
            print()
            print("🚗" + "=" * 58 + "🚗")
            print("           VEHICLE FOUND - ACTION TIME!")
            print("🚗" + "=" * 58 + "🚗")
            print()
            print("⚡ IMMEDIATE ACTIONS:")
            print("   1. Click on the vehicle immediately")
            print("   2. Click 'Order Now' or 'Sipariş Ver'")
            print("   3. Fill personal information quickly")
            print("   4. Complete 175,000 TL preorder payment")
            print("   5. Solve any CAPTCHAs")
            print()
            print("💡 TIPS:")
            print("   • Have personal info ready to copy-paste")
            print("   • Use saved payment method")
            print("   • Don't hesitate - act fast!")
            print()
            
            continue_monitoring = input("Continue monitoring for more vehicles? (y/n): ").strip().lower()
            return continue_monitoring in ['y', 'yes']
            
        elif response == 'n':
            print_status("No vehicles found - continuing to monitor", "INFO")
            return True
            
        elif response == 's':
            print_status("Check skipped - continuing to monitor", "INFO")
            return True
            
        elif response == 'q':
            print_status("Monitoring stopped by user", "INFO")
            return False
            
        else:
            print_status("Invalid response - continuing to monitor", "WARNING")
            return True
    
    def monitoring_loop(self):
        """Main monitoring loop"""
        print_status("Starting Tesla inventory monitoring...", "SUCCESS")
        
        # Open browser tabs first
        if not self.open_tesla_tabs():
            print_status("Failed to open browser tabs", "ERROR")
            return False
        
        # Initial reminder
        time.sleep(3)
        self.send_refresh_reminder()
        
        self.is_running = True
        
        try:
            while self.is_running:
                if self.is_paused:
                    time.sleep(1)
                    continue
                
                # Ask user about their findings
                response = self.ask_user_status()
                
                if not self.handle_user_response(response):
                    break
                
                # Wait for next check
                print_status(f"Next reminder in {self.refresh_interval} seconds...", "INFO")
                
                # Countdown with ability to interrupt
                for i in range(self.refresh_interval, 0, -1):
                    if not self.is_running or self.is_paused:
                        break
                    
                    if i % 10 == 0 or i <= 5:  # Show countdown at intervals
                        print(f"\r⏳ Next reminder in {i:2d} seconds... (Ctrl+R to refresh now)", end="", flush=True)
                    
                    time.sleep(1)
                
                print("\r" + " " * 60 + "\r", end="")  # Clear countdown line
                
                if self.is_running and not self.is_paused:
                    self.send_refresh_reminder()
                    
        except KeyboardInterrupt:
            print_status("Monitoring stopped by user", "INFO")
        except Exception as e:
            print_status(f"Monitoring error: {e}", "ERROR")
        finally:
            self.is_running = False
        
        return True
    
    def run(self):
        """Run the bot"""
        try:
            print("🚀 Ready to start Tesla monitoring?")
            print("💡 This will open Tesla pages in your browser and remind you to check them.")
            print()
            
            response = input("Start monitoring? (y/n): ").strip().lower()
            if response not in ['y', 'yes']:
                print_status("Monitoring cancelled", "INFO")
                return
            
            self.monitoring_loop()
            
        except KeyboardInterrupt:
            print_status("Bot stopped by user", "INFO")
        except Exception as e:
            print_status(f"Bot error: {e}", "ERROR")
        finally:
            print("\n🙏 Thank you for using Tesla WebBrowser Bot!")
            print("💡 Keep those browser tabs open for manual checking!")

def main():
    """Main entry point"""
    try:
        bot = TeslaWebBrowserBot()
        bot.run()
    except KeyboardInterrupt:
        print("\n🛑 Bot stopped by user")
    except Exception as e:
        print(f"❌ Failed to start bot: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
