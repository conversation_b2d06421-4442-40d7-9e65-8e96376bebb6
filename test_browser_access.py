#!/usr/bin/env python3
"""
Test Browser Access to Tesla
Test if browser automation can bypass Tesla's blocking
"""

import time
import sys

def test_browser_access():
    """Test browser access to Tesla inventory"""
    print("🧪 Testing Browser Access to Tesla")
    print("=" * 50)
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.common.by import By
        
        print("✅ Selenium imported successfully")
        
        # Setup browser options
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        print("🔧 Setting up Chrome browser...")
        driver = webdriver.Chrome(options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("✅ Browser setup successful")
        
        # Test 1: Access main Tesla site
        print("\n1. Testing main Tesla website...")
        driver.get("https://www.tesla.com")
        time.sleep(3)
        
        if "tesla" in driver.title.lower():
            print("   ✅ Main Tesla site accessible")
        else:
            print(f"   ⚠️  Unexpected title: {driver.title}")
        
        # Test 2: Access Turkey inventory page
        print("\n2. Testing Turkey inventory page...")
        url = "https://www.tesla.com/tr_TR/inventory/new/my"
        driver.get(url)
        time.sleep(5)  # Wait for page to load
        
        current_url = driver.current_url
        print(f"   Current URL: {current_url}")
        
        if "inventory" in current_url.lower():
            print("   ✅ Turkey inventory page accessible")
            
            # Check page content
            page_source = driver.page_source.lower()
            
            content_checks = {
                "model y": "model y" in page_source,
                "tesla": "tesla" in page_source,
                "inventory": "inventory" in page_source,
                "price/fiyat": any(word in page_source for word in ["price", "fiyat", "₺", "tl"]),
                "turkish": any(word in page_source for word in ["türkiye", "turkey", "tr_tr"])
            }
            
            print("   📄 Page content analysis:")
            for check, result in content_checks.items():
                status = "✅" if result else "❌"
                print(f"      {status} {check}")
            
            # Look for specific Juniper/pricing indicators
            juniper_indicators = ["juniper", "1.900.000", "1,900,000", "1900000"]
            found_indicators = [indicator for indicator in juniper_indicators if indicator in page_source]
            
            if found_indicators:
                print(f"   🎯 Found potential indicators: {found_indicators}")
            else:
                print("   ℹ️  No specific Juniper indicators found (normal if no inventory)")
                
        else:
            print(f"   ❌ Redirected or blocked: {current_url}")
        
        # Test 3: Check for blocking indicators
        print("\n3. Checking for blocking indicators...")
        page_source = driver.page_source.lower()
        
        blocking_indicators = [
            "access denied",
            "forbidden",
            "blocked",
            "bot",
            "automation",
            "captcha"
        ]
        
        found_blocking = [indicator for indicator in blocking_indicators if indicator in page_source]
        
        if found_blocking:
            print(f"   ⚠️  Potential blocking detected: {found_blocking}")
        else:
            print("   ✅ No obvious blocking indicators")
        
        # Test 4: Try to interact with page
        print("\n4. Testing page interaction...")
        try:
            # Look for common elements
            elements_to_find = [
                "button",
                "a[href*='inventory']",
                "div[class*='vehicle']",
                "div[class*='inventory']"
            ]
            
            found_elements = 0
            for selector in elements_to_find:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        found_elements += len(elements)
                        print(f"   ✅ Found {len(elements)} elements matching '{selector}'")
                except:
                    pass
            
            if found_elements > 0:
                print(f"   ✅ Page is interactive ({found_elements} total elements found)")
            else:
                print("   ⚠️  Limited page interaction detected")
                
        except Exception as e:
            print(f"   ❌ Interaction test failed: {e}")
        
        driver.quit()
        print("\n🏁 Browser test completed")
        
        return True
        
    except ImportError:
        print("❌ Selenium not installed")
        print("   Install with: pip install selenium")
        print("   Also download ChromeDriver from: https://chromedriver.chromium.org/")
        return False
        
    except Exception as e:
        print(f"❌ Browser test failed: {e}")
        if 'driver' in locals():
            try:
                driver.quit()
            except:
                pass
        return False

def main():
    """Main test function"""
    print("🚗 Tesla Browser Access Test")
    print("Testing if browser automation can bypass Tesla's anti-bot protection")
    print()
    
    success = test_browser_access()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 BROWSER ACCESS TEST PASSED!")
        print("💡 Browser automation should work for Tesla monitoring")
        print("💡 You can now use the updated monitor bot with browser fallback")
    else:
        print("❌ BROWSER ACCESS TEST FAILED")
        print("💡 Try installing Selenium and ChromeDriver")
        print("💡 Fall back to manual monitoring approach")
    
    print("\n📋 Next Steps:")
    if success:
        print("   1. Run: python tesla_bot_monitor.py")
        print("   2. The bot will use browser automation when API is blocked")
        print("   3. Manual verification will be required for detections")
    else:
        print("   1. Install: pip install selenium")
        print("   2. Download ChromeDriver")
        print("   3. Or use: python tesla_manual_guide.py")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)
