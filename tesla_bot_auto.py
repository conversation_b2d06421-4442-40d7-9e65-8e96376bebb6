#!/usr/bin/env python3
"""
Tesla Bot - Full Automation Version
Continuously monitors Tesla inventory and automatically fills order when Juniper Model Y becomes available
"""

import time
import sys
import json
import threading
import keyboard
from datetime import datetime
from typing import List, Dict, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from config import get_config
from utils import (
    setup_logging, check_vehicle_availability,
    format_vehicle_info, play_notification_sound, get_user_confirmation,
    print_status, handle_keyboard_interrupt, log_vehicle_found, create_session
)

class TeslaAutoBot:
    """Tesla inventory monitoring bot with automatic order filling"""
    
    def __init__(self):
        self.config = get_config()
        self.logger = setup_logging("tesla_auto_bot")
        self.session = create_session()
        self.driver = None
        self.is_running = False
        self.is_paused = False
        self.found_vehicles = []
        self.order_form_data = {}
        
        # Setup keyboard shortcuts
        self.setup_keyboard_shortcuts()
        
        print("🤖 Tesla Auto Bot - Full Automation Version")
        print("=" * 50)
        print(f"Target: {self.config['target_vehicle']['variant_keywords']} Model Y")
        print(f"Max Price: {self.config['target_vehicle']['target_price']:,} TL")
        print(f"Preorder Amount: {self.config['order_form']['payment_info']['preorder_amount']:,} TL")
        print("=" * 50)
        print("⚠️  WARNING: This bot will automatically fill order forms!")
        print("⚠️  Make sure your personal information is configured correctly!")
        print("=" * 50)
        print("Keyboard Shortcuts:")
        print("  Ctrl+R: Reinitialize/Resume monitoring")
        print("  Ctrl+P: Pause/Resume monitoring") 
        print("  Ctrl+Q: Quit")
        print("=" * 50)
    
    def setup_keyboard_shortcuts(self):
        """Setup keyboard shortcuts for bot control"""
        try:
            keyboard.add_hotkey('ctrl+r', self.reinitialize_monitoring)
            keyboard.add_hotkey('ctrl+p', self.toggle_pause)
            keyboard.add_hotkey('ctrl+q', self.quit_bot)
        except Exception as e:
            self.logger.warning(f"Could not setup keyboard shortcuts: {e}")
    
    def reinitialize_monitoring(self):
        """Reinitialize monitoring (Ctrl+R)"""
        if not self.is_running:
            print_status("Reinitializing monitoring...", "INFO")
            self.start_monitoring()
        else:
            print_status("Restarting vehicle search...", "INFO")
            self.is_paused = False
    
    def toggle_pause(self):
        """Toggle pause state (Ctrl+P)"""
        if self.is_running:
            self.is_paused = not self.is_paused
            status = "paused" if self.is_paused else "resumed"
            print_status(f"Monitoring {status}", "INFO")
    
    def quit_bot(self):
        """Quit the bot (Ctrl+Q)"""
        print_status("Shutting down bot...", "INFO")
        self.is_running = False
        if self.driver:
            self.driver.quit()
        sys.exit(0)
    
    def setup_webdriver(self) -> bool:
        """Setup Chrome WebDriver for automation"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            print_status("WebDriver initialized successfully", "SUCCESS")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to setup WebDriver: {e}")
            print_status(f"WebDriver setup failed: {e}", "ERROR")
            return False
    
    def validate_order_form_data(self) -> bool:
        """Validate that order form data is properly configured"""
        personal_info = self.config["order_form"]["personal_info"]
        
        required_fields = ["first_name", "last_name", "email", "phone"]
        missing_fields = []
        
        for field in required_fields:
            if not personal_info.get(field):
                missing_fields.append(field)
        
        if missing_fields:
            print_status(f"Missing required personal information: {', '.join(missing_fields)}", "ERROR")
            print("Please update config.py with your personal information before using auto mode.")
            return False
        
        return True
    
    def fetch_inventory(self) -> Optional[Dict]:
        """Fetch current inventory using successful HTML parsing method"""
        try:
            from utils import make_inventory_request

            response = make_inventory_request(timeout=self.config["monitoring"]["timeout"])

            if response:
                results_count = len(response.get('results', []))
                self.logger.debug(f"Inventory response received: {results_count} vehicles")
                print_status(f"Found {results_count} vehicles on inventory page", "INFO")
                return response
            else:
                self.logger.warning("No response from Tesla inventory page")
                return None

        except Exception as e:
            self.logger.error(f"Error fetching inventory: {e}")
            return None
    
    def check_for_vehicles(self) -> List[Dict]:
        """Check for target vehicles in inventory"""
        inventory_data = self.fetch_inventory()
        
        if not inventory_data:
            return []
        
        vehicles = check_vehicle_availability(inventory_data)
        
        if vehicles:
            self.logger.info(f"Found {len(vehicles)} matching vehicle(s)")
            log_vehicle_found(self.logger, vehicles)
        
        return vehicles
    
    def wait_for_element(self, by: By, value: str, timeout: int = 10):
        """Wait for element to be present and return it"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((by, value))
            )
            return element
        except TimeoutException:
            self.logger.warning(f"Element not found: {value}")
            return None
    
    def fill_personal_information(self) -> bool:
        """Fill personal information in the order form"""
        try:
            personal_info = self.config["order_form"]["personal_info"]
            
            # Common field mappings (may need adjustment based on actual form)
            field_mappings = {
                "firstName": personal_info["first_name"],
                "lastName": personal_info["last_name"],
                "email": personal_info["email"],
                "phone": personal_info["phone"],
                "address": personal_info["address"],
                "city": personal_info["city"],
                "postalCode": personal_info["postal_code"]
            }
            
            for field_name, value in field_mappings.items():
                if value:
                    try:
                        # Try multiple possible selectors
                        selectors = [
                            f"input[name='{field_name}']",
                            f"input[id='{field_name}']",
                            f"input[data-testid='{field_name}']",
                            f"#{field_name}",
                            f".{field_name}"
                        ]
                        
                        element = None
                        for selector in selectors:
                            try:
                                element = self.driver.find_element(By.CSS_SELECTOR, selector)
                                break
                            except NoSuchElementException:
                                continue
                        
                        if element:
                            element.clear()
                            element.send_keys(value)
                            print_status(f"Filled {field_name}: {value}", "INFO")
                        else:
                            print_status(f"Could not find field: {field_name}", "WARNING")
                            
                    except Exception as e:
                        self.logger.warning(f"Error filling {field_name}: {e}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error filling personal information: {e}")
            return False
    
    def handle_captcha(self) -> bool:
        """Handle CAPTCHA if present"""
        try:
            # Look for common CAPTCHA indicators
            captcha_selectors = [
                ".captcha",
                ".recaptcha",
                "#captcha",
                "[data-testid*='captcha']",
                "iframe[src*='recaptcha']"
            ]
            
            for selector in captcha_selectors:
                try:
                    captcha_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if captcha_element.is_displayed():
                        print_status("CAPTCHA detected! Please solve manually.", "WARNING")
                        play_notification_sound()
                        
                        # Wait for user to solve CAPTCHA
                        input("Press Enter after solving the CAPTCHA...")
                        return True
                except NoSuchElementException:
                    continue
            
            return True  # No CAPTCHA found
            
        except Exception as e:
            self.logger.error(f"Error handling CAPTCHA: {e}")
            return False
    
    def place_order(self, vehicle_data: Dict) -> bool:
        """Attempt to place order for the vehicle"""
        try:
            # Navigate to vehicle order page
            vin = vehicle_data.get("VIN", "")
            if vin:
                url = f"{self.config['urls']['base']}/tr_TR/new/{vin}"
            else:
                url = self.config["urls"]["order"]
            
            print_status(f"Navigating to order page: {url}", "INFO")
            self.driver.get(url)
            
            # Wait for page to load
            time.sleep(3)
            
            # Look for order/reserve button
            order_button_selectors = [
                "button[data-testid='reserve-button']",
                "button[data-testid='order-button']",
                ".reserve-button",
                ".order-button",
                "button:contains('Rezerve Et')",
                "button:contains('Sipariş Ver')",
                "input[type='submit'][value*='Rezerve']",
                "input[type='submit'][value*='Sipariş']"
            ]
            
            order_button = None
            for selector in order_button_selectors:
                try:
                    order_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if order_button.is_displayed():
                        break
                except NoSuchElementException:
                    continue
            
            if not order_button:
                print_status("Could not find order button", "ERROR")
                return False
            
            # Click order button
            print_status("Clicking order button...", "INFO")
            order_button.click()
            time.sleep(2)
            
            # Fill personal information
            print_status("Filling personal information...", "INFO")
            if not self.fill_personal_information():
                print_status("Failed to fill personal information", "ERROR")
                return False
            
            # Handle CAPTCHA if present
            if not self.handle_captcha():
                print_status("CAPTCHA handling failed", "ERROR")
                return False
            
            # Look for final submit button
            submit_selectors = [
                "button[type='submit']",
                "input[type='submit']",
                "button[data-testid='submit']",
                ".submit-button",
                "button:contains('Onayla')",
                "button:contains('Tamamla')"
            ]
            
            submit_button = None
            for selector in submit_selectors:
                try:
                    submit_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if submit_button.is_displayed():
                        break
                except NoSuchElementException:
                    continue
            
            if submit_button:
                # Final confirmation before submitting
                vehicle_info = format_vehicle_info(vehicle_data)
                if get_user_confirmation(f"Submit order for: {vehicle_info}?"):
                    print_status("Submitting order...", "SUCCESS")
                    submit_button.click()
                    
                    # Wait for confirmation
                    time.sleep(5)
                    
                    # Check for success indicators
                    success_indicators = [
                        "success",
                        "confirmation",
                        "teşekkür",
                        "başarılı"
                    ]
                    
                    page_text = self.driver.page_source.lower()
                    if any(indicator in page_text for indicator in success_indicators):
                        print_status("Order submitted successfully!", "SUCCESS")
                        return True
                    else:
                        print_status("Order submission status unclear", "WARNING")
                        return False
                else:
                    print_status("Order submission cancelled by user", "INFO")
                    return False
            else:
                print_status("Could not find submit button", "ERROR")
                return False
                
        except Exception as e:
            self.logger.error(f"Error placing order: {e}")
            print_status(f"Order placement failed: {e}", "ERROR")
            return False
    
    def handle_vehicle_found(self, vehicles: List[Dict]):
        """Handle when target vehicles are found"""
        print_status(f"JUNIPER MODEL Y FOUND! ({len(vehicles)} vehicle(s))", "FOUND")
        
        # Play notification sound
        if self.config["monitoring"]["notification_sound"]:
            play_notification_sound()
        
        # Display vehicle information
        for i, vehicle in enumerate(vehicles, 1):
            print(f"\n🚗 Vehicle {i}:")
            print(f"   {format_vehicle_info(vehicle)}")
        
        # Select vehicle to order
        selected_vehicle = None
        if len(vehicles) == 1:
            selected_vehicle = vehicles[0]
        else:
            print(f"\nFound {len(vehicles)} vehicles. Which one would you like to order?")
            for i, vehicle in enumerate(vehicles, 1):
                print(f"{i}. {format_vehicle_info(vehicle)}")
            
            while True:
                try:
                    choice = input(f"Enter choice (1-{len(vehicles)}): ").strip()
                    if choice.isdigit() and 1 <= int(choice) <= len(vehicles):
                        selected_vehicle = vehicles[int(choice) - 1]
                        break
                    else:
                        print("Invalid choice. Please try again.")
                except (ValueError, KeyboardInterrupt):
                    print("Invalid input or cancelled.")
                    return
        
        # Attempt to place order
        if selected_vehicle:
            vehicle_info = format_vehicle_info(selected_vehicle)
            if get_user_confirmation(f"Automatically place order for: {vehicle_info}?"):
                print_status("Starting automatic order process...", "INFO")
                
                if self.place_order(selected_vehicle):
                    print_status("Order process completed successfully!", "SUCCESS")
                    self.is_running = False  # Stop monitoring after successful order
                else:
                    print_status("Order process failed. Continuing monitoring...", "WARNING")
            else:
                print_status("Automatic ordering cancelled by user.", "INFO")
    
    def monitoring_loop(self):
        """Main monitoring loop"""
        check_count = 0
        
        while self.is_running:
            try:
                if self.is_paused:
                    time.sleep(1)
                    continue
                
                check_count += 1
                print_status(f"Check #{check_count} - Scanning inventory...", "INFO")
                
                # Check for vehicles
                vehicles = self.check_for_vehicles()
                
                if vehicles:
                    self.found_vehicles.extend(vehicles)
                    self.handle_vehicle_found(vehicles)
                    
                    if not self.is_running:  # User chose to stop or order completed
                        break
                else:
                    print_status("No matching vehicles found. Continuing to monitor...", "INFO")
                
                # Wait for next check
                interval = self.config["monitoring"]["check_interval"]
                for i in range(interval):
                    if not self.is_running or self.is_paused:
                        break
                    time.sleep(1)
                
            except KeyboardInterrupt:
                handle_keyboard_interrupt()
                break
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                print_status(f"Error occurred: {e}", "ERROR")
                time.sleep(5)  # Wait before retrying
    
    def start_monitoring(self):
        """Start the monitoring process"""
        if self.is_running:
            print_status("Monitoring is already running!", "WARNING")
            return
        
        # Validate configuration
        if not self.validate_order_form_data():
            return
        
        # Setup WebDriver
        if not self.setup_webdriver():
            return
        
        self.is_running = True
        self.is_paused = False
        
        print_status("Starting Tesla inventory monitoring with auto-order...", "SUCCESS")
        print_status(f"Monitoring for: {', '.join(self.config['target_vehicle']['variant_keywords'])}", "INFO")
        print_status(f"Target price: {self.config['target_vehicle']['target_price']:,} TL", "INFO")
        
        try:
            self.monitoring_loop()
        except Exception as e:
            self.logger.error(f"Fatal error in monitoring: {e}")
            print_status(f"Fatal error: {e}", "ERROR")
        finally:
            self.is_running = False
            if self.driver:
                self.driver.quit()
            print_status("Monitoring stopped.", "INFO")
    
    def run(self):
        """Run the bot"""
        try:
            # Initial confirmation
            print("⚠️  This bot will automatically attempt to place orders!")
            print("⚠️  Make sure your configuration is correct!")
            
            if get_user_confirmation("Start automatic monitoring and ordering?"):
                self.start_monitoring()
            else:
                print_status("Monitoring cancelled by user.", "INFO")
                
        except KeyboardInterrupt:
            handle_keyboard_interrupt()
        except Exception as e:
            self.logger.error(f"Unexpected error: {e}")
            print_status(f"Unexpected error: {e}", "ERROR")
        finally:
            if self.driver:
                self.driver.quit()
            print("\n🙏 Thank you for using Tesla Auto Bot!")
            if self.found_vehicles:
                print(f"📊 Session Summary: Found {len(self.found_vehicles)} vehicle(s)")

def main():
    """Main entry point"""
    try:
        bot = TeslaAutoBot()
        bot.run()
    except Exception as e:
        print(f"Failed to start bot: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
