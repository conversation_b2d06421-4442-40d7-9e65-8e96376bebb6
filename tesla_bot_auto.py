#!/usr/bin/env python3
"""
Tesla Model Y Juniper Auto-Order Bot - Version 2
Monitors Tesla Turkey inventory and automatically fills order forms.

This bot continuously monitors Tesla Model Y Juniper inventory in Turkey
and automatically fills order forms when vehicles become available.
"""

import time
import json
import logging
import webbrowser
from datetime import datetime
from typing import Dict, List, Optional
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select
from selenium.common.exceptions import TimeoutException, WebDriverException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import keyboard
import winsound
import random

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tesla_auto.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TeslaAutoOrderBot:
    """Tesla Model Y Juniper automatic ordering bot."""

    def __init__(self, config_file: str = "order_config.json"):
        self.base_url = "https://www.tesla.com"
        self.inventory_url = "https://www.tesla.com/tr_TR/inventory/new/my"

        # Load user configuration
        self.config = self.load_config(config_file)

        # Target specifications for Model Y Juniper
        self.target_specs = {
            "model": "my",  # Model Y
            "condition": "new",
            "market": "TR",
            "currency": "TRY",
            "max_price": self.config.get("max_price", 2000000),  # 2M TL max
            "target_price": self.config.get("target_price", 1900000),  # 1.9M TL target
            "min_price": self.config.get("min_price", 1800000),  # 1.8M TL min
        }

        # Monitoring state
        self.is_monitoring = False
        self.found_vehicles = []
        self.last_check_time = None
        self.previous_vehicle_count = 0

        # Browser driver for automation
        self.driver = None
        self.wait = None

    def load_config(self, config_file: str) -> Dict:
        """Load user configuration from JSON file."""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                logger.info(f"Configuration loaded from {config_file}")
                return config
        except FileNotFoundError:
            logger.warning(f"Config file {config_file} not found, creating default...")
            default_config = self.create_default_config()
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
            return default_config
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            return self.create_default_config()

    def create_default_config(self) -> Dict:
        """Create default configuration."""
        return {
            "max_price": 2000000,
            "target_price": 1900000,
            "personal_info": {
                "first_name": "",
                "last_name": "",
                "email": "",
                "phone": "",
                "address": "",
                "city": "",
                "postal_code": "",
                "id_number": ""
            },
            "payment_info": {
                "preorder_amount": 175000,  # 175,000 TL preorder payment
                "payment_method": "credit_card"  # or "bank_transfer"
            },
            "preferences": {
                "auto_confirm": False,  # Require manual confirmation before payment
                "max_attempts": 3,
                "retry_delay": 5
            }
        }

    def setup_driver(self) -> bool:
        """Setup Chrome WebDriver with stealth options."""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")

            # Use webdriver-manager to automatically handle ChromeDriver
            try:
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            except Exception as e:
                logger.warning(f"WebDriver manager failed: {e}, trying direct Chrome...")
                # Fallback to direct Chrome without service
                self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 20)

            logger.info("Chrome WebDriver initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to setup WebDriver: {e}")
            return False

    def get_inventory_data(self) -> List[Dict]:
        """Scrape current inventory data from Tesla website using browser."""
        if not self.driver:
            logger.error("WebDriver not initialized")
            return []

        try:
            logger.info("Loading Tesla inventory page...")
            self.driver.get(self.inventory_url)

            # Wait for page to load
            time.sleep(3 + random.uniform(1, 3))  # Random delay to appear human

            # Wait for inventory results to load
            try:
                self.wait.until(EC.presence_of_element_located((By.CLASS_NAME, "result")))
            except TimeoutException:
                # Try alternative selectors
                try:
                    self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "[data-testid='inventory-card']")))
                except TimeoutException:
                    logger.warning("No inventory results found or page didn't load properly")
                    return []

            # Scroll to load all results
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)

            # Extract vehicle data from the page
            vehicles = []

            # Try multiple selectors for vehicle cards
            vehicle_cards = []
            selectors = [
                "[data-testid='inventory-card']",
                ".result",
                ".inventory-card",
                ".vehicle-card"
            ]

            for selector in selectors:
                vehicle_cards = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if vehicle_cards:
                    logger.info(f"Found {len(vehicle_cards)} vehicle cards using selector: {selector}")
                    break

            if not vehicle_cards:
                logger.warning("No vehicle cards found on page")
                return []

            for card in vehicle_cards:
                try:
                    vehicle_data = self.extract_vehicle_data(card)
                    if vehicle_data:
                        vehicles.append(vehicle_data)
                except Exception as e:
                    logger.warning(f"Error extracting vehicle data: {e}")
                    continue

            logger.info(f"Successfully extracted {len(vehicles)} vehicles from page")
            return vehicles

        except Exception as e:
            logger.error(f"Failed to get inventory data: {e}")
            return []

    def extract_vehicle_data(self, card_element) -> Optional[Dict]:
        """Extract vehicle data from a vehicle card element."""
        try:
            vehicle_data = {}

            # Extract price
            price_selectors = [
                "[data-testid='price']",
                ".price",
                ".vehicle-price",
                ".result-price"
            ]

            price_text = None
            for selector in price_selectors:
                try:
                    price_element = card_element.find_element(By.CSS_SELECTOR, selector)
                    price_text = price_element.text
                    break
                except NoSuchElementException:
                    continue

            if price_text:
                # Extract numeric price from text like "1.900.000 TL" or "₺1,900,000"
                price_numbers = re.findall(r'[\d.,]+', price_text.replace('.', '').replace(',', ''))
                if price_numbers:
                    try:
                        price = int(price_numbers[0])
                        vehicle_data['Price'] = price
                    except ValueError:
                        pass

            # Extract VIN or ID
            vin_selectors = [
                "[data-testid='vin']",
                ".vin",
                ".vehicle-id"
            ]

            for selector in vin_selectors:
                try:
                    vin_element = card_element.find_element(By.CSS_SELECTOR, selector)
                    vehicle_data['VIN'] = vin_element.text
                    break
                except NoSuchElementException:
                    continue

            # Extract year from text content
            card_text = card_element.text
            year_match = re.search(r'20(2[4-9]|[3-9]\d)', card_text)  # 2024 and later
            if year_match:
                vehicle_data['Year'] = int(year_match.group())

            # Extract model info
            if 'model y' in card_text.lower() or 'modely' in card_text.lower():
                vehicle_data['Model'] = 'MY'

            # Check if it's new (default assumption for inventory page)
            vehicle_data['TitleStatus'] = 'new'

            # Extract trim/variant info
            vehicle_data['TrimName'] = self.extract_trim_info(card_text)

            # Store the card element for later use in ordering
            vehicle_data['_card_element'] = card_element

            return vehicle_data if vehicle_data.get('Price') else None

        except Exception as e:
            logger.warning(f"Error extracting vehicle data: {e}")
            return None

    def extract_trim_info(self, text: str) -> str:
        """Extract trim information from vehicle text."""
        text_lower = text.lower()

        # Look for common Model Y trim indicators
        if 'performance' in text_lower:
            return 'Performance'
        elif 'long range' in text_lower:
            return 'Long Range'
        elif 'rwd' in text_lower or 'rear' in text_lower:
            return 'RWD'
        elif 'awd' in text_lower or 'all wheel' in text_lower:
            return 'AWD'
        else:
            return 'Standard'

    def filter_juniper_vehicles(self, vehicles: List[Dict]) -> List[Dict]:
        """Filter vehicles to find Model Y Juniper variants."""
        juniper_vehicles = []

        for vehicle in vehicles:
            try:
                # Check if it's a Model Y
                if vehicle.get('Model') != 'MY':
                    continue

                # Check price range (around 1.9M TL for Juniper)
                price = vehicle.get('Price', 0)
                min_price = self.target_specs['min_price']
                max_price = self.target_specs['max_price']

                if not (min_price <= price <= max_price):
                    continue

                # Check if it's new
                if vehicle.get('TitleStatus') != 'new':
                    continue

                # Juniper is typically the latest Model Y variant
                # Check for recent model year (2024+ for Juniper)
                model_year = vehicle.get('Year', 0)
                if model_year >= 2024:  # Juniper is 2024+
                    juniper_vehicles.append(vehicle)
                    logger.info(f"Found potential Juniper: {vehicle.get('VIN', 'Unknown VIN')} - {price:,} TL - {vehicle.get('TrimName', 'Unknown trim')}")

            except Exception as e:
                logger.warning(f"Error processing vehicle: {e}")
                continue

        return juniper_vehicles

    def fill_personal_info(self) -> bool:
        """Fill personal information form."""
        try:
            personal_info = self.config.get("personal_info", {})

            # Wait for form to load
            time.sleep(3)

            # Fill first name
            if personal_info.get("first_name"):
                first_name_field = self.wait.until(
                    EC.presence_of_element_located((By.NAME, "firstName"))
                )
                first_name_field.clear()
                first_name_field.send_keys(personal_info["first_name"])
                logger.info("Filled first name")

            # Fill last name
            if personal_info.get("last_name"):
                last_name_field = self.driver.find_element(By.NAME, "lastName")
                last_name_field.clear()
                last_name_field.send_keys(personal_info["last_name"])
                logger.info("Filled last name")

            # Fill email
            if personal_info.get("email"):
                email_field = self.driver.find_element(By.NAME, "email")
                email_field.clear()
                email_field.send_keys(personal_info["email"])
                logger.info("Filled email")

            # Fill phone
            if personal_info.get("phone"):
                phone_field = self.driver.find_element(By.NAME, "phoneNumber")
                phone_field.clear()
                phone_field.send_keys(personal_info["phone"])
                logger.info("Filled phone")

            # Fill address
            if personal_info.get("address"):
                address_field = self.driver.find_element(By.NAME, "address")
                address_field.clear()
                address_field.send_keys(personal_info["address"])
                logger.info("Filled address")

            # Fill city
            if personal_info.get("city"):
                city_field = self.driver.find_element(By.NAME, "city")
                city_field.clear()
                city_field.send_keys(personal_info["city"])
                logger.info("Filled city")

            # Fill postal code
            if personal_info.get("postal_code"):
                postal_field = self.driver.find_element(By.NAME, "postalCode")
                postal_field.clear()
                postal_field.send_keys(personal_info["postal_code"])
                logger.info("Filled postal code")

            # Fill ID number (Turkish TC Kimlik No)
            if personal_info.get("id_number"):
                id_field = self.driver.find_element(By.NAME, "nationalId")
                id_field.clear()
                id_field.send_keys(personal_info["id_number"])
                logger.info("Filled ID number")

            return True

        except Exception as e:
            logger.error(f"Failed to fill personal info: {e}")
            return False

    def handle_captcha(self) -> bool:
        """Handle CAPTCHA if present."""
        try:
            # Look for CAPTCHA elements
            captcha_elements = self.driver.find_elements(By.CLASS_NAME, "captcha")
            if not captcha_elements:
                captcha_elements = self.driver.find_elements(By.ID, "captcha")

            if captcha_elements:
                logger.info("🔒 CAPTCHA detected! Please solve it manually...")
                winsound.Beep(800, 2000)  # Alert sound

                # Wait for user to solve CAPTCHA
                input("Press Enter after solving the CAPTCHA...")
                logger.info("CAPTCHA solved, continuing...")
                return True

            return True  # No CAPTCHA found

        except Exception as e:
            logger.warning(f"Error checking for CAPTCHA: {e}")
            return True  # Continue anyway

    def process_order(self, vehicle: Dict) -> bool:
        """Process the complete order for a vehicle."""
        try:
            vin = vehicle.get('VIN')
            price = vehicle.get('Price', 0)

            if not vin:
                logger.error("No VIN found for vehicle")
                return False

            logger.info(f"🚗 Processing order for VIN: {vin} - Price: {price:,} TL")

            # Try to click order button directly from the card
            card_element = vehicle.get('_card_element')
            if card_element:
                try:
                    # Look for order button in the card
                    order_buttons = card_element.find_elements(By.TAG_NAME, "button")
                    for button in order_buttons:
                        if any(word in button.text.lower() for word in ['order', 'sipariş', 'satın', 'buy']):
                            logger.info(f"Clicking order button: {button.text}")
                            button.click()
                            time.sleep(5)  # Wait for page to load
                            break
                    else:
                        # If no button found, click the card itself
                        logger.info("Clicking vehicle card to navigate to order page")
                        card_element.click()
                        time.sleep(5)  # Wait for page to load

                except Exception as e:
                    logger.warning(f"Failed to click card element: {e}")
                    # Fallback to URL navigation
                    order_url = f"https://www.tesla.com/tr_TR/my/design#{vin}"
                    self.driver.get(order_url)
                    time.sleep(5)
            else:
                # Navigate to order page directly
                order_url = f"https://www.tesla.com/tr_TR/my/design#{vin}"
                self.driver.get(order_url)
                time.sleep(5)

            # Look for "Order Now" or "Reserve" button on the order page
            try:
                order_button = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Sipariş') or contains(text(), 'Reserve') or contains(text(), 'Order')]"))
                )
                order_button.click()
                logger.info("Clicked order button on order page")
                time.sleep(3)
            except TimeoutException:
                logger.warning("Order button not found on order page, trying alternative selectors...")
                # Try alternative button selectors
                buttons = self.driver.find_elements(By.TAG_NAME, "button")
                for button in buttons:
                    if any(word in button.text.lower() for word in ['sipariş', 'order', 'reserve', 'satın']):
                        button.click()
                        logger.info(f"Clicked button: {button.text}")
                        time.sleep(3)
                        break

            # Fill personal information
            if not self.fill_personal_info():
                logger.error("Failed to fill personal information")
                return False

            # Handle CAPTCHA if present
            if not self.handle_captcha():
                logger.error("CAPTCHA handling failed")
                return False

            # Continue to payment
            if not self.process_payment():
                logger.error("Payment processing failed")
                return False

            logger.info("✅ Order processed successfully!")
            return True

        except Exception as e:
            logger.error(f"Failed to process order: {e}")
            return False

    def process_payment(self) -> bool:
        """Process payment step."""
        try:
            payment_info = self.config.get("payment_info", {})
            preferences = self.config.get("preferences", {})

            # Look for payment section
            time.sleep(3)

            # Check if manual confirmation is required
            if not preferences.get("auto_confirm", False):
                preorder_amount = payment_info.get("preorder_amount", 175000)
                logger.info(f"💳 Ready to process payment of {preorder_amount:,} TL")

                # Play notification sound
                winsound.Beep(1200, 1500)

                # Ask for confirmation
                confirm = input(f"\n🔔 Confirm payment of {preorder_amount:,} TL? (y/n): ").lower().strip()
                if confirm != 'y':
                    logger.info("Payment cancelled by user")
                    return False

            # Look for payment method selection
            payment_method = payment_info.get("payment_method", "credit_card")

            if payment_method == "credit_card":
                # Select credit card payment
                try:
                    credit_card_radio = self.driver.find_element(By.XPATH, "//input[@type='radio' and contains(@value, 'credit')]")
                    credit_card_radio.click()
                    logger.info("Selected credit card payment")
                except NoSuchElementException:
                    logger.warning("Credit card option not found")

            # Look for final submit button
            try:
                submit_button = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Confirm') or contains(text(), 'Submit') or contains(text(), 'Onayla')]"))
                )

                # Final confirmation
                logger.info("🎯 Ready to submit order!")
                winsound.Beep(1500, 2000)

                if not preferences.get("auto_confirm", False):
                    final_confirm = input("\n🚨 FINAL CONFIRMATION: Submit order? (y/n): ").lower().strip()
                    if final_confirm != 'y':
                        logger.info("Order submission cancelled by user")
                        return False

                submit_button.click()
                logger.info("✅ Order submitted!")

                # Wait for confirmation page
                time.sleep(5)
                return True

            except TimeoutException:
                logger.error("Submit button not found")
                return False

        except Exception as e:
            logger.error(f"Payment processing failed: {e}")
            return False

    def monitor_and_order(self):
        """Main monitoring and ordering loop."""
        logger.info("🚀 Starting Tesla Model Y Juniper auto-ordering...")
        logger.info("Press Ctrl+R to reinitialize, Ctrl+C to stop")

        self.is_monitoring = True
        check_interval = 30  # Check every 30 seconds

        while self.is_monitoring:
            try:
                self.last_check_time = datetime.now()
                logger.info(f"🔍 Checking inventory at {self.last_check_time.strftime('%H:%M:%S')}")

                # Fetch inventory data using browser
                vehicles = self.get_inventory_data()
                if not vehicles:
                    logger.warning("Failed to fetch inventory data, retrying...")
                    time.sleep(10)
                    continue

                # Filter for Juniper vehicles
                juniper_vehicles = self.filter_juniper_vehicles(vehicles)

                if juniper_vehicles:
                    # Vehicles found! Process orders
                    logger.info(f"🚗 FOUND {len(juniper_vehicles)} Model Y Juniper vehicle(s)!")
                    winsound.Beep(1000, 1000)  # Alert sound

                    for i, vehicle in enumerate(juniper_vehicles, 1):
                        vin = vehicle.get('VIN', 'Unknown')
                        price = vehicle.get('Price', 0)

                        logger.info(f"\n🎯 Vehicle {i}: VIN {vin} - {price:,} TL")

                        # Ask user if they want to order this vehicle
                        if len(juniper_vehicles) > 1:
                            order_choice = input(f"Order this vehicle? (y/n/s for skip): ").lower().strip()
                            if order_choice == 'n':
                                logger.info("Stopping auto-ordering")
                                self.is_monitoring = False
                                break
                            elif order_choice == 's':
                                logger.info("Skipping this vehicle")
                                continue

                        # Process the order
                        success = self.process_order(vehicle)

                        if success:
                            logger.info("✅ Order completed successfully!")
                            self.found_vehicles.append(vehicle)

                            # Ask if user wants to continue monitoring
                            continue_monitoring = input("\n🔄 Continue monitoring for more vehicles? (y/n): ").lower().strip()
                            if continue_monitoring != 'y':
                                self.is_monitoring = False
                                break
                        else:
                            logger.error("❌ Order failed, continuing monitoring...")

                        # Small delay between orders
                        time.sleep(5)

                else:
                    logger.info("No Model Y Juniper vehicles available")

                # Wait before next check
                if self.is_monitoring:
                    logger.info(f"⏰ Next check in {check_interval} seconds...")
                    time.sleep(check_interval)

            except KeyboardInterrupt:
                logger.info("Monitoring stopped by user")
                self.is_monitoring = False
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(10)  # Wait before retrying

    def cleanup(self):
        """Clean up resources."""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("WebDriver closed")
            except:
                pass

    def run(self):
        """Main entry point."""
        try:
            # Validate configuration
            personal_info = self.config.get("personal_info", {})
            required_fields = ["first_name", "last_name", "email", "phone"]

            missing_fields = [field for field in required_fields if not personal_info.get(field)]
            if missing_fields:
                logger.error(f"Missing required personal information: {missing_fields}")
                logger.error("Please update order_config.json with your information")
                return

            # Setup WebDriver
            if not self.setup_driver():
                logger.error("WebDriver setup failed")
                return

            # Start monitoring and ordering
            self.monitor_and_order()

        except Exception as e:
            logger.error(f"Fatal error: {e}")
        finally:
            self.cleanup()

def main():
    """Main function with keyboard shortcuts."""
    bot = TeslaAutoOrderBot()

    def reinitialize():
        """Reinitialize the bot."""
        logger.info("🔄 Reinitializing Tesla auto-order bot...")
        bot.cleanup()
        bot.__init__()
        bot.run()

    # Register keyboard shortcuts
    keyboard.add_hotkey('ctrl+r', reinitialize)

    try:
        bot.run()
    except KeyboardInterrupt:
        logger.info("Program terminated by user")
    finally:
        bot.cleanup()

if __name__ == "__main__":
    main()