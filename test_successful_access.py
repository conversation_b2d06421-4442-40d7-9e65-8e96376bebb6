#!/usr/bin/env python3
"""
Test successful access methods
Find what works and what doesn't for Tesla access
"""

import requests
import time
import json
from urllib.parse import urljoin

def test_different_approaches():
    """Test different approaches to access Tesla"""
    
    print("🧪 Testing Different Tesla Access Methods")
    print("=" * 60)
    
    # Test 1: Minimal headers (like a basic browser)
    print("\n1. Testing with minimal browser headers...")
    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
        response = requests.get("https://www.tesla.com/tr_TR/inventory/new/my", 
                              headers=headers, timeout=15)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ SUCCESS with minimal headers!")
            return test_inventory_parsing(response.text)
        else:
            print(f"   ❌ Failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: Session-based approach
    print("\n2. Testing with session...")
    try:
        session = requests.Session()
        session.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        })
        
        # First visit main Tesla site
        session.get("https://www.tesla.com", timeout=10)
        time.sleep(2)
        
        # Then visit inventory
        response = session.get("https://www.tesla.com/tr_TR/inventory/new/my", timeout=15)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ SUCCESS with session!")
            return test_inventory_parsing(response.text)
        else:
            print(f"   ❌ Failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Different user agents
    print("\n3. Testing different user agents...")
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ]
    
    for i, ua in enumerate(user_agents):
        try:
            headers = {"User-Agent": ua}
            response = requests.get("https://www.tesla.com/tr_TR/inventory/new/my", 
                                  headers=headers, timeout=10)
            print(f"   UA {i+1}: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ SUCCESS with User Agent {i+1}!")
                return test_inventory_parsing(response.text)
                
        except Exception as e:
            print(f"   UA {i+1}: Error - {e}")
    
    # Test 4: Try with delays
    print("\n4. Testing with delays...")
    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
        }
        
        print("   Waiting 5 seconds...")
        time.sleep(5)
        
        response = requests.get("https://www.tesla.com/tr_TR/inventory/new/my", 
                              headers=headers, timeout=20)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ SUCCESS with delays!")
            return test_inventory_parsing(response.text)
        else:
            print(f"   ❌ Failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    return False

def test_inventory_parsing(html_content):
    """Test parsing inventory from HTML content"""
    print("\n📄 Testing inventory parsing...")
    
    try:
        content_lower = html_content.lower()
        
        # Check content length
        print(f"   Page length: {len(html_content)} characters")
        
        # Look for key indicators
        indicators = {
            "model y": "model y" in content_lower,
            "inventory": "inventory" in content_lower,
            "tesla": "tesla" in content_lower,
            "price": any(x in content_lower for x in ["₺", "tl", "price", "fiyat"]),
            "available": any(x in content_lower for x in ["available", "mevcut"]),
            "juniper": "juniper" in content_lower
        }
        
        print("   Content indicators:")
        for key, found in indicators.items():
            status = "✅" if found else "❌"
            print(f"     {status} {key}")
        
        # Look for JavaScript data
        import re
        
        # Try to find Tesla data object
        tesla_data_pattern = r'window\.tesla\s*=\s*({.*?});'
        tesla_match = re.search(tesla_data_pattern, html_content, re.DOTALL)
        
        if tesla_match:
            print("   ✅ Found window.tesla data object")
            try:
                # Try to extract some data (carefully)
                tesla_str = tesla_match.group(1)
                if "inventory" in tesla_str.lower():
                    print("   ✅ Tesla object contains inventory data")
                if "results" in tesla_str.lower():
                    print("   ✅ Tesla object contains results")
            except:
                print("   ⚠️  Tesla object found but couldn't parse")
        else:
            print("   ❌ No window.tesla data found")
        
        # Look for vehicle data patterns
        vehicle_patterns = [
            r'"VIN":\s*"[^"]*"',
            r'"Price":\s*\d+',
            r'"Model":\s*"[^"]*"',
            r'vehicle.*data',
            r'inventory.*results'
        ]
        
        vehicle_data_found = False
        for pattern in vehicle_patterns:
            if re.search(pattern, html_content, re.IGNORECASE):
                print(f"   ✅ Found vehicle data pattern: {pattern}")
                vehicle_data_found = True
        
        if not vehicle_data_found:
            print("   ❌ No vehicle data patterns found")
        
        return len(html_content) > 10000 and indicators["tesla"]
        
    except Exception as e:
        print(f"   ❌ Parsing error: {e}")
        return False

def test_api_with_successful_method():
    """If we find a successful method, test it with API endpoints"""
    print("\n🔌 Testing API endpoints with successful method...")
    
    # Use the most promising approach
    session = requests.Session()
    session.headers.update({
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "Accept-Language": "tr-TR,tr;q=0.9,en;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "Connection": "keep-alive"
    })
    
    try:
        # First establish session with main page
        print("   Establishing session...")
        session.get("https://www.tesla.com/tr_TR/inventory/new/my", timeout=15)
        time.sleep(2)
        
        # Now try API with proper headers
        api_headers = {
            "Accept": "application/json, text/plain, */*",
            "Referer": "https://www.tesla.com/tr_TR/inventory/new/my",
            "X-Requested-With": "XMLHttpRequest"
        }
        session.headers.update(api_headers)
        
        api_url = "https://www.tesla.com/coinorder/api/v4/inventory-results"
        params = {
            "model": "my",
            "condition": "new",
            "market": "TR",
            "language": "tr"
        }
        
        response = session.get(api_url, params=params, timeout=15)
        print(f"   API Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ API SUCCESS!")
            try:
                data = response.json()
                print(f"   📊 API returned: {type(data)}")
                if isinstance(data, dict) and "results" in data:
                    results = data["results"]
                    print(f"   🚗 Found {len(results)} vehicles in API")
                    return True
            except:
                print("   ⚠️  API response not JSON")
        else:
            print(f"   ❌ API failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ API test error: {e}")
    
    return False

def main():
    """Main test function"""
    print("🔍 Finding successful Tesla access method...")
    print("This will help us fix the blocked bots.")
    print()
    
    # Test different approaches
    html_success = test_different_approaches()
    
    if html_success:
        print("\n🎉 Found working HTML access method!")
        
        # Test if we can also access API
        api_success = test_api_with_successful_method()
        
        if api_success:
            print("\n🚀 EXCELLENT! Both HTML and API access work!")
            print("💡 We can now fix the blocked bots using this method.")
        else:
            print("\n📄 HTML access works, but API is still blocked.")
            print("💡 We can create HTML-parsing based bots.")
    else:
        print("\n❌ All methods failed. Tesla blocking is very strict.")
        print("💡 Browser automation or manual monitoring are the only options.")
    
    print("\n" + "="*60)
    print("🏁 Test completed. Check results above.")

if __name__ == "__main__":
    main()
