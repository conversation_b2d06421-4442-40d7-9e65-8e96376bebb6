#!/usr/bin/env python3
"""
Debug Browser Content - Show What Bo<PERSON> Sees
Shows exactly what content the bot sees when using browser automation
"""

import time
import sys
import os
from datetime import datetime

def save_page_content(content: str, filename: str):
    """Save page content to file for inspection"""
    os.makedirs("debug_output", exist_ok=True)
    filepath = os.path.join("debug_output", filename)
    
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"💾 Saved content to: {filepath}")

def analyze_page_content(page_source: str):
    """Analyze and show what the bot sees in the page"""
    print("\n🔍 PAGE CONTENT ANALYSIS")
    print("=" * 60)
    
    content_lower = page_source.lower()
    
    # 1. Basic page info
    print(f"📄 Page size: {len(page_source):,} characters")
    print(f"📄 Page size (lowercase): {len(content_lower):,} characters")
    
    # 2. Tesla-specific indicators
    print("\n🚗 TESLA INDICATORS:")
    tesla_indicators = {
        "tesla": "tesla" in content_lower,
        "model y": "model y" in content_lower,
        "inventory": "inventory" in content_lower,
        "vehicle": "vehicle" in content_lower,
        "araç": "araç" in content_lower,  # Turkish for vehicle
        "otomobil": "otomobil" in content_lower,  # Turkish for car
    }
    
    for indicator, found in tesla_indicators.items():
        status = "✅" if found else "❌"
        print(f"   {status} {indicator}")
    
    # 3. Juniper-specific indicators
    print("\n🎯 JUNIPER INDICATORS:")
    juniper_indicators = {
        "juniper": "juniper" in content_lower,
        "model y juniper": "model y juniper" in content_lower,
        "juniper model y": "juniper model y" in content_lower,
    }
    
    for indicator, found in juniper_indicators.items():
        status = "✅" if found else "❌"
        print(f"   {status} {indicator}")
    
    # 4. Price indicators
    print("\n💰 PRICE INDICATORS:")
    price_indicators = {
        "₺": "₺" in content_lower,
        "tl": "tl" in content_lower,
        "lira": "lira" in content_lower,
        "price": "price" in content_lower,
        "fiyat": "fiyat" in content_lower,  # Turkish for price
        "1.900.000": "1.900.000" in content_lower,
        "1,900,000": "1,900,000" in content_lower,
        "1900000": "1900000" in content_lower,
        "1.9": "1.9" in content_lower,
    }
    
    for indicator, found in price_indicators.items():
        status = "✅" if found else "❌"
        print(f"   {status} {indicator}")
    
    # 5. Availability indicators
    print("\n📋 AVAILABILITY INDICATORS:")
    availability_indicators = {
        "available": "available" in content_lower,
        "mevcut": "mevcut" in content_lower,  # Turkish for available
        "stok": "stok" in content_lower,  # Turkish for stock
        "sipariş": "sipariş" in content_lower,  # Turkish for order
        "rezerve": "rezerve" in content_lower,  # Turkish for reserve
        "order": "order" in content_lower,
        "reserve": "reserve" in content_lower,
        "buy": "buy" in content_lower,
        "satın al": "satın al" in content_lower,  # Turkish for buy
    }
    
    for indicator, found in availability_indicators.items():
        status = "✅" if found else "❌"
        print(f"   {status} {indicator}")
    
    # 6. Negative indicators (no inventory)
    print("\n❌ NEGATIVE INDICATORS:")
    negative_indicators = {
        "no results": "no results" in content_lower,
        "no vehicles": "no vehicles" in content_lower,
        "sonuç bulunamadı": "sonuç bulunamadı" in content_lower,
        "araç bulunamadı": "araç bulunamadı" in content_lower,
        "stok yok": "stok yok" in content_lower,
        "mevcut değil": "mevcut değil" in content_lower,
    }
    
    for indicator, found in negative_indicators.items():
        status = "⚠️" if found else "✅"
        print(f"   {status} {indicator}")
    
    # 7. Show sample content snippets
    print("\n📝 SAMPLE CONTENT SNIPPETS:")
    
    # Find lines containing key terms
    lines = page_source.split('\n')
    interesting_lines = []
    
    search_terms = ['model y', 'juniper', '1.9', 'price', 'fiyat', 'available', 'mevcut', 'order', 'sipariş']
    
    for line in lines:
        line_lower = line.lower().strip()
        if any(term in line_lower for term in search_terms) and len(line_lower) > 10:
            interesting_lines.append(line.strip())
    
    if interesting_lines:
        print("   Found interesting content lines:")
        for i, line in enumerate(interesting_lines[:10]):  # Show first 10
            print(f"   {i+1:2d}. {line[:100]}{'...' if len(line) > 100 else ''}")
        
        if len(interesting_lines) > 10:
            print(f"   ... and {len(interesting_lines) - 10} more lines")
    else:
        print("   No particularly interesting content lines found")
    
    # 8. JavaScript/Dynamic content indicators
    print("\n⚡ DYNAMIC CONTENT INDICATORS:")
    dynamic_indicators = {
        "javascript": "javascript" in content_lower,
        "react": "react" in content_lower,
        "vue": "vue" in content_lower,
        "angular": "angular" in content_lower,
        "window.tesla": "window.tesla" in content_lower,
        "tesla_config": "tesla_config" in content_lower,
        "inventory_results": "inventory_results" in content_lower,
    }
    
    for indicator, found in dynamic_indicators.items():
        status = "✅" if found else "❌"
        print(f"   {status} {indicator}")

def debug_browser_content():
    """Debug what the browser automation sees"""
    print("🔍 Debug Browser Content - What Bot Sees")
    print("=" * 60)
    print("This will show you exactly what content the bot analyzes")
    print("when using browser automation to check Tesla inventory.")
    print()
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        print("✅ Selenium imported successfully")
        
        # Setup browser options (same as bot uses)
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        print("🔧 Setting up Chrome browser...")
        driver = webdriver.Chrome(options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("✅ Browser setup successful")
        
        # Navigate to Tesla inventory page
        url = "https://www.tesla.com/tr_TR/inventory/new/my"
        print(f"\n🌐 Navigating to: {url}")
        
        driver.get(url)
        print("⏳ Waiting for page to load...")
        time.sleep(5)  # Wait for page load
        
        # Get current URL and title
        current_url = driver.current_url
        page_title = driver.title
        
        print(f"📍 Current URL: {current_url}")
        print(f"📄 Page title: {page_title}")
        
        # Check if we got redirected or blocked
        if "inventory" not in current_url.lower():
            print("⚠️  WARNING: Page was redirected - possible blocking")
        else:
            print("✅ Successfully loaded inventory page")
        
        # Get page source (what the bot analyzes)
        print("\n📥 Retrieving page source...")
        page_source = driver.page_source
        
        # Save full page source
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        save_page_content(page_source, f"tesla_page_source_{timestamp}.html")
        
        # Analyze the content
        analyze_page_content(page_source)
        
        # Show what the bot's detection logic would find
        print("\n🤖 BOT DETECTION SIMULATION:")
        print("=" * 60)
        
        # Simulate the bot's detection logic
        content_lower = page_source.lower()
        
        # Bot looks for these indicators
        juniper_indicators = ["juniper", "model y", "1.900.000", "1,900,000", "1900000", "1.9"]
        found_indicators = [indicator for indicator in juniper_indicators if indicator in content_lower]
        
        print(f"🔍 Bot searched for: {juniper_indicators}")
        print(f"✅ Bot found: {found_indicators}")
        
        if found_indicators:
            print("🎯 BOT DECISION: POTENTIAL VEHICLE DETECTED!")
            print("   The bot would alert you and open browser for manual verification")
        else:
            print("❌ BOT DECISION: NO VEHICLES DETECTED")
            print("   The bot would continue monitoring")
        
        # Show a sample of the actual HTML structure
        print("\n🏗️  HTML STRUCTURE SAMPLE:")
        print("=" * 60)
        
        # Find and show some key HTML elements
        try:
            from selenium.webdriver.common.by import By
            
            # Look for common elements
            elements_to_check = [
                ("title", "title"),
                ("h1", "h1"),
                ("h2", "h2"),
                ("[data-testid]", "elements with data-testid"),
                (".vehicle", "elements with 'vehicle' class"),
                (".inventory", "elements with 'inventory' class"),
                ("button", "buttons"),
            ]
            
            for selector, description in elements_to_check:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        print(f"✅ Found {len(elements)} {description}")
                        # Show first few elements
                        for i, elem in enumerate(elements[:3]):
                            try:
                                text = elem.text.strip()[:100]
                                if text:
                                    print(f"   {i+1}. {text}{'...' if len(elem.text) > 100 else ''}")
                            except:
                                print(f"   {i+1}. [Element text not accessible]")
                    else:
                        print(f"❌ No {description} found")
                except Exception as e:
                    print(f"⚠️  Error checking {description}: {e}")
        
        except Exception as e:
            print(f"⚠️  Error analyzing HTML structure: {e}")
        
        driver.quit()
        print("\n🏁 Browser debug completed")
        
        return True
        
    except ImportError:
        print("❌ Selenium not installed")
        print("   Install with: pip install selenium")
        return False
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        if 'driver' in locals():
            try:
                driver.quit()
            except:
                pass
        return False

def main():
    """Main debug function"""
    print("🚗 Tesla Bot - Browser Content Debug")
    print("This tool shows you exactly what the bot sees when using browser automation")
    print()
    
    success = debug_browser_content()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 BROWSER CONTENT DEBUG COMPLETED!")
        print("\n📋 What you learned:")
        print("   • Exact content the bot analyzes")
        print("   • Which indicators are found/missing")
        print("   • How bot makes detection decisions")
        print("   • HTML structure of Tesla page")
        print("\n📁 Files created:")
        print("   • debug_output/tesla_page_source_*.html - Full page source")
        print("\n💡 Use this information to:")
        print("   • Understand why bot detects/doesn't detect vehicles")
        print("   • Improve detection logic if needed")
        print("   • Manually verify what bot sees")
    else:
        print("❌ BROWSER CONTENT DEBUG FAILED")
        print("💡 Check Selenium installation and try again")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Debug interrupted by user")
    except Exception as e:
        print(f"\n❌ Debug failed: {e}")
        sys.exit(1)
