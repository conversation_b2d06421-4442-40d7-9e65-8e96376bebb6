@echo off
title Tesla Bot Launcher
color 0A

echo.
echo ========================================
echo    Tesla Bot for Turkey - Launcher
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    echo.
    pause
    exit /b 1
)

REM Check if required files exist
if not exist "tesla_bot_launcher.py" (
    echo ERROR: tesla_bot_launcher.py not found
    echo Make sure you're running this from the correct directory
    echo.
    pause
    exit /b 1
)

REM Try to install requirements if they don't exist
if exist "requirements.txt" (
    echo Installing/updating requirements...
    pip install -r requirements.txt
    echo.
)

REM Run the launcher
echo Starting Tesla Bot Launcher...
echo.
python tesla_bot_launcher.py

REM Keep window open if there was an error
if errorlevel 1 (
    echo.
    echo Bot exited with an error. Check the messages above.
    pause
)

echo.
echo Thank you for using Tesla Bot!
pause
