# Tesla Bot Requirements
# Core dependencies for Tesla inventory monitoring bots

# HTTP requests and web scraping
requests>=2.31.0
urllib3>=2.0.0

# Web automation (for auto bot)
selenium>=4.15.0

# Keyboard input handling
keyboard>=0.13.5

# JSON and data handling
jsonschema>=4.19.0

# Windows sound notifications
# Note: winsound is built-in on Windows

# Optional: Enhanced logging and monitoring
colorama>=0.4.6
tqdm>=4.66.0

# Development and testing (optional)
pytest>=7.4.0
pytest-cov>=4.1.0

# Note: You'll also need to install ChromeDriver for the auto bot
# Download from: https://chromedriver.chromium.org/
# Or use: pip install webdriver-manager (alternative approach)
