# Tesla Model Y Juniper Turkey Bots

Two specialized bots for monitoring and ordering Tesla Model Y Juniper vehicles in Turkey's fast-moving inventory market.

## 🚗 Bot Versions

### Version 1: Monitor & Open (`tesla_bot_monitor.py`)
- **Purpose**: Monitors inventory and opens order pages for manual completion
- **Use Case**: When you want to fill order information yourself
- **Features**:
  - Continuous inventory monitoring
  - Automatic browser opening when vehicles are found
  - Sound notifications
  - Keyboard shortcuts (Ctrl+R to reinitialize)
  - Multiple vehicle selection

### Version 2: Full Automation (`tesla_bot_auto.py`)
- **Purpose**: Monitors inventory and automatically fills order forms
- **Use Case**: Complete automation with manual payment confirmation
- **Features**:
  - Automatic form filling
  - CAPTCHA handling (manual solving)
  - Payment confirmation prompts
  - Configuration-based personal info
  - 175,000 TL preorder payment processing

## 🎯 Target Specifications

- **Model**: Tesla Model Y Juniper
- **Market**: Turkey (TR)
- **Price Range**: 1.8M - 2.1M TL (targeting 1.9M TL)
- **Condition**: New vehicles only
- **Model Year**: 2024+ (Juniper variants)

## 🛠️ Setup Instructions

### 1. Prerequisites

- Python 3.8 or higher
- Google Chrome browser
- ChromeDriver (will be installed automatically)
- Windows 10/11 (optimized for Windows)

### 2. Installation

```bash
# Clone or download the files to your desired directory
cd TeslaBot

# Install required packages
pip install -r requirements.txt

# Install ChromeDriver (if not already installed)
# Download from: https://chromedriver.chromium.org/
# Or use: pip install webdriver-manager
```

### 3. Configuration (For Auto Bot Only)

Edit `order_config.json` with your personal information:

```json
{
  "max_price": 2000000,
  "target_price": 1900000,
  "personal_info": {
    "first_name": "Your Name",
    "last_name": "Your Surname",
    "email": "<EMAIL>",
    "phone": "+90 ************",
    "address": "Your Full Address",
    "city": "Your City",
    "postal_code": "12345",
    "id_number": "12345678901"
  },
  "payment_info": {
    "preorder_amount": 175000,
    "payment_method": "credit_card"
  },
  "preferences": {
    "auto_confirm": false,
    "max_attempts": 3,
    "retry_delay": 5
  }
}
```

## 🚀 Usage

### Monitor Bot (Version 1)
```bash
python tesla_bot_monitor.py
```

**Features:**
- Checks inventory every 30 seconds
- Opens browser tabs when vehicles are found
- Allows manual selection of multiple vehicles
- Continues monitoring after each find

### Auto Bot (Version 2)
```bash
python tesla_bot_auto.py
```

**Features:**
- Automatically fills personal information
- Handles CAPTCHA (requires manual solving)
- Confirms payment amount before processing
- Processes 175,000 TL preorder payment

## ⌨️ Keyboard Shortcuts

- **Ctrl+R**: Reinitialize bot (restart vehicle search)
- **Ctrl+C**: Stop monitoring

## 🔧 Anti-Bot Measures

Both bots use advanced techniques to bypass Tesla's anti-bot measures:

- **Selenium WebDriver**: Simulates real user interactions
- **Stealth Mode**: Disables automation detection
- **Human-like Timing**: Random delays between actions
- **Real Browser Headers**: Mimics genuine browser requests
- **No Direct API Calls**: Uses browser automation only

## 📋 Process Flow

### Monitor Bot Flow:
1. Start monitoring Tesla inventory API
2. Filter for Model Y Juniper vehicles (1.9M TL range)
3. Play notification sound when found
4. Open order page(s) in browser
5. User manually completes order form
6. Continue monitoring if requested

### Auto Bot Flow:
1. Start monitoring Tesla inventory API
2. Filter for Model Y Juniper vehicles
3. Navigate to order page automatically
4. Fill personal information from config
5. Handle CAPTCHA (manual solving required)
6. Confirm payment amount with user
7. Process 175,000 TL preorder payment
8. Complete order automatically

## 🔔 Notifications

- **Sound Alerts**: Beep notifications when vehicles are found
- **Console Logging**: Detailed progress information
- **Log Files**: `tesla_monitor.log` and `tesla_auto.log`

## ⚠️ Important Notes

### Payment Information
- **Preorder Amount**: 175,000 TL (not full vehicle price)
- **Payment Confirmation**: Manual confirmation required by default
- **CAPTCHA**: Must be solved manually when prompted

### Legal & Ethical
- These bots simulate normal user behavior
- No API abuse or server overloading
- Respects Tesla's terms of service
- For personal use only

### Performance Tips
- Run on a stable internet connection
- Keep Chrome browser updated
- Monitor during peak inventory times
- Use Version 1 for learning the process first

## 🐛 Troubleshooting

### Common Issues:

1. **ChromeDriver Error**:
   ```bash
   pip install webdriver-manager
   ```

2. **Import Errors**:
   ```bash
   pip install -r requirements.txt
   ```

3. **No Vehicles Found**:
   - Check if inventory is actually available
   - Verify price range settings
   - Ensure Turkey market is selected

4. **Form Filling Fails**:
   - Update personal information in config
   - Check for website layout changes
   - Verify field names in browser inspector

### Log Files:
- `tesla_monitor.log`: Monitor bot activities
- `tesla_auto.log`: Auto bot activities

## 📞 Support

For issues or questions:
1. Check log files for error details
2. Verify configuration settings
3. Ensure all dependencies are installed
4. Test with Monitor Bot first before using Auto Bot

## 🔄 Updates

The bots are designed to be resilient to minor website changes, but may need updates if Tesla significantly changes their ordering process.

---

**Disclaimer**: These bots are for educational and personal use only. Users are responsible for complying with Tesla's terms of service and applicable laws.