# Tesla Bot - Turkey Juniper Model Y Monitor

Two powerful bots for monitoring Tesla inventory in Turkey and automatically ordering the Juniper Model Y (1.9M TL).

## 🚗 Features

### Version 1: Monitor Bot (Browser Version)
- ✅ Continuously monitors Tesla Turkey inventory
- ✅ Detects when Juniper Model Y becomes available
- ✅ Opens order page in browser for manual completion
- ✅ Keyboard shortcuts for control (Ctrl+R, Ctrl+P, Ctrl+Q)
- ✅ Sound notifications when vehicle found
- ✅ No payment automation (safer)

### Version 2: Auto Bot (Full Automation)
- ✅ Continuously monitors Tesla Turkey inventory  
- ✅ Automatically fills order forms when vehicle found
- ✅ Handles personal information automatically
- ✅ CAPTCHA detection with manual solving
- ✅ 175,000 TL preorder payment handling
- ⚠️ **Requires careful configuration**

## 📋 Requirements

- Python 3.8 or higher
- Windows 10/11 (for sound notifications)
- Chrome browser (for auto bot)
- Internet connection

## 🛠️ Installation

1. **Clone or download this repository**
   ```bash
   git clone <repository-url>
   cd TeslaBot
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **For Auto Bot: Install ChromeDriver**
   - Download from: https://chromedriver.chromium.org/
   - Place `chromedriver.exe` in your PATH or project folder
   - Or install via: `pip install webdriver-manager`

## ⚙️ Configuration

### 1. Basic Configuration (config.py)

Edit `config.py` to customize your settings:

```python
# Target Vehicle Configuration
TARGET_VEHICLE = {
    "target_price": 1900000,  # 1.9M TL for Juniper
    "max_price_tolerance": 50000,  # Allow 50k TL tolerance
    "variant_keywords": ["juniper", "model y"]
}

# Monitoring Configuration  
MONITORING_CONFIG = {
    "check_interval": 5,  # seconds between checks
    "notification_sound": True
}
```

### 2. Personal Information (For Auto Bot Only)

**⚠️ IMPORTANT: Only configure this if using the Auto Bot!**

```python
ORDER_FORM_CONFIG = {
    "personal_info": {
        "first_name": "Your Name",
        "last_name": "Your Surname", 
        "email": "<EMAIL>",
        "phone": "+90 ************",
        "address": "Your Address",
        "city": "Istanbul",
        "postal_code": "34000"
    }
}
```

## 🚀 Usage

### Quick Start
```bash
python tesla_bot_launcher.py
```

### Direct Bot Execution

**Monitor Bot (Recommended for beginners):**
```bash
python tesla_bot_monitor.py
```

**Auto Bot (Advanced users only):**
```bash
python tesla_bot_auto.py
```

## 🎮 Keyboard Controls

While bots are running:
- **Ctrl+R**: Reinitialize/Resume monitoring
- **Ctrl+P**: Pause/Resume monitoring  
- **Ctrl+Q**: Quit bot
- **Ctrl+C**: Emergency stop

## 📊 Bot Behavior

### Monitor Bot Workflow
1. 🔍 Scans Tesla inventory every 5 seconds
2. 🎯 Looks for Juniper Model Y around 1.9M TL
3. 🔔 Plays sound notification when found
4. 🌐 Opens browser to order page
5. 👤 You complete the form manually
6. ✅ Safe and controlled process

### Auto Bot Workflow  
1. 🔍 Scans Tesla inventory every 5 seconds
2. 🎯 Looks for Juniper Model Y around 1.9M TL
3. 🔔 Plays sound notification when found
4. 🤖 Automatically opens order page
5. 📝 Fills personal information automatically
6. 🔐 Waits for manual CAPTCHA solving
7. 💳 Processes 175,000 TL preorder payment
8. ✅ Completes order automatically

## 🔧 Troubleshooting

### Common Issues

**"Missing required packages" error:**
```bash
pip install -r requirements.txt
```

**ChromeDriver not found (Auto Bot):**
- Download ChromeDriver matching your Chrome version
- Place in project folder or system PATH

**API connection errors:**
- Check internet connection
- Tesla servers might be temporarily down
- Bot will automatically retry

**Keyboard shortcuts not working:**
- Run as administrator on Windows
- Some antivirus software may block keyboard hooks

### Log Files

Logs are saved in the `logs/` directory:
- `tesla_monitor_bot_YYYYMMDD.log` - Monitor bot logs
- `tesla_auto_bot_YYYYMMDD.log` - Auto bot logs

## ⚠️ Important Warnings

### For Auto Bot Users:
- **Double-check personal information** before running
- **Monitor the bot** during operation
- **Be ready to solve CAPTCHAs** manually
- **Understand you're making real orders** with real money
- **Test with monitor bot first** to understand the process

### Legal & Ethical:
- ✅ These bots use official Tesla APIs
- ✅ No terms of service violations
- ✅ Respectful request intervals (5 seconds)
- ⚠️ Use responsibly and ethically
- ⚠️ You are responsible for any orders placed

## 📁 File Structure

```
TeslaBot/
├── tesla_bot_launcher.py    # Main launcher script
├── tesla_bot_monitor.py     # Monitor bot (browser version)
├── tesla_bot_auto.py        # Auto bot (full automation)
├── config.py                # Configuration settings
├── utils.py                 # Shared utility functions
├── requirements.txt         # Python dependencies
├── README.md               # This file
├── logs/                   # Log files directory
├── data/                   # Data storage directory
├── screenshots/            # Screenshots directory
└── responses/              # API response storage
```

## 🎯 Target Specifications

- **Model**: Tesla Model Y
- **Variant**: Juniper
- **Market**: Turkey (TR)
- **Price Target**: 1,900,000 TL
- **Tolerance**: ±50,000 TL
- **Preorder**: 175,000 TL (not full price)
- **Currency**: Turkish Lira (TRY)

## 🤝 Support

If you encounter issues:

1. Check the log files in `logs/` directory
2. Verify your configuration in `config.py`
3. Ensure all dependencies are installed
4. Try the monitor bot first before auto bot

## 📝 Version History

- **v1.0** - Initial release with both bot versions
- Monitor bot with browser opening
- Auto bot with form filling
- Keyboard shortcuts and sound notifications
- Turkish market specific configuration

## 🙏 Acknowledgments

Created for Tesla enthusiasts in Turkey who want to quickly secure their Juniper Model Y orders in the fast-moving Turkish Tesla market.

**Good luck with your Tesla order! 🚗⚡**
