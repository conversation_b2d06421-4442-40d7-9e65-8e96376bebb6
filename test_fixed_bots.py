#!/usr/bin/env python3
"""
Test Fixed Tesla Bots
Test the updated bots that use successful HTML parsing method
"""

import sys
from datetime import datetime

def test_utils_functions():
    """Test the updated utility functions"""
    print("🔧 Testing Updated Utility Functions")
    print("=" * 50)
    
    try:
        from utils import make_inventory_request, check_vehicle_availability
        
        print("1. Testing inventory request...")
        inventory_data = make_inventory_request(timeout=20)
        
        if inventory_data:
            print("   ✅ Inventory request successful!")
            print(f"   📊 Data type: {type(inventory_data)}")
            
            results = inventory_data.get('results', [])
            print(f"   🚗 Found {len(results)} vehicles")
            
            if results:
                print("   📋 Sample vehicle data:")
                sample = results[0]
                for key, value in sample.items():
                    if isinstance(value, str) and len(value) > 50:
                        value = value[:50] + "..."
                    print(f"      {key}: {value}")
            
            print("\n2. Testing vehicle availability check...")
            matching_vehicles = check_vehicle_availability(inventory_data)
            
            if matching_vehicles:
                print(f"   🎯 Found {len(matching_vehicles)} matching vehicles!")
                for i, vehicle in enumerate(matching_vehicles, 1):
                    print(f"   Vehicle {i}: {vehicle.get('price', 'N/A')} TL - {vehicle.get('trim', 'N/A')}")
            else:
                print("   ℹ️  No matching vehicles found (this is normal)")
            
            return True
        else:
            print("   ❌ Inventory request failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing utilities: {e}")
        return False

def test_monitor_bot_import():
    """Test monitor bot import and basic functionality"""
    print("\n🌐 Testing Monitor Bot")
    print("=" * 50)
    
    try:
        from tesla_bot_monitor import TeslaMonitorBot
        
        print("1. Creating monitor bot instance...")
        bot = TeslaMonitorBot()
        print("   ✅ Monitor bot created successfully")
        
        print("2. Testing inventory fetch...")
        inventory_data = bot.fetch_inventory()
        
        if inventory_data:
            print("   ✅ Monitor bot can fetch inventory!")
            results_count = len(inventory_data.get('results', []))
            print(f"   📊 Found {results_count} vehicles")
        else:
            print("   ❌ Monitor bot inventory fetch failed")
            return False
        
        print("3. Testing vehicle check...")
        vehicles = bot.check_for_vehicles()
        print(f"   🔍 Vehicle check returned {len(vehicles)} matches")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing monitor bot: {e}")
        return False

def test_auto_bot_import():
    """Test auto bot import and basic functionality"""
    print("\n🤖 Testing Auto Bot")
    print("=" * 50)
    
    try:
        from tesla_bot_auto import TeslaAutoBot
        
        print("1. Creating auto bot instance...")
        bot = TeslaAutoBot()
        print("   ✅ Auto bot created successfully")
        
        print("2. Testing inventory fetch...")
        inventory_data = bot.fetch_inventory()
        
        if inventory_data:
            print("   ✅ Auto bot can fetch inventory!")
            results_count = len(inventory_data.get('results', []))
            print(f"   📊 Found {results_count} vehicles")
        else:
            print("   ❌ Auto bot inventory fetch failed")
            return False
        
        print("3. Testing vehicle check...")
        vehicles = bot.check_for_vehicles()
        print(f"   🔍 Vehicle check returned {len(vehicles)} matches")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing auto bot: {e}")
        return False

def test_launcher_integration():
    """Test launcher integration"""
    print("\n🚀 Testing Launcher Integration")
    print("=" * 50)
    
    try:
        import tesla_bot_launcher
        print("   ✅ Launcher imports successfully")
        
        # Test dependency checking
        if hasattr(tesla_bot_launcher, 'check_dependencies'):
            deps_ok = tesla_bot_launcher.check_dependencies()
            print(f"   📦 Dependencies check: {'✅' if deps_ok else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing launcher: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing Fixed Tesla Bots")
    print("=" * 60)
    print(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Testing bots updated to use successful HTML parsing method")
    print("=" * 60)
    
    # Run tests
    test_results = {
        "Utility Functions": test_utils_functions(),
        "Monitor Bot": test_monitor_bot_import(),
        "Auto Bot": test_auto_bot_import(),
        "Launcher Integration": test_launcher_integration()
    }
    
    # Generate report
    print("\n" + "="*60)
    print("📊 TEST RESULTS")
    print("="*60)
    
    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results.values() if result)
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
    
    print("-"*60)
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ The bots have been successfully fixed!")
        print("🚗 Tesla inventory access is now working!")
        print()
        print("💡 You can now use:")
        print("   • python tesla_bot_monitor.py (Browser version)")
        print("   • python tesla_bot_auto.py (Auto version)")
        print("   • python tesla_bot_launcher.py (Main launcher)")
    elif passed_tests >= total_tests - 1:
        print("\n⚠️  MOSTLY WORKING - Minor issues detected")
        print("🚗 Tesla inventory access is working!")
        print("💡 You can try using the monitor bot")
    else:
        print("\n❌ SIGNIFICANT ISSUES DETECTED")
        print("🔧 Some components need further fixing")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n🛑 Test interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Test failed with error: {e}")
        sys.exit(1)
