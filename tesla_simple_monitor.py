#!/usr/bin/env python3
"""
Tesla Simple Monitor - Fallback Version
A simpler approach that checks the Tesla inventory page directly
"""

import time
import sys
import requests
import webbrowser
from datetime import datetime
from typing import Optional

def print_status(message: str, level: str = "INFO"):
    """Print status message with timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    symbols = {
        "INFO": "ℹ️",
        "SUCCESS": "✅", 
        "WARNING": "⚠️",
        "ERROR": "❌",
        "FOUND": "🚗"
    }
    symbol = symbols.get(level, "ℹ️")
    print(f"[{timestamp}] {symbol} {message}")

def play_notification():
    """Play notification sound"""
    try:
        import winsound
        winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
        time.sleep(0.5)
        winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
    except:
        print("\a\a\a")  # Fallback bell sound

def check_tesla_inventory() -> bool:
    """Check Tesla inventory page for availability"""
    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (<PERSON>HTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language": "tr-TR,tr;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1"
        }
        
        url = "https://www.tesla.com/tr_TR/inventory/new/my"
        
        session = requests.Session()
        session.headers.update(headers)
        
        response = session.get(url, timeout=30)
        
        if response.status_code == 200:
            content = response.text.lower()
            
            # Look for indicators that vehicles are available
            availability_indicators = [
                "available",
                "mevcut",
                "stok",
                "inventory",
                "vehicle",
                "araç",
                "model y",
                "juniper"
            ]
            
            # Look for price indicators around 1.9M TL
            price_indicators = [
                "1.900.000",
                "1,900,000", 
                "1900000",
                "₺1.9",
                "₺1,9"
            ]
            
            # Simple heuristic: if we see both availability and price indicators
            has_availability = any(indicator in content for indicator in availability_indicators)
            has_price = any(indicator in content for indicator in price_indicators)
            
            # Also check if the page doesn't show "no results" or similar
            no_results_indicators = [
                "no results",
                "sonuç bulunamadı",
                "araç bulunamadı",
                "stok yok"
            ]
            
            has_no_results = any(indicator in content for indicator in no_results_indicators)
            
            if has_availability and not has_no_results:
                print_status("Potential vehicles detected on inventory page!", "FOUND")
                return True
            else:
                print_status("No vehicles detected", "INFO")
                return False
                
        else:
            print_status(f"Could not access inventory page: {response.status_code}", "WARNING")
            return False
            
    except Exception as e:
        print_status(f"Error checking inventory: {e}", "ERROR")
        return False

def open_tesla_page():
    """Open Tesla inventory page in browser"""
    try:
        url = "https://www.tesla.com/tr_TR/inventory/new/my"
        webbrowser.open(url)
        print_status("Opened Tesla inventory page in browser", "SUCCESS")
        return True
    except Exception as e:
        print_status(f"Failed to open browser: {e}", "ERROR")
        return False

def main():
    """Main monitoring loop"""
    print("🚗 Tesla Simple Monitor - Fallback Version")
    print("=" * 50)
    print("Target: Juniper Model Y in Turkey")
    print("This version checks the inventory page directly")
    print("Press Ctrl+C to stop")
    print("=" * 50)
    
    check_count = 0
    
    try:
        while True:
            check_count += 1
            print_status(f"Check #{check_count} - Checking Tesla inventory page...", "INFO")
            
            if check_tesla_inventory():
                print_status("🎉 POTENTIAL VEHICLE FOUND!", "FOUND")
                play_notification()
                
                # Ask user if they want to open browser
                try:
                    response = input("Open Tesla inventory page in browser? (y/n): ").strip().lower()
                    if response in ['y', 'yes']:
                        open_tesla_page()
                    
                    # Ask if they want to continue monitoring
                    response = input("Continue monitoring? (y/n): ").strip().lower()
                    if response not in ['y', 'yes']:
                        break
                        
                except KeyboardInterrupt:
                    break
            
            # Wait before next check
            print_status("Waiting 15 seconds before next check...", "INFO")
            time.sleep(15)
            
    except KeyboardInterrupt:
        print_status("Monitoring stopped by user", "INFO")
    except Exception as e:
        print_status(f"Unexpected error: {e}", "ERROR")
    
    print("\n🙏 Thank you for using Tesla Simple Monitor!")

if __name__ == "__main__":
    main()
