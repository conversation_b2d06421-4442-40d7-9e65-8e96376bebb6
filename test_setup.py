#!/usr/bin/env python3
"""
Tesla Bot Setup Test
Test script to validate installation and configuration
"""

import sys
import os
import json
from datetime import datetime

def test_python_version():
    """Test Python version compatibility"""
    print("🐍 Testing Python version...")
    version = sys.version_info
    
    if version.major >= 3 and version.minor >= 8:
        print(f"   ✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
        return True
    else:
        print(f"   ❌ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.8+")
        return False

def test_dependencies():
    """Test required dependencies"""
    print("\n📦 Testing dependencies...")
    
    required_packages = {
        'requests': 'HTTP requests',
        'keyboard': 'Keyboard input handling',
        'json': 'JSON handling (built-in)',
        'datetime': 'Date/time handling (built-in)',
        'threading': 'Threading support (built-in)',
        'time': 'Time functions (built-in)',
        'sys': 'System functions (built-in)',
        'os': 'Operating system interface (built-in)'
    }
    
    optional_packages = {
        'selenium': 'Web automation (for auto bot)',
        'winsound': 'Sound notifications (Windows built-in)'
    }
    
    success_count = 0
    total_required = len(required_packages)
    
    # Test required packages
    for package, description in required_packages.items():
        try:
            __import__(package)
            print(f"   ✅ {package} - {description}")
            success_count += 1
        except ImportError:
            print(f"   ❌ {package} - {description} - MISSING")
    
    # Test optional packages
    print("\n   Optional packages:")
    for package, description in optional_packages.items():
        try:
            __import__(package)
            print(f"   ✅ {package} - {description}")
        except ImportError:
            print(f"   ⚠️  {package} - {description} - Not installed")
    
    return success_count == total_required

def test_configuration():
    """Test configuration file"""
    print("\n⚙️ Testing configuration...")
    
    try:
        from config import get_config, validate_config
        
        # Test config validation
        if validate_config():
            print("   ✅ Configuration validation passed")
        else:
            print("   ❌ Configuration validation failed")
            return False
        
        # Test config loading
        config = get_config()
        print("   ✅ Configuration loaded successfully")
        
        # Test key configuration values
        target = config.get("target_vehicle", {})
        if target.get("target_price") == 1900000:
            print("   ✅ Target price configured correctly (1.9M TL)")
        else:
            print(f"   ⚠️  Target price: {target.get('target_price', 'Not set')}")
        
        if "juniper" in [kw.lower() for kw in target.get("variant_keywords", [])]:
            print("   ✅ Juniper keyword found in target")
        else:
            print("   ⚠️  Juniper keyword not found in target")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Cannot import config: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Configuration error: {e}")
        return False

def test_utilities():
    """Test utility functions"""
    print("\n🔧 Testing utilities...")
    
    try:
        from utils import setup_logging, print_status, play_notification_sound
        
        # Test logging setup
        logger = setup_logging("test")
        print("   ✅ Logging setup successful")
        
        # Test status printing
        print_status("Test message", "INFO")
        print("   ✅ Status printing works")
        
        # Test notification sound (optional)
        try:
            play_notification_sound()
            print("   ✅ Notification sound works")
        except Exception:
            print("   ⚠️  Notification sound not available")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Cannot import utils: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Utilities error: {e}")
        return False

def test_api_connectivity():
    """Test Tesla API connectivity"""
    print("\n🌐 Testing Tesla API connectivity...")
    
    try:
        from utils import make_api_request
        from config import get_config
        
        config = get_config()
        
        # Test basic connectivity to Tesla
        print("   🔍 Testing connection to Tesla website...")
        
        # Simple test request (just check if we can reach Tesla)
        import requests
        response = requests.get("https://www.tesla.com", timeout=10)
        
        if response.status_code == 200:
            print("   ✅ Tesla website reachable")
        else:
            print(f"   ⚠️  Tesla website returned status: {response.status_code}")
        
        print("   ℹ️  API endpoint test skipped (requires actual inventory call)")
        return True
        
    except Exception as e:
        print(f"   ❌ Connectivity test failed: {e}")
        return False

def test_file_structure():
    """Test file structure and permissions"""
    print("\n📁 Testing file structure...")
    
    required_files = [
        "config.py",
        "utils.py", 
        "tesla_bot_monitor.py",
        "tesla_bot_auto.py",
        "tesla_bot_launcher.py",
        "requirements.txt"
    ]
    
    required_dirs = [
        "logs",
        "data", 
        "screenshots",
        "responses"
    ]
    
    # Test files
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} - MISSING")
            missing_files.append(file)
    
    # Test directories
    for directory in required_dirs:
        if os.path.exists(directory):
            print(f"   ✅ {directory}/ directory")
        else:
            print(f"   ⚠️  {directory}/ directory - Will be created")
    
    return len(missing_files) == 0

def test_bot_imports():
    """Test bot script imports"""
    print("\n🤖 Testing bot imports...")
    
    # Test monitor bot
    try:
        import tesla_bot_monitor
        print("   ✅ Monitor bot import successful")
        monitor_success = True
    except ImportError as e:
        print(f"   ❌ Monitor bot import failed: {e}")
        monitor_success = False
    
    # Test auto bot
    try:
        import tesla_bot_auto
        print("   ✅ Auto bot import successful")
        auto_success = True
    except ImportError as e:
        print(f"   ❌ Auto bot import failed: {e}")
        auto_success = False
    
    # Test launcher
    try:
        import tesla_bot_launcher
        print("   ✅ Launcher import successful")
        launcher_success = True
    except ImportError as e:
        print(f"   ❌ Launcher import failed: {e}")
        launcher_success = False
    
    return monitor_success and launcher_success  # Auto bot is optional

def generate_test_report(results):
    """Generate test report"""
    print("\n" + "="*60)
    print("📊 TEST REPORT")
    print("="*60)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
    
    print("-"*60)
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 ALL TESTS PASSED! Tesla Bot is ready to use.")
        print("💡 Run 'python tesla_bot_launcher.py' to start.")
    elif passed_tests >= total_tests - 1:  # Allow 1 failure
        print("\n⚠️  MOSTLY READY - Minor issues detected.")
        print("💡 You can probably still use the monitor bot.")
    else:
        print("\n❌ SETUP INCOMPLETE - Please fix the issues above.")
        print("💡 Check README.md for installation instructions.")

def main():
    """Main test function"""
    print("🧪 Tesla Bot Setup Test")
    print("="*60)
    print(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Platform: {sys.platform}")
    print("="*60)
    
    # Run all tests
    test_results = {
        "Python Version": test_python_version(),
        "Dependencies": test_dependencies(),
        "Configuration": test_configuration(),
        "Utilities": test_utilities(),
        "File Structure": test_file_structure(),
        "Bot Imports": test_bot_imports(),
        "API Connectivity": test_api_connectivity()
    }
    
    # Generate report
    generate_test_report(test_results)
    
    return all(test_results.values())

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n🛑 Test interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Test failed with error: {e}")
        sys.exit(1)
