#!/usr/bin/env python3
"""
Tesla Browser Monitor - Anti-Bot Protection Workaround
Uses browser automation to bypass Tesla's anti-bot protection
"""

import time
import sys
import webbrowser
import keyboard
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

def print_status(message: str, level: str = "INFO"):
    """Print status message with timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    symbols = {
        "INFO": "ℹ️",
        "SUCCESS": "✅", 
        "WARNING": "⚠️",
        "ERROR": "❌",
        "FOUND": "🚗"
    }
    symbol = symbols.get(level, "ℹ️")
    print(f"[{timestamp}] {symbol} {message}")

def play_notification():
    """Play notification sound"""
    try:
        import winsound
        winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
        time.sleep(0.5)
        winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
        time.sleep(0.5)
        winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
    except:
        print("\a\a\a")

class TeslaBrowserMonitor:
    """Tesla monitor using browser automation to bypass anti-bot protection"""
    
    def __init__(self):
        self.driver = None
        self.is_running = False
        self.check_count = 0
        
    def setup_browser(self) -> bool:
        """Setup Chrome browser with anti-detection measures"""
        try:
            print_status("Setting up browser...", "INFO")
            
            chrome_options = Options()
            
            # Anti-detection measures
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # Normal browser behavior
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            
            # User agent
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            
            # Remove webdriver property
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            print_status("Browser setup successful", "SUCCESS")
            return True
            
        except Exception as e:
            print_status(f"Browser setup failed: {e}", "ERROR")
            print("💡 Make sure ChromeDriver is installed and in your PATH")
            print("   Download from: https://chromedriver.chromium.org/")
            return False
    
    def check_inventory(self) -> bool:
        """Check Tesla inventory using browser"""
        try:
            url = "https://www.tesla.com/tr_TR/inventory/new/my"
            
            print_status("Loading Tesla inventory page...", "INFO")
            self.driver.get(url)
            
            # Wait for page to load
            time.sleep(5)
            
            # Check if we got blocked or redirected
            current_url = self.driver.current_url
            if "inventory" not in current_url.lower():
                print_status("Page redirected - possible blocking", "WARNING")
                return False
            
            # Look for vehicle cards or inventory items
            vehicle_selectors = [
                "[data-testid*='vehicle']",
                ".vehicle-card",
                ".inventory-card",
                ".result-card",
                "[class*='vehicle']",
                "[class*='inventory']"
            ]
            
            vehicles_found = False
            
            for selector in vehicle_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        print_status(f"Found {len(elements)} potential vehicle elements", "INFO")
                        vehicles_found = True
                        break
                except:
                    continue
            
            # Also check page text for indicators
            page_text = self.driver.page_source.lower()
            
            # Look for positive indicators
            positive_indicators = [
                "model y",
                "available",
                "mevcut", 
                "₺",
                "tl",
                "price",
                "fiyat"
            ]
            
            # Look for negative indicators
            negative_indicators = [
                "no vehicles",
                "no results",
                "sonuç bulunamadı",
                "araç bulunamadı",
                "stok yok"
            ]
            
            has_positive = any(indicator in page_text for indicator in positive_indicators)
            has_negative = any(indicator in page_text for indicator in negative_indicators)
            
            # Look specifically for price indicators around 1.9M
            price_indicators = [
                "1.900.000",
                "1,900,000",
                "1900000",
                "1.9"
            ]
            
            has_target_price = any(indicator in page_text for indicator in price_indicators)
            
            if has_target_price:
                print_status("🎯 Found potential Juniper Model Y pricing!", "FOUND")
                return True
            elif vehicles_found and has_positive and not has_negative:
                print_status("Found potential vehicles on page", "FOUND")
                return True
            else:
                print_status("No vehicles detected", "INFO")
                return False
                
        except Exception as e:
            print_status(f"Error checking inventory: {e}", "ERROR")
            return False
    
    def handle_vehicle_found(self):
        """Handle when vehicles are potentially found"""
        print_status("🚗 POTENTIAL JUNIPER MODEL Y DETECTED!", "FOUND")
        play_notification()
        
        print("\n" + "="*60)
        print("🎉 VEHICLE DETECTION ALERT!")
        print("="*60)
        print("The browser has detected potential vehicles on the Tesla inventory page.")
        print("Please manually check the browser window to verify and place your order.")
        print("="*60)
        
        # Keep browser open for manual inspection
        try:
            response = input("\nContinue monitoring? (y/n): ").strip().lower()
            if response not in ['y', 'yes']:
                self.is_running = False
        except KeyboardInterrupt:
            self.is_running = False
    
    def monitor_loop(self):
        """Main monitoring loop"""
        print_status("Starting Tesla inventory monitoring...", "SUCCESS")
        print("🎯 Target: Juniper Model Y (~1.9M TL)")
        print("⚠️  Browser window will open - do not close it!")
        print("📱 Press Ctrl+C to stop monitoring")
        print()
        
        self.is_running = True
        
        try:
            while self.is_running:
                self.check_count += 1
                print_status(f"Check #{self.check_count} - Scanning inventory...", "INFO")
                
                if self.check_inventory():
                    self.handle_vehicle_found()
                    if not self.is_running:
                        break
                
                # Wait before next check
                print_status("Waiting 30 seconds before next check...", "INFO")
                for i in range(30):
                    if not self.is_running:
                        break
                    time.sleep(1)
                    
        except KeyboardInterrupt:
            print_status("Monitoring stopped by user", "INFO")
        except Exception as e:
            print_status(f"Monitoring error: {e}", "ERROR")
        finally:
            self.cleanup()
    
    def cleanup(self):
        """Clean up browser resources"""
        if self.driver:
            try:
                self.driver.quit()
                print_status("Browser closed", "INFO")
            except:
                pass
    
    def run(self):
        """Run the monitor"""
        print("🚗 Tesla Browser Monitor - Anti-Bot Protection Workaround")
        print("=" * 60)
        print("This version uses a real browser to bypass Tesla's anti-bot protection.")
        print("=" * 60)
        
        if not self.setup_browser():
            return False
        
        try:
            self.monitor_loop()
        except Exception as e:
            print_status(f"Fatal error: {e}", "ERROR")
        finally:
            self.cleanup()
        
        print("\n🙏 Thank you for using Tesla Browser Monitor!")
        return True

def main():
    """Main entry point"""
    try:
        monitor = TeslaBrowserMonitor()
        monitor.run()
    except KeyboardInterrupt:
        print("\n🛑 Monitoring stopped by user")
    except Exception as e:
        print(f"❌ Failed to start monitor: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
