# Tesla Bot Setup Guide - Turkey Juniper Model Y

## 🎯 Quick Start

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run Setup Test**
   ```bash
   python test_setup.py
   ```

3. **Start the Bot**
   ```bash
   python tesla_bot_launcher.py
   ```
   Or double-click: `run_tesla_bot.bat`

## 📋 What You Get

### ✅ Two Bot Versions Created:

**1. Monitor Bot (`tesla_bot_monitor.py`)**
- 🔍 Continuously monitors Tesla Turkey inventory
- 🎯 Detects Juniper Model Y around 1.9M TL
- 🌐 Opens browser when vehicle found
- 👤 You complete the order manually
- ✅ **Recommended for most users**

**2. Auto Bot (`tesla_bot_auto.py`)**
- 🔍 Continuously monitors Tesla Turkey inventory
- 🤖 Automatically fills order forms
- 💳 Handles 175,000 TL preorder payment
- 🔐 Waits for manual CAPTCHA solving
- ⚠️ **Requires personal info configuration**

### ✅ Supporting Files:
- `config.py` - All configuration settings
- `utils.py` - Shared utility functions
- `tesla_bot_launcher.py` - Main menu launcher
- `test_setup.py` - Validates installation
- `requirements.txt` - Python dependencies
- `run_tesla_bot.bat` - Windows batch launcher
- `README.md` - Detailed documentation

## ⚙️ Configuration

### Basic Settings (config.py)
```python
TARGET_VEHICLE = {
    "target_price": 1900000,  # 1.9M TL for Juniper
    "max_price_tolerance": 50000,  # ±50k TL tolerance
    "variant_keywords": ["juniper", "model y"]
}

MONITORING_CONFIG = {
    "check_interval": 5,  # Check every 5 seconds
    "notification_sound": True
}
```

### Personal Info (Auto Bot Only)
```python
ORDER_FORM_CONFIG = {
    "personal_info": {
        "first_name": "Your Name",
        "last_name": "Your Surname",
        "email": "<EMAIL>",
        "phone": "+90 ************",
        # ... more fields
    }
}
```

## 🎮 How to Use

### Monitor Bot (Recommended)
1. Run: `python tesla_bot_monitor.py`
2. Bot scans inventory every 5 seconds
3. When Juniper Model Y found → Sound notification
4. Browser opens to order page
5. You fill the form manually
6. Complete 175,000 TL preorder payment

### Auto Bot (Advanced)
1. Configure personal info in `config.py`
2. Run: `python tesla_bot_auto.py`
3. Bot scans inventory every 5 seconds
4. When vehicle found → Automatically opens order page
5. Fills personal information automatically
6. Waits for you to solve CAPTCHA
7. Processes payment automatically

## 🎯 Target Specifications

- **Model**: Tesla Model Y
- **Variant**: Juniper
- **Market**: Turkey (TR)
- **Price**: ~1,900,000 TL
- **Tolerance**: ±50,000 TL
- **Preorder**: 175,000 TL (not full price)
- **URL**: https://www.tesla.com/tr_TR/inventory/new/my

## 🎮 Keyboard Controls

While bots are running:
- **Ctrl+R**: Restart/Resume monitoring
- **Ctrl+P**: Pause/Resume
- **Ctrl+Q**: Quit
- **Ctrl+C**: Emergency stop

## 🔧 Troubleshooting

### Common Issues:

**"keyboard module not found"**
```bash
pip install keyboard
```

**"selenium not found" (Auto Bot)**
```bash
pip install selenium
# Also download ChromeDriver
```

**API connection errors**
- Check internet connection
- Tesla servers might be busy
- Bot will automatically retry

**Keyboard shortcuts not working**
- Run as administrator
- Some antivirus may block keyboard hooks

### Log Files:
- `logs/tesla_monitor_bot_YYYYMMDD.log`
- `logs/tesla_auto_bot_YYYYMMDD.log`

## ⚠️ Important Warnings

### For Auto Bot:
- ⚠️ **Test with Monitor Bot first**
- ⚠️ **Double-check personal information**
- ⚠️ **You're making real orders with real money**
- ⚠️ **Be ready to solve CAPTCHAs manually**
- ⚠️ **Monitor the bot during operation**

### Legal & Ethical:
- ✅ Uses official Tesla APIs
- ✅ Respectful 5-second intervals
- ✅ No terms of service violations
- ⚠️ Use responsibly
- ⚠️ You are responsible for orders placed

## 📊 Success Indicators

### Monitor Bot:
```
[10:30:15] 🚗 JUNIPER MODEL Y FOUND! (1 vehicle(s))
[10:30:15] 🌐 Opening order page in browser...
```

### Auto Bot:
```
[10:30:15] 🚗 JUNIPER MODEL Y FOUND! (1 vehicle(s))
[10:30:15] 🤖 Starting automatic order process...
[10:30:20] 📝 Filling personal information...
[10:30:25] 🔐 CAPTCHA detected! Please solve manually.
[10:30:45] ✅ Order submitted successfully!
```

## 🎉 Ready to Use!

Your Tesla Bot setup is complete and tested. The bots are configured specifically for:

- **Turkish Tesla market**
- **Juniper Model Y variant**
- **1.9M TL price target**
- **Fast Turkish inventory turnover**

### Next Steps:
1. **Start with Monitor Bot** to understand the process
2. **Configure personal info** if you want to use Auto Bot
3. **Keep the bot running** during high-inventory periods
4. **Be ready to act quickly** when vehicles are found

**Good luck securing your Juniper Model Y! 🚗⚡**

---

*Created for Tesla enthusiasts in Turkey who want to quickly secure their orders in the fast-moving Turkish Tesla market.*
