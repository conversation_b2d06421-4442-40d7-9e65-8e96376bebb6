2025-07-17 12:42:11,417 - INFO - Chrome WebDriver initialized successfully
2025-07-17 12:42:11,424 - INFO - Press Ctrl+R to reinitialize, Ctrl+C to stop
2025-07-17 12:42:11,678 - ERROR - Failed to fetch inventory data: 403 Client Error: Forbidden for url: https://www.tesla.com/coinorder/api/v4/inventory-results?query=%7B%22model%22%3A+%22my%22%2C+%22condition%22%3A+%22new%22%2C+%22options%22%3A+%7B%7D%2C+%22arrangeby%22%3A+%22Price%22%2C+%22order%22%3A+%22asc%22%2C+%22market%22%3A+%22TR%22%2C+%22language%22%3A+%22tr%22%2C+%22super_region%22%3A+%22north_america%22%2C+%22lng%22%3A+32.8597%2C+%22lat%22%3A+39.9334%2C+%22zip%22%3A+%2206100%22%2C+%22range%22%3A+0%7D
2025-07-17 12:42:11,680 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:42:21,916 - ERROR - Failed to fetch inventory data: 403 Client Error: Forbidden for url: https://www.tesla.com/coinorder/api/v4/inventory-results?query=%7B%22model%22%3A+%22my%22%2C+%22condition%22%3A+%22new%22%2C+%22options%22%3A+%7B%7D%2C+%22arrangeby%22%3A+%22Price%22%2C+%22order%22%3A+%22asc%22%2C+%22market%22%3A+%22TR%22%2C+%22language%22%3A+%22tr%22%2C+%22super_region%22%3A+%22north_america%22%2C+%22lng%22%3A+32.8597%2C+%22lat%22%3A+39.9334%2C+%22zip%22%3A+%2206100%22%2C+%22range%22%3A+0%7D
2025-07-17 12:42:21,916 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:42:30,369 - INFO - WebDriver closed
2025-07-17 12:42:49,031 - INFO - Chrome WebDriver initialized successfully
2025-07-17 12:42:49,038 - INFO - Press Ctrl+R to reinitialize, Ctrl+C to stop
2025-07-17 12:42:49,278 - ERROR - Failed to fetch inventory data: 403 Client Error: Forbidden for url: https://www.tesla.com/coinorder/api/v4/inventory-results?query=%7B%22model%22%3A+%22my%22%2C+%22condition%22%3A+%22new%22%2C+%22options%22%3A+%7B%7D%2C+%22arrangeby%22%3A+%22Price%22%2C+%22order%22%3A+%22asc%22%2C+%22market%22%3A+%22TR%22%2C+%22language%22%3A+%22tr%22%2C+%22super_region%22%3A+%22north_america%22%2C+%22lng%22%3A+32.8597%2C+%22lat%22%3A+39.9334%2C+%22zip%22%3A+%2206100%22%2C+%22range%22%3A+0%7D
2025-07-17 12:42:49,279 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:42:59,500 - ERROR - Failed to fetch inventory data: 403 Client Error: Forbidden for url: https://www.tesla.com/coinorder/api/v4/inventory-results?query=%7B%22model%22%3A+%22my%22%2C+%22condition%22%3A+%22new%22%2C+%22options%22%3A+%7B%7D%2C+%22arrangeby%22%3A+%22Price%22%2C+%22order%22%3A+%22asc%22%2C+%22market%22%3A+%22TR%22%2C+%22language%22%3A+%22tr%22%2C+%22super_region%22%3A+%22north_america%22%2C+%22lng%22%3A+32.8597%2C+%22lat%22%3A+39.9334%2C+%22zip%22%3A+%2206100%22%2C+%22range%22%3A+0%7D
2025-07-17 12:42:59,501 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:43:09,738 - ERROR - Failed to fetch inventory data: 403 Client Error: Forbidden for url: https://www.tesla.com/coinorder/api/v4/inventory-results?query=%7B%22model%22%3A+%22my%22%2C+%22condition%22%3A+%22new%22%2C+%22options%22%3A+%7B%7D%2C+%22arrangeby%22%3A+%22Price%22%2C+%22order%22%3A+%22asc%22%2C+%22market%22%3A+%22TR%22%2C+%22language%22%3A+%22tr%22%2C+%22super_region%22%3A+%22north_america%22%2C+%22lng%22%3A+32.8597%2C+%22lat%22%3A+39.9334%2C+%22zip%22%3A+%2206100%22%2C+%22range%22%3A+0%7D
2025-07-17 12:43:09,739 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:43:19,963 - ERROR - Failed to fetch inventory data: 403 Client Error: Forbidden for url: https://www.tesla.com/coinorder/api/v4/inventory-results?query=%7B%22model%22%3A+%22my%22%2C+%22condition%22%3A+%22new%22%2C+%22options%22%3A+%7B%7D%2C+%22arrangeby%22%3A+%22Price%22%2C+%22order%22%3A+%22asc%22%2C+%22market%22%3A+%22TR%22%2C+%22language%22%3A+%22tr%22%2C+%22super_region%22%3A+%22north_america%22%2C+%22lng%22%3A+32.8597%2C+%22lat%22%3A+39.9334%2C+%22zip%22%3A+%2206100%22%2C+%22range%22%3A+0%7D
2025-07-17 12:43:19,964 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:43:23,460 - INFO - Monitoring stopped by user
2025-07-17 12:43:23,463 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)': /session/ba02f519c53a3a73e69f099b4c63a3e8
2025-07-17 12:43:27,557 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000219DFC3CA30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/ba02f519c53a3a73e69f099b4c63a3e8
2025-07-17 12:43:31,646 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000219DFC3C8B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/ba02f519c53a3a73e69f099b4c63a3e8
2025-07-17 12:48:33,140 - INFO - Chrome WebDriver initialized successfully
2025-07-17 12:48:33,148 - INFO - Press Ctrl+R to reinitialize, Ctrl+C to stop
2025-07-17 12:48:34,339 - ERROR - Failed to fetch inventory data: 403 Client Error: Forbidden for url: https://www.tesla.com/coinorder/api/v4/inventory-results?query=%7B%22model%22%3A+%22my%22%2C+%22condition%22%3A+%22new%22%2C+%22options%22%3A+%7B%7D%2C+%22arrangeby%22%3A+%22Price%22%2C+%22order%22%3A+%22asc%22%2C+%22market%22%3A+%22TR%22%2C+%22language%22%3A+%22tr%22%2C+%22super_region%22%3A+%22north_america%22%2C+%22lng%22%3A+32.8597%2C+%22lat%22%3A+39.9334%2C+%22zip%22%3A+%2206100%22%2C+%22range%22%3A+0%7D
2025-07-17 12:48:34,340 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:48:44,501 - ERROR - Failed to fetch inventory data: 403 Client Error: Forbidden for url: https://www.tesla.com/coinorder/api/v4/inventory-results?query=%7B%22model%22%3A+%22my%22%2C+%22condition%22%3A+%22new%22%2C+%22options%22%3A+%7B%7D%2C+%22arrangeby%22%3A+%22Price%22%2C+%22order%22%3A+%22asc%22%2C+%22market%22%3A+%22TR%22%2C+%22language%22%3A+%22tr%22%2C+%22super_region%22%3A+%22north_america%22%2C+%22lng%22%3A+32.8597%2C+%22lat%22%3A+39.9334%2C+%22zip%22%3A+%2206100%22%2C+%22range%22%3A+0%7D
2025-07-17 12:48:44,501 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:48:54,604 - ERROR - Failed to fetch inventory data: 403 Client Error: Forbidden for url: https://www.tesla.com/coinorder/api/v4/inventory-results?query=%7B%22model%22%3A+%22my%22%2C+%22condition%22%3A+%22new%22%2C+%22options%22%3A+%7B%7D%2C+%22arrangeby%22%3A+%22Price%22%2C+%22order%22%3A+%22asc%22%2C+%22market%22%3A+%22TR%22%2C+%22language%22%3A+%22tr%22%2C+%22super_region%22%3A+%22north_america%22%2C+%22lng%22%3A+32.8597%2C+%22lat%22%3A+39.9334%2C+%22zip%22%3A+%2206100%22%2C+%22range%22%3A+0%7D
2025-07-17 12:48:54,605 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:48:57,036 - INFO - Monitoring stopped by user
2025-07-17 12:48:57,038 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)': /session/3fdd2197f08948c595c2090b367e494a
2025-07-17 12:49:01,095 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029C43A3CBB0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/3fdd2197f08948c595c2090b367e494a
2025-07-17 12:49:11,249 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029C43AAE070>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/3fdd2197f08948c595c2090b367e494a
2025-07-17 12:54:24,745 - INFO - Chrome WebDriver initialized successfully
2025-07-17 12:54:24,752 - INFO - Press Ctrl+R to reinitialize, Ctrl+C to stop
2025-07-17 12:54:24,757 - INFO - Loading Tesla inventory page...
2025-07-17 12:54:57,546 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-17 12:54:57,546 - ERROR - Failed to get inventory data: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff67d4ee925+77845]
	GetHandleVerifier [0x0x7ff67d4ee980+77936]
	(No symbol) [0x0x7ff67d2a9b0c]
	(No symbol) [0x0x7ff67d2f08bf]
	(No symbol) [0x0x7ff67d328792]
	(No symbol) [0x0x7ff67d323293]
	(No symbol) [0x0x7ff67d322359]
	(No symbol) [0x0x7ff67d274b05]
	GetHandleVerifier [0x0x7ff67d7c683d+3059501]
	GetHandleVerifier [0x0x7ff67d7c0bfd+3035885]
	GetHandleVerifier [0x0x7ff67d7e03f0+3164896]
	GetHandleVerifier [0x0x7ff67d508c2e+185118]
	GetHandleVerifier [0x0x7ff67d51053f+216111]
	(No symbol) [0x0x7ff67d273b00]
	GetHandleVerifier [0x0x7ff67d8ebd88+4260984]
	BaseThreadInitThunk [0x0x7ffa9afa7374+20]
	RtlUserThreadStart [0x0x7ffa9b3fcc91+33]

2025-07-17 12:54:57,548 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:54:59,570 - INFO - WebDriver closed
2025-07-17 12:55:05,356 - INFO - Chrome WebDriver initialized successfully
2025-07-17 12:55:05,361 - INFO - Press Ctrl+R to reinitialize, Ctrl+C to stop
2025-07-17 12:55:05,366 - INFO - Loading Tesla inventory page...
2025-07-17 12:55:07,567 - INFO - Loading Tesla inventory page...
2025-07-17 12:55:11,841 - ERROR - Failed to get inventory data: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.157)
Stacktrace:
	GetHandleVerifier [0x0x7ff67d4ee925+77845]
	GetHandleVerifier [0x0x7ff67d4ee980+77936]
	(No symbol) [0x0x7ff67d2a9cda]
	(No symbol) [0x0x7ff67d295f35]
	(No symbol) [0x0x7ff67d2baabe]
	(No symbol) [0x0x7ff67d32feb5]
	(No symbol) [0x0x7ff67d350432]
	(No symbol) [0x0x7ff67d3286a3]
	(No symbol) [0x0x7ff67d2f1791]
	(No symbol) [0x0x7ff67d2f2523]
	GetHandleVerifier [0x0x7ff67d7c683d+3059501]
	GetHandleVerifier [0x0x7ff67d7c0bfd+3035885]
	GetHandleVerifier [0x0x7ff67d7e03f0+3164896]
	GetHandleVerifier [0x0x7ff67d508c2e+185118]
	GetHandleVerifier [0x0x7ff67d51053f+216111]
	GetHandleVerifier [0x0x7ff67d4f72d4+113092]
	GetHandleVerifier [0x0x7ff67d4f7489+113529]
	GetHandleVerifier [0x0x7ff67d4de288+10616]
	BaseThreadInitThunk [0x0x7ffa9afa7374+20]
	RtlUserThreadStart [0x0x7ffa9b3fcc91+33]

2025-07-17 12:55:11,843 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:55:12,403 - ERROR - Failed to get inventory data: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff67d4ee925+77845]
	GetHandleVerifier [0x0x7ff67d4ee980+77936]
	(No symbol) [0x0x7ff67d2a9b0c]
	(No symbol) [0x0x7ff67d2f08bf]
	(No symbol) [0x0x7ff67d328792]
	(No symbol) [0x0x7ff67d323293]
	(No symbol) [0x0x7ff67d322359]
	(No symbol) [0x0x7ff67d274b05]
	GetHandleVerifier [0x0x7ff67d7c683d+3059501]
	GetHandleVerifier [0x0x7ff67d7c0bfd+3035885]
	GetHandleVerifier [0x0x7ff67d7e03f0+3164896]
	GetHandleVerifier [0x0x7ff67d508c2e+185118]
	GetHandleVerifier [0x0x7ff67d51053f+216111]
	(No symbol) [0x0x7ff67d273b00]
	GetHandleVerifier [0x0x7ff67d8ebd88+4260984]
	BaseThreadInitThunk [0x0x7ffa9afa7374+20]
	RtlUserThreadStart [0x0x7ffa9b3fcc91+33]

2025-07-17 12:55:12,404 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:55:14,820 - INFO - Monitoring stopped by user
2025-07-17 12:55:14,822 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)': /session/d2597b1d5aeb008497cf9a2dff34d6e7
2025-07-17 12:56:56,631 - INFO - ====== WebDriver manager ======
2025-07-17 12:56:57,902 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-17 12:56:58,028 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-17 12:56:58,134 - INFO - There is no [win64] chromedriver "138.0.7204.157" for browser google-chrome "138.0.7204" in cache
2025-07-17 12:56:58,135 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-17 12:56:58,418 - INFO - WebDriver version 138.0.7204.157 selected
2025-07-17 12:56:58,425 - INFO - Modern chrome version https://storage.googleapis.com/chrome-for-testing-public/138.0.7204.157/win32/chromedriver-win32.zip
2025-07-17 12:56:58,426 - INFO - About to download new driver from https://storage.googleapis.com/chrome-for-testing-public/138.0.7204.157/win32/chromedriver-win32.zip
2025-07-17 12:56:58,575 - INFO - Driver downloading response is 200
2025-07-17 12:56:59,425 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-17 12:57:00,915 - INFO - Driver has been saved in cache [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.157]
2025-07-17 12:57:00,957 - ERROR - Failed to setup WebDriver: [WinError 193] %1 is not a valid Win32 application
2025-07-17 12:57:00,959 - WARNING - WebDriver setup failed, will use default browser for order pages
2025-07-17 12:57:00,966 - INFO - Press Ctrl+R to reinitialize, Ctrl+C to stop
2025-07-17 12:57:00,971 - INFO - Loading Tesla inventory page...
2025-07-17 12:57:00,971 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 12:57:00,972 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:57:10,987 - INFO - Loading Tesla inventory page...
2025-07-17 12:57:10,987 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 12:57:10,988 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:57:21,001 - INFO - Loading Tesla inventory page...
2025-07-17 12:57:21,002 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 12:57:21,002 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:57:31,019 - INFO - Loading Tesla inventory page...
2025-07-17 12:57:31,020 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 12:57:31,020 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:57:38,369 - INFO - Monitoring stopped by user
2025-07-17 12:57:45,668 - INFO - ====== WebDriver manager ======
2025-07-17 12:57:46,886 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-17 12:57:47,011 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-17 12:57:47,106 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.157\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-07-17 12:57:47,109 - ERROR - Failed to setup WebDriver: [WinError 193] %1 is not a valid Win32 application
2025-07-17 12:57:47,109 - WARNING - WebDriver setup failed, will use default browser for order pages
2025-07-17 12:57:47,116 - INFO - Press Ctrl+R to reinitialize, Ctrl+C to stop
2025-07-17 12:57:47,121 - INFO - Loading Tesla inventory page...
2025-07-17 12:57:47,121 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 12:57:47,122 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:57:50,222 - INFO - Monitoring stopped by user
2025-07-17 12:57:54,125 - INFO - ====== WebDriver manager ======
2025-07-17 12:57:55,369 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-17 12:57:55,480 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-17 12:57:55,584 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.157\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-07-17 12:57:55,587 - ERROR - Failed to setup WebDriver: [WinError 193] %1 is not a valid Win32 application
2025-07-17 12:57:55,588 - WARNING - WebDriver setup failed, will use default browser for order pages
2025-07-17 12:57:55,596 - INFO - Press Ctrl+R to reinitialize, Ctrl+C to stop
2025-07-17 12:57:55,601 - INFO - Loading Tesla inventory page...
2025-07-17 12:57:55,601 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 12:57:55,602 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:58:05,622 - INFO - Loading Tesla inventory page...
2025-07-17 12:58:05,623 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 12:58:05,623 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:58:15,642 - INFO - Loading Tesla inventory page...
2025-07-17 12:58:15,642 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 12:58:15,642 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:58:25,653 - INFO - Loading Tesla inventory page...
2025-07-17 12:58:25,653 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 12:58:25,654 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:58:35,665 - INFO - Loading Tesla inventory page...
2025-07-17 12:58:35,665 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 12:58:35,666 - WARNING - Failed to fetch inventory data, retrying...
