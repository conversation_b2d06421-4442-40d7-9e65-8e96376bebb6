2025-07-17 12:42:11,417 - INFO - Chrome WebDriver initialized successfully
2025-07-17 12:42:11,424 - INFO - Press Ctrl+R to reinitialize, Ctrl+C to stop
2025-07-17 12:42:11,678 - ERROR - Failed to fetch inventory data: 403 Client Error: Forbidden for url: https://www.tesla.com/coinorder/api/v4/inventory-results?query=%7B%22model%22%3A+%22my%22%2C+%22condition%22%3A+%22new%22%2C+%22options%22%3A+%7B%7D%2C+%22arrangeby%22%3A+%22Price%22%2C+%22order%22%3A+%22asc%22%2C+%22market%22%3A+%22TR%22%2C+%22language%22%3A+%22tr%22%2C+%22super_region%22%3A+%22north_america%22%2C+%22lng%22%3A+32.8597%2C+%22lat%22%3A+39.9334%2C+%22zip%22%3A+%2206100%22%2C+%22range%22%3A+0%7D
2025-07-17 12:42:11,680 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:42:21,916 - ERROR - Failed to fetch inventory data: 403 Client Error: Forbidden for url: https://www.tesla.com/coinorder/api/v4/inventory-results?query=%7B%22model%22%3A+%22my%22%2C+%22condition%22%3A+%22new%22%2C+%22options%22%3A+%7B%7D%2C+%22arrangeby%22%3A+%22Price%22%2C+%22order%22%3A+%22asc%22%2C+%22market%22%3A+%22TR%22%2C+%22language%22%3A+%22tr%22%2C+%22super_region%22%3A+%22north_america%22%2C+%22lng%22%3A+32.8597%2C+%22lat%22%3A+39.9334%2C+%22zip%22%3A+%2206100%22%2C+%22range%22%3A+0%7D
2025-07-17 12:42:21,916 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:42:30,369 - INFO - WebDriver closed
2025-07-17 12:42:49,031 - INFO - Chrome WebDriver initialized successfully
2025-07-17 12:42:49,038 - INFO - Press Ctrl+R to reinitialize, Ctrl+C to stop
2025-07-17 12:42:49,278 - ERROR - Failed to fetch inventory data: 403 Client Error: Forbidden for url: https://www.tesla.com/coinorder/api/v4/inventory-results?query=%7B%22model%22%3A+%22my%22%2C+%22condition%22%3A+%22new%22%2C+%22options%22%3A+%7B%7D%2C+%22arrangeby%22%3A+%22Price%22%2C+%22order%22%3A+%22asc%22%2C+%22market%22%3A+%22TR%22%2C+%22language%22%3A+%22tr%22%2C+%22super_region%22%3A+%22north_america%22%2C+%22lng%22%3A+32.8597%2C+%22lat%22%3A+39.9334%2C+%22zip%22%3A+%2206100%22%2C+%22range%22%3A+0%7D
2025-07-17 12:42:49,279 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:42:59,500 - ERROR - Failed to fetch inventory data: 403 Client Error: Forbidden for url: https://www.tesla.com/coinorder/api/v4/inventory-results?query=%7B%22model%22%3A+%22my%22%2C+%22condition%22%3A+%22new%22%2C+%22options%22%3A+%7B%7D%2C+%22arrangeby%22%3A+%22Price%22%2C+%22order%22%3A+%22asc%22%2C+%22market%22%3A+%22TR%22%2C+%22language%22%3A+%22tr%22%2C+%22super_region%22%3A+%22north_america%22%2C+%22lng%22%3A+32.8597%2C+%22lat%22%3A+39.9334%2C+%22zip%22%3A+%2206100%22%2C+%22range%22%3A+0%7D
2025-07-17 12:42:59,501 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:43:09,738 - ERROR - Failed to fetch inventory data: 403 Client Error: Forbidden for url: https://www.tesla.com/coinorder/api/v4/inventory-results?query=%7B%22model%22%3A+%22my%22%2C+%22condition%22%3A+%22new%22%2C+%22options%22%3A+%7B%7D%2C+%22arrangeby%22%3A+%22Price%22%2C+%22order%22%3A+%22asc%22%2C+%22market%22%3A+%22TR%22%2C+%22language%22%3A+%22tr%22%2C+%22super_region%22%3A+%22north_america%22%2C+%22lng%22%3A+32.8597%2C+%22lat%22%3A+39.9334%2C+%22zip%22%3A+%2206100%22%2C+%22range%22%3A+0%7D
2025-07-17 12:43:09,739 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:43:19,963 - ERROR - Failed to fetch inventory data: 403 Client Error: Forbidden for url: https://www.tesla.com/coinorder/api/v4/inventory-results?query=%7B%22model%22%3A+%22my%22%2C+%22condition%22%3A+%22new%22%2C+%22options%22%3A+%7B%7D%2C+%22arrangeby%22%3A+%22Price%22%2C+%22order%22%3A+%22asc%22%2C+%22market%22%3A+%22TR%22%2C+%22language%22%3A+%22tr%22%2C+%22super_region%22%3A+%22north_america%22%2C+%22lng%22%3A+32.8597%2C+%22lat%22%3A+39.9334%2C+%22zip%22%3A+%2206100%22%2C+%22range%22%3A+0%7D
2025-07-17 12:43:19,964 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:43:23,460 - INFO - Monitoring stopped by user
2025-07-17 12:43:23,463 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)': /session/ba02f519c53a3a73e69f099b4c63a3e8
2025-07-17 12:43:27,557 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000219DFC3CA30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/ba02f519c53a3a73e69f099b4c63a3e8
2025-07-17 12:43:31,646 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000219DFC3C8B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/ba02f519c53a3a73e69f099b4c63a3e8
2025-07-17 12:48:33,140 - INFO - Chrome WebDriver initialized successfully
2025-07-17 12:48:33,148 - INFO - Press Ctrl+R to reinitialize, Ctrl+C to stop
2025-07-17 12:48:34,339 - ERROR - Failed to fetch inventory data: 403 Client Error: Forbidden for url: https://www.tesla.com/coinorder/api/v4/inventory-results?query=%7B%22model%22%3A+%22my%22%2C+%22condition%22%3A+%22new%22%2C+%22options%22%3A+%7B%7D%2C+%22arrangeby%22%3A+%22Price%22%2C+%22order%22%3A+%22asc%22%2C+%22market%22%3A+%22TR%22%2C+%22language%22%3A+%22tr%22%2C+%22super_region%22%3A+%22north_america%22%2C+%22lng%22%3A+32.8597%2C+%22lat%22%3A+39.9334%2C+%22zip%22%3A+%2206100%22%2C+%22range%22%3A+0%7D
2025-07-17 12:48:34,340 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:48:44,501 - ERROR - Failed to fetch inventory data: 403 Client Error: Forbidden for url: https://www.tesla.com/coinorder/api/v4/inventory-results?query=%7B%22model%22%3A+%22my%22%2C+%22condition%22%3A+%22new%22%2C+%22options%22%3A+%7B%7D%2C+%22arrangeby%22%3A+%22Price%22%2C+%22order%22%3A+%22asc%22%2C+%22market%22%3A+%22TR%22%2C+%22language%22%3A+%22tr%22%2C+%22super_region%22%3A+%22north_america%22%2C+%22lng%22%3A+32.8597%2C+%22lat%22%3A+39.9334%2C+%22zip%22%3A+%2206100%22%2C+%22range%22%3A+0%7D
2025-07-17 12:48:44,501 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:48:54,604 - ERROR - Failed to fetch inventory data: 403 Client Error: Forbidden for url: https://www.tesla.com/coinorder/api/v4/inventory-results?query=%7B%22model%22%3A+%22my%22%2C+%22condition%22%3A+%22new%22%2C+%22options%22%3A+%7B%7D%2C+%22arrangeby%22%3A+%22Price%22%2C+%22order%22%3A+%22asc%22%2C+%22market%22%3A+%22TR%22%2C+%22language%22%3A+%22tr%22%2C+%22super_region%22%3A+%22north_america%22%2C+%22lng%22%3A+32.8597%2C+%22lat%22%3A+39.9334%2C+%22zip%22%3A+%2206100%22%2C+%22range%22%3A+0%7D
2025-07-17 12:48:54,605 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:48:57,036 - INFO - Monitoring stopped by user
2025-07-17 12:48:57,038 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)': /session/3fdd2197f08948c595c2090b367e494a
2025-07-17 12:49:01,095 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029C43A3CBB0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/3fdd2197f08948c595c2090b367e494a
2025-07-17 12:49:11,249 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029C43AAE070>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/3fdd2197f08948c595c2090b367e494a
2025-07-17 12:54:24,745 - INFO - Chrome WebDriver initialized successfully
2025-07-17 12:54:24,752 - INFO - Press Ctrl+R to reinitialize, Ctrl+C to stop
2025-07-17 12:54:24,757 - INFO - Loading Tesla inventory page...
2025-07-17 12:54:57,546 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-17 12:54:57,546 - ERROR - Failed to get inventory data: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff67d4ee925+77845]
	GetHandleVerifier [0x0x7ff67d4ee980+77936]
	(No symbol) [0x0x7ff67d2a9b0c]
	(No symbol) [0x0x7ff67d2f08bf]
	(No symbol) [0x0x7ff67d328792]
	(No symbol) [0x0x7ff67d323293]
	(No symbol) [0x0x7ff67d322359]
	(No symbol) [0x0x7ff67d274b05]
	GetHandleVerifier [0x0x7ff67d7c683d+3059501]
	GetHandleVerifier [0x0x7ff67d7c0bfd+3035885]
	GetHandleVerifier [0x0x7ff67d7e03f0+3164896]
	GetHandleVerifier [0x0x7ff67d508c2e+185118]
	GetHandleVerifier [0x0x7ff67d51053f+216111]
	(No symbol) [0x0x7ff67d273b00]
	GetHandleVerifier [0x0x7ff67d8ebd88+4260984]
	BaseThreadInitThunk [0x0x7ffa9afa7374+20]
	RtlUserThreadStart [0x0x7ffa9b3fcc91+33]

2025-07-17 12:54:57,548 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:54:59,570 - INFO - WebDriver closed
2025-07-17 12:55:05,356 - INFO - Chrome WebDriver initialized successfully
2025-07-17 12:55:05,361 - INFO - Press Ctrl+R to reinitialize, Ctrl+C to stop
2025-07-17 12:55:05,366 - INFO - Loading Tesla inventory page...
2025-07-17 12:55:07,567 - INFO - Loading Tesla inventory page...
2025-07-17 12:55:11,841 - ERROR - Failed to get inventory data: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.157)
Stacktrace:
	GetHandleVerifier [0x0x7ff67d4ee925+77845]
	GetHandleVerifier [0x0x7ff67d4ee980+77936]
	(No symbol) [0x0x7ff67d2a9cda]
	(No symbol) [0x0x7ff67d295f35]
	(No symbol) [0x0x7ff67d2baabe]
	(No symbol) [0x0x7ff67d32feb5]
	(No symbol) [0x0x7ff67d350432]
	(No symbol) [0x0x7ff67d3286a3]
	(No symbol) [0x0x7ff67d2f1791]
	(No symbol) [0x0x7ff67d2f2523]
	GetHandleVerifier [0x0x7ff67d7c683d+3059501]
	GetHandleVerifier [0x0x7ff67d7c0bfd+3035885]
	GetHandleVerifier [0x0x7ff67d7e03f0+3164896]
	GetHandleVerifier [0x0x7ff67d508c2e+185118]
	GetHandleVerifier [0x0x7ff67d51053f+216111]
	GetHandleVerifier [0x0x7ff67d4f72d4+113092]
	GetHandleVerifier [0x0x7ff67d4f7489+113529]
	GetHandleVerifier [0x0x7ff67d4de288+10616]
	BaseThreadInitThunk [0x0x7ffa9afa7374+20]
	RtlUserThreadStart [0x0x7ffa9b3fcc91+33]

2025-07-17 12:55:11,843 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:55:12,403 - ERROR - Failed to get inventory data: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff67d4ee925+77845]
	GetHandleVerifier [0x0x7ff67d4ee980+77936]
	(No symbol) [0x0x7ff67d2a9b0c]
	(No symbol) [0x0x7ff67d2f08bf]
	(No symbol) [0x0x7ff67d328792]
	(No symbol) [0x0x7ff67d323293]
	(No symbol) [0x0x7ff67d322359]
	(No symbol) [0x0x7ff67d274b05]
	GetHandleVerifier [0x0x7ff67d7c683d+3059501]
	GetHandleVerifier [0x0x7ff67d7c0bfd+3035885]
	GetHandleVerifier [0x0x7ff67d7e03f0+3164896]
	GetHandleVerifier [0x0x7ff67d508c2e+185118]
	GetHandleVerifier [0x0x7ff67d51053f+216111]
	(No symbol) [0x0x7ff67d273b00]
	GetHandleVerifier [0x0x7ff67d8ebd88+4260984]
	BaseThreadInitThunk [0x0x7ffa9afa7374+20]
	RtlUserThreadStart [0x0x7ffa9b3fcc91+33]

2025-07-17 12:55:12,404 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:55:14,820 - INFO - Monitoring stopped by user
2025-07-17 12:55:14,822 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)': /session/d2597b1d5aeb008497cf9a2dff34d6e7
2025-07-17 12:56:56,631 - INFO - ====== WebDriver manager ======
2025-07-17 12:56:57,902 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-17 12:56:58,028 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-17 12:56:58,134 - INFO - There is no [win64] chromedriver "138.0.7204.157" for browser google-chrome "138.0.7204" in cache
2025-07-17 12:56:58,135 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-17 12:56:58,418 - INFO - WebDriver version 138.0.7204.157 selected
2025-07-17 12:56:58,425 - INFO - Modern chrome version https://storage.googleapis.com/chrome-for-testing-public/138.0.7204.157/win32/chromedriver-win32.zip
2025-07-17 12:56:58,426 - INFO - About to download new driver from https://storage.googleapis.com/chrome-for-testing-public/138.0.7204.157/win32/chromedriver-win32.zip
2025-07-17 12:56:58,575 - INFO - Driver downloading response is 200
2025-07-17 12:56:59,425 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-17 12:57:00,915 - INFO - Driver has been saved in cache [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.157]
2025-07-17 12:57:00,957 - ERROR - Failed to setup WebDriver: [WinError 193] %1 is not a valid Win32 application
2025-07-17 12:57:00,959 - WARNING - WebDriver setup failed, will use default browser for order pages
2025-07-17 12:57:00,966 - INFO - Press Ctrl+R to reinitialize, Ctrl+C to stop
2025-07-17 12:57:00,971 - INFO - Loading Tesla inventory page...
2025-07-17 12:57:00,971 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 12:57:00,972 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:57:10,987 - INFO - Loading Tesla inventory page...
2025-07-17 12:57:10,987 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 12:57:10,988 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:57:21,001 - INFO - Loading Tesla inventory page...
2025-07-17 12:57:21,002 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 12:57:21,002 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:57:31,019 - INFO - Loading Tesla inventory page...
2025-07-17 12:57:31,020 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 12:57:31,020 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:57:38,369 - INFO - Monitoring stopped by user
2025-07-17 12:57:45,668 - INFO - ====== WebDriver manager ======
2025-07-17 12:57:46,886 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-17 12:57:47,011 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-17 12:57:47,106 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.157\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-07-17 12:57:47,109 - ERROR - Failed to setup WebDriver: [WinError 193] %1 is not a valid Win32 application
2025-07-17 12:57:47,109 - WARNING - WebDriver setup failed, will use default browser for order pages
2025-07-17 12:57:47,116 - INFO - Press Ctrl+R to reinitialize, Ctrl+C to stop
2025-07-17 12:57:47,121 - INFO - Loading Tesla inventory page...
2025-07-17 12:57:47,121 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 12:57:47,122 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:57:50,222 - INFO - Monitoring stopped by user
2025-07-17 12:57:54,125 - INFO - ====== WebDriver manager ======
2025-07-17 12:57:55,369 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-17 12:57:55,480 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-17 12:57:55,584 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.157\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-07-17 12:57:55,587 - ERROR - Failed to setup WebDriver: [WinError 193] %1 is not a valid Win32 application
2025-07-17 12:57:55,588 - WARNING - WebDriver setup failed, will use default browser for order pages
2025-07-17 12:57:55,596 - INFO - Press Ctrl+R to reinitialize, Ctrl+C to stop
2025-07-17 12:57:55,601 - INFO - Loading Tesla inventory page...
2025-07-17 12:57:55,601 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 12:57:55,602 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:58:05,622 - INFO - Loading Tesla inventory page...
2025-07-17 12:58:05,623 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 12:58:05,623 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:58:15,642 - INFO - Loading Tesla inventory page...
2025-07-17 12:58:15,642 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 12:58:15,642 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:58:25,653 - INFO - Loading Tesla inventory page...
2025-07-17 12:58:25,653 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 12:58:25,654 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 12:58:35,665 - INFO - Loading Tesla inventory page...
2025-07-17 12:58:35,665 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 12:58:35,666 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 13:01:37,442 - INFO - ====== WebDriver manager ======
2025-07-17 13:01:38,687 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-17 13:01:38,792 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-17 13:01:38,887 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.157\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-07-17 13:01:38,889 - ERROR - Failed to setup WebDriver: [WinError 193] %1 is not a valid Win32 application
2025-07-17 13:01:38,889 - WARNING - WebDriver setup failed, will use default browser for order pages
2025-07-17 13:01:38,896 - INFO - Press Ctrl+R to reinitialize, Ctrl+C to stop
2025-07-17 13:01:38,899 - INFO - Loading Tesla inventory page...
2025-07-17 13:01:38,900 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 13:01:38,900 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 13:01:48,916 - INFO - Loading Tesla inventory page...
2025-07-17 13:01:48,917 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 13:01:48,918 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 13:01:58,936 - INFO - Loading Tesla inventory page...
2025-07-17 13:01:58,937 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 13:01:58,937 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 13:02:08,952 - INFO - Loading Tesla inventory page...
2025-07-17 13:02:08,952 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 13:02:08,952 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 13:02:18,958 - INFO - Loading Tesla inventory page...
2025-07-17 13:02:18,959 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 13:02:18,959 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 13:02:28,972 - INFO - Loading Tesla inventory page...
2025-07-17 13:02:28,973 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 13:02:28,973 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 13:02:38,992 - INFO - Loading Tesla inventory page...
2025-07-17 13:02:38,992 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 13:02:38,993 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 13:02:49,001 - INFO - Loading Tesla inventory page...
2025-07-17 13:02:49,002 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 13:02:49,002 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 13:02:59,021 - INFO - Loading Tesla inventory page...
2025-07-17 13:02:59,022 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 13:02:59,022 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 13:03:09,037 - INFO - Loading Tesla inventory page...
2025-07-17 13:03:09,038 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 13:03:09,040 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 13:03:19,052 - INFO - Loading Tesla inventory page...
2025-07-17 13:03:19,052 - ERROR - Failed to get inventory data: 'NoneType' object has no attribute 'get'
2025-07-17 13:03:19,053 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 13:05:35,252 - INFO - ====== WebDriver manager ======
2025-07-17 13:05:36,461 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-17 13:05:36,570 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-17 13:05:36,668 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.157\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-07-17 13:05:36,670 - WARNING - WebDriver manager failed: [WinError 193] %1 is not a valid Win32 application, trying direct Chrome...
2025-07-17 13:05:38,505 - INFO - Chrome WebDriver initialized successfully
2025-07-17 13:05:38,510 - INFO - Press Ctrl+R to reinitialize, Ctrl+C to stop
2025-07-17 13:05:38,513 - INFO - Loading Tesla inventory page...
2025-07-17 13:05:51,718 - ERROR - Failed to get inventory data: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=138.0.7204.157)
Stacktrace:
	GetHandleVerifier [0x0x7ff67d4ee925+77845]
	GetHandleVerifier [0x0x7ff67d4ee980+77936]
	(No symbol) [0x0x7ff67d2a9cda]
	(No symbol) [0x0x7ff67d2821a1]
	(No symbol) [0x0x7ff67d32fc6e]
	(No symbol) [0x0x7ff67d350432]
	(No symbol) [0x0x7ff67d3286a3]
	(No symbol) [0x0x7ff67d2f1791]
	(No symbol) [0x0x7ff67d2f2523]
	GetHandleVerifier [0x0x7ff67d7c683d+3059501]
	GetHandleVerifier [0x0x7ff67d7c0bfd+3035885]
	GetHandleVerifier [0x0x7ff67d7e03f0+3164896]
	GetHandleVerifier [0x0x7ff67d508c2e+185118]
	GetHandleVerifier [0x0x7ff67d51053f+216111]
	GetHandleVerifier [0x0x7ff67d4f72d4+113092]
	GetHandleVerifier [0x0x7ff67d4f7489+113529]
	GetHandleVerifier [0x0x7ff67d4de288+10616]
	BaseThreadInitThunk [0x0x7ffa9afa7374+20]
	RtlUserThreadStart [0x0x7ffa9b3fcc91+33]

2025-07-17 13:05:51,720 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 13:05:55,494 - INFO - Monitoring stopped by user
2025-07-17 13:05:55,497 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)': /session/86cc0a3b53de451925e758f397ad669f
2025-07-17 13:16:59,297 - INFO - ====== WebDriver manager ======
2025-07-17 13:17:00,698 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-17 13:17:00,840 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-17 13:17:00,935 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.157\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-07-17 13:17:00,975 - WARNING - WebDriver manager failed: [WinError 193] %1 is not a valid Win32 application, trying direct Chrome...
2025-07-17 13:17:03,046 - INFO - Establishing session with Tesla main page...
2025-07-17 13:17:10,605 - INFO - Chrome WebDriver initialized successfully
2025-07-17 13:17:10,615 - INFO - Press Ctrl+R to reinitialize, Ctrl+C to stop
2025-07-17 13:17:10,622 - INFO - Navigating to Tesla inventory page...
2025-07-17 13:17:10,636 - INFO - Loading inventory page...
2025-07-17 13:17:58,965 - WARNING - No inventory results found or page didn't load properly
2025-07-17 13:17:58,965 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 13:18:08,973 - INFO - Navigating to Tesla inventory page...
2025-07-17 13:18:08,983 - INFO - Loading inventory page...
2025-07-17 13:18:40,119 - ERROR - Failed to get inventory data: Message: tab crashed
  (Session info: chrome=138.0.7204.157)
Stacktrace:
	GetHandleVerifier [0x0x7ff67d4ee925+77845]
	GetHandleVerifier [0x0x7ff67d4ee980+77936]
	(No symbol) [0x0x7ff67d2a9b0c]
	(No symbol) [0x0x7ff67d29727b]
	(No symbol) [0x0x7ff67d294eab]
	(No symbol) [0x0x7ff67d29590f]
	(No symbol) [0x0x7ff67d2a453e]
	(No symbol) [0x0x7ff67d2ba4f1]
	(No symbol) [0x0x7ff67d2c165a]
	(No symbol) [0x0x7ff67d2960ad]
	(No symbol) [0x0x7ff67d2b9ce1]
	(No symbol) [0x0x7ff67d3509e1]
	(No symbol) [0x0x7ff67d3286a3]
	(No symbol) [0x0x7ff67d2f1791]
	(No symbol) [0x0x7ff67d2f2523]
	GetHandleVerifier [0x0x7ff67d7c683d+3059501]
	GetHandleVerifier [0x0x7ff67d7c0bfd+3035885]
	GetHandleVerifier [0x0x7ff67d7e03f0+3164896]
	GetHandleVerifier [0x0x7ff67d508c2e+185118]
	GetHandleVerifier [0x0x7ff67d51053f+216111]
	GetHandleVerifier [0x0x7ff67d4f72d4+113092]
	GetHandleVerifier [0x0x7ff67d4f7489+113529]
	GetHandleVerifier [0x0x7ff67d4de288+10616]
	BaseThreadInitThunk [0x0x7ffa9afa7374+20]
	RtlUserThreadStart [0x0x7ffa9b3fcc91+33]

2025-07-17 13:18:40,121 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 13:18:50,135 - INFO - Navigating to Tesla inventory page...
2025-07-17 13:18:50,137 - ERROR - Failed to get inventory data: Message: tab crashed
  (Session info: chrome=138.0.7204.157)
Stacktrace:
	GetHandleVerifier [0x0x7ff67d4ee925+77845]
	GetHandleVerifier [0x0x7ff67d4ee980+77936]
	(No symbol) [0x0x7ff67d2a9b0c]
	(No symbol) [0x0x7ff67d29727b]
	(No symbol) [0x0x7ff67d294eab]
	(No symbol) [0x0x7ff67d29590f]
	(No symbol) [0x0x7ff67d2a453e]
	(No symbol) [0x0x7ff67d2ba4f1]
	(No symbol) [0x0x7ff67d2c165a]
	(No symbol) [0x0x7ff67d2960ad]
	(No symbol) [0x0x7ff67d2b9ce1]
	(No symbol) [0x0x7ff67d3509e1]
	(No symbol) [0x0x7ff67d3286a3]
	(No symbol) [0x0x7ff67d2f1791]
	(No symbol) [0x0x7ff67d2f2523]
	GetHandleVerifier [0x0x7ff67d7c683d+3059501]
	GetHandleVerifier [0x0x7ff67d7c0bfd+3035885]
	GetHandleVerifier [0x0x7ff67d7e03f0+3164896]
	GetHandleVerifier [0x0x7ff67d508c2e+185118]
	GetHandleVerifier [0x0x7ff67d51053f+216111]
	GetHandleVerifier [0x0x7ff67d4f72d4+113092]
	GetHandleVerifier [0x0x7ff67d4f7489+113529]
	GetHandleVerifier [0x0x7ff67d4de288+10616]
	BaseThreadInitThunk [0x0x7ffa9afa7374+20]
	RtlUserThreadStart [0x0x7ffa9b3fcc91+33]

2025-07-17 13:18:50,139 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 13:25:59,875 - INFO - ====== WebDriver manager ======
2025-07-17 13:26:01,094 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-17 13:26:01,254 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-17 13:26:01,356 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.157\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-07-17 13:26:01,360 - WARNING - WebDriver manager failed: [WinError 193] %1 is not a valid Win32 application, trying direct Chrome...
2025-07-17 13:26:03,186 - INFO - Establishing session with Tesla main page...
2025-07-17 13:26:09,336 - INFO - Chrome WebDriver initialized successfully
2025-07-17 13:26:09,346 - INFO - Press Ctrl+R to reinitialize, Ctrl+C to stop
2025-07-17 13:26:09,353 - INFO - Navigating to Tesla inventory like a human user...
2025-07-17 13:26:09,367 - INFO - Looking for Model Y navigation...
2025-07-17 13:26:09,465 - INFO - Model Y link not found, trying direct navigation to Model Y page...
2025-07-17 13:26:17,123 - INFO - Looking for inventory or order button...
2025-07-17 13:26:17,301 - INFO - Inventory button not found, trying direct inventory navigation...
2025-07-17 13:26:25,392 - INFO - Simulating human browsing behavior...
2025-07-17 13:26:29,484 - INFO - Current page title: Access Denied
2025-07-17 13:26:29,485 - ERROR - Access denied detected on inventory page
2025-07-17 13:26:29,485 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 13:26:39,501 - INFO - Navigating to Tesla inventory like a human user...
2025-07-17 13:26:39,511 - INFO - Looking for Model Y navigation...
2025-07-17 13:26:39,586 - INFO - Model Y link not found, trying direct navigation to Model Y page...
2025-07-17 13:26:45,587 - INFO - Looking for inventory or order button...
2025-07-17 13:26:45,691 - INFO - Inventory button not found, trying direct inventory navigation...
2025-07-17 13:26:52,061 - INFO - Simulating human browsing behavior...
2025-07-17 13:26:56,023 - INFO - Current page title: Access Denied
2025-07-17 13:26:56,023 - ERROR - Access denied detected on inventory page
2025-07-17 13:26:56,024 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 13:27:06,042 - INFO - Navigating to Tesla inventory like a human user...
2025-07-17 13:27:06,054 - INFO - Looking for Model Y navigation...
2025-07-17 13:27:06,126 - INFO - Model Y link not found, trying direct navigation to Model Y page...
2025-07-17 13:27:12,340 - INFO - Looking for inventory or order button...
2025-07-17 13:27:12,458 - INFO - Inventory button not found, trying direct inventory navigation...
2025-07-17 13:27:20,292 - INFO - Simulating human browsing behavior...
2025-07-17 13:27:23,878 - INFO - Current page title: Access Denied
2025-07-17 13:27:23,878 - ERROR - Access denied detected on inventory page
2025-07-17 13:27:23,879 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 13:27:33,891 - INFO - Navigating to Tesla inventory like a human user...
2025-07-17 13:27:33,901 - INFO - Looking for Model Y navigation...
2025-07-17 13:27:33,964 - INFO - Model Y link not found, trying direct navigation to Model Y page...
2025-07-17 13:27:39,780 - INFO - Looking for inventory or order button...
2025-07-17 13:27:39,783 - WARNING - Error finding inventory button: Message: tab crashed
  (Session info: chrome=138.0.7204.157)
Stacktrace:
	GetHandleVerifier [0x0x7ff67d4ee925+77845]
	GetHandleVerifier [0x0x7ff67d4ee980+77936]
	(No symbol) [0x0x7ff67d2a9b0c]
	(No symbol) [0x0x7ff67d29727b]
	(No symbol) [0x0x7ff67d294eab]
	(No symbol) [0x0x7ff67d29590f]
	(No symbol) [0x0x7ff67d2a453e]
	(No symbol) [0x0x7ff67d2ba4f1]
	(No symbol) [0x0x7ff67d2c165a]
	(No symbol) [0x0x7ff67d2960ad]
	(No symbol) [0x0x7ff67d2b9ce1]
	(No symbol) [0x0x7ff67d3509e1]
	(No symbol) [0x0x7ff67d3286a3]
	(No symbol) [0x0x7ff67d2f1791]
	(No symbol) [0x0x7ff67d2f2523]
	GetHandleVerifier [0x0x7ff67d7c683d+3059501]
	GetHandleVerifier [0x0x7ff67d7c0bfd+3035885]
	GetHandleVerifier [0x0x7ff67d7e03f0+3164896]
	GetHandleVerifier [0x0x7ff67d508c2e+185118]
	GetHandleVerifier [0x0x7ff67d51053f+216111]
	GetHandleVerifier [0x0x7ff67d4f72d4+113092]
	GetHandleVerifier [0x0x7ff67d4f7489+113529]
	GetHandleVerifier [0x0x7ff67d4de288+10616]
	BaseThreadInitThunk [0x0x7ffa9afa7374+20]
	RtlUserThreadStart [0x0x7ffa9b3fcc91+33]

2025-07-17 13:27:39,785 - INFO - Falling back to direct inventory URL...
2025-07-17 13:27:39,789 - ERROR - Failed to get inventory data: Message: tab crashed
  (Session info: chrome=138.0.7204.157)
Stacktrace:
	GetHandleVerifier [0x0x7ff67d4ee925+77845]
	GetHandleVerifier [0x0x7ff67d4ee980+77936]
	(No symbol) [0x0x7ff67d2a9b0c]
	(No symbol) [0x0x7ff67d29727b]
	(No symbol) [0x0x7ff67d294eab]
	(No symbol) [0x0x7ff67d29590f]
	(No symbol) [0x0x7ff67d2a453e]
	(No symbol) [0x0x7ff67d2ba4f1]
	(No symbol) [0x0x7ff67d2c165a]
	(No symbol) [0x0x7ff67d2960ad]
	(No symbol) [0x0x7ff67d2b9ce1]
	(No symbol) [0x0x7ff67d3509e1]
	(No symbol) [0x0x7ff67d3286a3]
	(No symbol) [0x0x7ff67d2f1791]
	(No symbol) [0x0x7ff67d2f2523]
	GetHandleVerifier [0x0x7ff67d7c683d+3059501]
	GetHandleVerifier [0x0x7ff67d7c0bfd+3035885]
	GetHandleVerifier [0x0x7ff67d7e03f0+3164896]
	GetHandleVerifier [0x0x7ff67d508c2e+185118]
	GetHandleVerifier [0x0x7ff67d51053f+216111]
	GetHandleVerifier [0x0x7ff67d4f72d4+113092]
	GetHandleVerifier [0x0x7ff67d4f7489+113529]
	GetHandleVerifier [0x0x7ff67d4de288+10616]
	BaseThreadInitThunk [0x0x7ffa9afa7374+20]
	RtlUserThreadStart [0x0x7ffa9b3fcc91+33]

2025-07-17 13:27:39,791 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 13:27:49,803 - INFO - Navigating to Tesla inventory like a human user...
2025-07-17 13:27:49,807 - ERROR - Failed to get inventory data: Message: tab crashed
  (Session info: chrome=138.0.7204.157)
Stacktrace:
	GetHandleVerifier [0x0x7ff67d4ee925+77845]
	GetHandleVerifier [0x0x7ff67d4ee980+77936]
	(No symbol) [0x0x7ff67d2a9b0c]
	(No symbol) [0x0x7ff67d29727b]
	(No symbol) [0x0x7ff67d294eab]
	(No symbol) [0x0x7ff67d29590f]
	(No symbol) [0x0x7ff67d2a453e]
	(No symbol) [0x0x7ff67d2ba4f1]
	(No symbol) [0x0x7ff67d2c165a]
	(No symbol) [0x0x7ff67d2960ad]
	(No symbol) [0x0x7ff67d2b9ce1]
	(No symbol) [0x0x7ff67d3509e1]
	(No symbol) [0x0x7ff67d3286a3]
	(No symbol) [0x0x7ff67d2f1791]
	(No symbol) [0x0x7ff67d2f2523]
	GetHandleVerifier [0x0x7ff67d7c683d+3059501]
	GetHandleVerifier [0x0x7ff67d7c0bfd+3035885]
	GetHandleVerifier [0x0x7ff67d7e03f0+3164896]
	GetHandleVerifier [0x0x7ff67d508c2e+185118]
	GetHandleVerifier [0x0x7ff67d51053f+216111]
	GetHandleVerifier [0x0x7ff67d4f72d4+113092]
	GetHandleVerifier [0x0x7ff67d4f7489+113529]
	GetHandleVerifier [0x0x7ff67d4de288+10616]
	BaseThreadInitThunk [0x0x7ffa9afa7374+20]
	RtlUserThreadStart [0x0x7ffa9b3fcc91+33]

2025-07-17 13:27:49,809 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 13:28:47,225 - INFO - ====== WebDriver manager ======
2025-07-17 13:28:48,355 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-17 13:28:48,461 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-17 13:28:48,569 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.157\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-07-17 13:28:48,571 - WARNING - WebDriver manager failed: [WinError 193] %1 is not a valid Win32 application, trying direct Chrome...
2025-07-17 13:28:50,320 - INFO - Establishing session with Tesla main page...
2025-07-17 13:28:56,732 - INFO - Chrome WebDriver initialized successfully
2025-07-17 13:28:56,740 - INFO - Press Ctrl+R to reinitialize, Ctrl+C to stop
2025-07-17 13:28:56,747 - INFO - Navigating to Tesla inventory like a human user...
2025-07-17 13:28:56,763 - INFO - Looking for Model Y navigation...
2025-07-17 13:28:56,863 - INFO - Model Y link not found, trying direct navigation to Model Y page...
2025-07-17 13:29:02,655 - INFO - Looking for available vehicles through different paths...
2025-07-17 13:29:02,831 - INFO - No order/configure button found, trying alternative approach...
2025-07-17 13:29:03,024 - INFO - No inventory links found, trying direct URL approach...
2025-07-17 13:29:03,025 - INFO - Trying inventory URL: https://www.tesla.com/tr_TR/model-y/design#inventory
2025-07-17 13:29:12,063 - INFO - Successfully accessed: https://www.tesla.com/tr_TR/model-y/design#inventory
2025-07-17 13:29:12,064 - INFO - Simulating human browsing behavior...
2025-07-17 13:29:15,868 - INFO - Current page title: | Tesla
2025-07-17 13:29:25,885 - ERROR - Failed to get inventory data: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=138.0.7204.157)
Stacktrace:
	GetHandleVerifier [0x0x7ff67d4ee925+77845]
	GetHandleVerifier [0x0x7ff67d4ee980+77936]
	(No symbol) [0x0x7ff67d2a9cda]
	(No symbol) [0x0x7ff67d2821a1]
	(No symbol) [0x0x7ff67d32fc6e]
	(No symbol) [0x0x7ff67d350432]
	(No symbol) [0x0x7ff67d3286a3]
	(No symbol) [0x0x7ff67d2f1791]
	(No symbol) [0x0x7ff67d2f2523]
	GetHandleVerifier [0x0x7ff67d7c683d+3059501]
	GetHandleVerifier [0x0x7ff67d7c0bfd+3035885]
	GetHandleVerifier [0x0x7ff67d7e03f0+3164896]
	GetHandleVerifier [0x0x7ff67d508c2e+185118]
	GetHandleVerifier [0x0x7ff67d51053f+216111]
	GetHandleVerifier [0x0x7ff67d4f72d4+113092]
	GetHandleVerifier [0x0x7ff67d4f7489+113529]
	GetHandleVerifier [0x0x7ff67d4de288+10616]
	BaseThreadInitThunk [0x0x7ffa9afa7374+20]
	RtlUserThreadStart [0x0x7ffa9b3fcc91+33]

2025-07-17 13:29:25,886 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 13:29:35,891 - INFO - Navigating to Tesla inventory like a human user...
2025-07-17 13:29:35,893 - ERROR - Failed to get inventory data: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=138.0.7204.157)
Stacktrace:
	GetHandleVerifier [0x0x7ff67d4ee925+77845]
	GetHandleVerifier [0x0x7ff67d4ee980+77936]
	(No symbol) [0x0x7ff67d2a9cda]
	(No symbol) [0x0x7ff67d2821a1]
	(No symbol) [0x0x7ff67d32fc6e]
	(No symbol) [0x0x7ff67d350432]
	(No symbol) [0x0x7ff67d3286a3]
	(No symbol) [0x0x7ff67d2f1791]
	(No symbol) [0x0x7ff67d2f2523]
	GetHandleVerifier [0x0x7ff67d7c683d+3059501]
	GetHandleVerifier [0x0x7ff67d7c0bfd+3035885]
	GetHandleVerifier [0x0x7ff67d7e03f0+3164896]
	GetHandleVerifier [0x0x7ff67d508c2e+185118]
	GetHandleVerifier [0x0x7ff67d51053f+216111]
	GetHandleVerifier [0x0x7ff67d4f72d4+113092]
	GetHandleVerifier [0x0x7ff67d4f7489+113529]
	GetHandleVerifier [0x0x7ff67d4de288+10616]
	BaseThreadInitThunk [0x0x7ffa9afa7374+20]
	RtlUserThreadStart [0x0x7ffa9b3fcc91+33]

2025-07-17 13:29:35,895 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 13:29:45,905 - INFO - Navigating to Tesla inventory like a human user...
2025-07-17 13:29:45,908 - ERROR - Failed to get inventory data: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=138.0.7204.157)
Stacktrace:
	GetHandleVerifier [0x0x7ff67d4ee925+77845]
	GetHandleVerifier [0x0x7ff67d4ee980+77936]
	(No symbol) [0x0x7ff67d2a9cda]
	(No symbol) [0x0x7ff67d2821a1]
	(No symbol) [0x0x7ff67d32fc6e]
	(No symbol) [0x0x7ff67d350432]
	(No symbol) [0x0x7ff67d3286a3]
	(No symbol) [0x0x7ff67d2f1791]
	(No symbol) [0x0x7ff67d2f2523]
	GetHandleVerifier [0x0x7ff67d7c683d+3059501]
	GetHandleVerifier [0x0x7ff67d7c0bfd+3035885]
	GetHandleVerifier [0x0x7ff67d7e03f0+3164896]
	GetHandleVerifier [0x0x7ff67d508c2e+185118]
	GetHandleVerifier [0x0x7ff67d51053f+216111]
	GetHandleVerifier [0x0x7ff67d4f72d4+113092]
	GetHandleVerifier [0x0x7ff67d4f7489+113529]
	GetHandleVerifier [0x0x7ff67d4de288+10616]
	BaseThreadInitThunk [0x0x7ffa9afa7374+20]
	RtlUserThreadStart [0x0x7ffa9b3fcc91+33]

2025-07-17 13:29:45,909 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 13:29:55,924 - INFO - Navigating to Tesla inventory like a human user...
2025-07-17 13:29:55,927 - ERROR - Failed to get inventory data: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=138.0.7204.157)
Stacktrace:
	GetHandleVerifier [0x0x7ff67d4ee925+77845]
	GetHandleVerifier [0x0x7ff67d4ee980+77936]
	(No symbol) [0x0x7ff67d2a9cda]
	(No symbol) [0x0x7ff67d2821a1]
	(No symbol) [0x0x7ff67d32fc6e]
	(No symbol) [0x0x7ff67d350432]
	(No symbol) [0x0x7ff67d3286a3]
	(No symbol) [0x0x7ff67d2f1791]
	(No symbol) [0x0x7ff67d2f2523]
	GetHandleVerifier [0x0x7ff67d7c683d+3059501]
	GetHandleVerifier [0x0x7ff67d7c0bfd+3035885]
	GetHandleVerifier [0x0x7ff67d7e03f0+3164896]
	GetHandleVerifier [0x0x7ff67d508c2e+185118]
	GetHandleVerifier [0x0x7ff67d51053f+216111]
	GetHandleVerifier [0x0x7ff67d4f72d4+113092]
	GetHandleVerifier [0x0x7ff67d4f7489+113529]
	GetHandleVerifier [0x0x7ff67d4de288+10616]
	BaseThreadInitThunk [0x0x7ffa9afa7374+20]
	RtlUserThreadStart [0x0x7ffa9b3fcc91+33]

2025-07-17 13:29:55,929 - WARNING - Failed to fetch inventory data, retrying...
2025-07-17 13:30:05,937 - INFO - Navigating to Tesla inventory like a human user...
2025-07-17 13:30:05,940 - ERROR - Failed to get inventory data: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=138.0.7204.157)
Stacktrace:
	GetHandleVerifier [0x0x7ff67d4ee925+77845]
	GetHandleVerifier [0x0x7ff67d4ee980+77936]
	(No symbol) [0x0x7ff67d2a9cda]
	(No symbol) [0x0x7ff67d2821a1]
	(No symbol) [0x0x7ff67d32fc6e]
	(No symbol) [0x0x7ff67d350432]
	(No symbol) [0x0x7ff67d3286a3]
	(No symbol) [0x0x7ff67d2f1791]
	(No symbol) [0x0x7ff67d2f2523]
	GetHandleVerifier [0x0x7ff67d7c683d+3059501]
	GetHandleVerifier [0x0x7ff67d7c0bfd+3035885]
	GetHandleVerifier [0x0x7ff67d7e03f0+3164896]
	GetHandleVerifier [0x0x7ff67d508c2e+185118]
	GetHandleVerifier [0x0x7ff67d51053f+216111]
	GetHandleVerifier [0x0x7ff67d4f72d4+113092]
	GetHandleVerifier [0x0x7ff67d4f7489+113529]
	GetHandleVerifier [0x0x7ff67d4de288+10616]
	BaseThreadInitThunk [0x0x7ffa9afa7374+20]
	RtlUserThreadStart [0x0x7ffa9b3fcc91+33]

2025-07-17 13:30:05,942 - WARNING - Failed to fetch inventory data, retrying...
