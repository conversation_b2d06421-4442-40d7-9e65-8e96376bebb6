#!/usr/bin/env python3
"""
Tesla Bot Test Script
Tests the browser-based inventory monitoring without HTTP requests.
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import re

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_driver():
    """Setup Chrome WebDriver with stealth options."""
    try:
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")

        # Use webdriver-manager to automatically handle ChromeDriver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        logger.info("Chrome WebDriver initialized successfully")
        return driver
    except Exception as e:
        logger.error(f"Failed to setup WebDriver: {e}")
        return None

def test_inventory_scraping():
    """Test scraping Tesla inventory without HTTP requests."""
    driver = setup_driver()
    if not driver:
        logger.error("Failed to initialize WebDriver")
        return

    try:
        # Navigate to Tesla inventory page
        inventory_url = "https://www.tesla.com/tr_TR/inventory/new/my"
        logger.info(f"Navigating to {inventory_url}")
        driver.get(inventory_url)

        # Wait for page to load
        time.sleep(5)

        # Check if page loaded successfully
        title = driver.title
        logger.info(f"Page title: {title}")

        # Try to find inventory elements
        wait = WebDriverWait(driver, 20)

        # Try multiple selectors for vehicle cards
        selectors = [
            "[data-testid='inventory-card']",
            ".result",
            ".inventory-card",
            ".vehicle-card"
        ]

        found_selector = None
        for selector in selectors:
            try:
                wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                vehicle_cards = driver.find_elements(By.CSS_SELECTOR, selector)
                if vehicle_cards:
                    found_selector = selector
                    logger.info(f"Found {len(vehicle_cards)} vehicle cards using selector: {selector}")
                    break
            except TimeoutException:
                continue

        if not found_selector:
            logger.warning("No vehicle cards found on page")
            return

        # Extract some basic info from the first few cards
        vehicle_cards = driver.find_elements(By.CSS_SELECTOR, found_selector)
        for i, card in enumerate(vehicle_cards[:3], 1):
            try:
                card_text = card.text
                logger.info(f"\nVehicle {i} text: {card_text[:100]}...")

                # Try to extract price
                price_text = None
                price_selectors = [
                    "[data-testid='price']",
                    ".price",
                    ".vehicle-price",
                    ".result-price"
                ]

                for price_selector in price_selectors:
                    try:
                        price_element = card.find_element(By.CSS_SELECTOR, price_selector)
                        price_text = price_element.text
                        logger.info(f"Vehicle {i} price: {price_text}")
                        break
                    except NoSuchElementException:
                        continue
            except Exception as e:
                logger.warning(f"Error extracting data from card {i}: {e}")

        logger.info("Test completed successfully")

    except Exception as e:
        logger.error(f"Test failed: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    logger.info("Starting Tesla bot test")
    test_inventory_scraping()
    logger.info("Test completed")