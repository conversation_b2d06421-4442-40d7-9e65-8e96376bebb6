#!/usr/bin/env python3
"""
Tesla Bot Test Script
Tests the browser-based inventory monitoring without HTTP requests.
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import re

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_driver():
    """Setup Chrome WebDriver with stealth options."""
    try:
        chrome_options = Options()

        # Essential stealth options
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # Realistic browser behavior
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_argument("--disable-infobars")
        chrome_options.add_argument("--disable-notifications")

        # User agent (realistic)
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")

        # Additional preferences to appear more human
        prefs = {
            "profile.default_content_setting_values.notifications": 2,
            "profile.default_content_settings.popups": 0,
            "profile.managed_default_content_settings.images": 2
        }
        chrome_options.add_experimental_option("prefs", prefs)

        # Try webdriver-manager first, fallback to direct Chrome
        try:
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            logger.info("Using webdriver-manager ChromeDriver")
        except Exception as e:
            logger.warning(f"WebDriver manager failed: {e}")
            logger.info("Trying direct Chrome...")
            driver = webdriver.Chrome(options=chrome_options)
        # Execute stealth scripts
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        driver.execute_script("Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})")
        driver.execute_script("Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en', 'tr']})")
        driver.execute_script("window.chrome = { runtime: {} }")

        # First navigate to Tesla main page to establish session
        logger.info("Establishing session with Tesla main page...")
        driver.get("https://www.tesla.com/tr_TR")
        time.sleep(3)

        logger.info("Chrome WebDriver initialized successfully")
        return driver
    except Exception as e:
        logger.error(f"Failed to setup WebDriver: {e}")
        return None

def test_inventory_scraping():
    """Test scraping Tesla inventory without HTTP requests."""
    driver = setup_driver()
    if not driver:
        logger.error("Failed to initialize WebDriver")
        return

    try:
        # Navigate to Tesla inventory page
        inventory_url = "https://www.tesla.com/tr_TR/inventory/new/my"
        logger.info(f"Navigating to {inventory_url}")
        driver.get(inventory_url)

        # Wait for page to load
        time.sleep(5)

        # Check if page loaded successfully
        title = driver.title
        logger.info(f"Page title: {title}")

        # Check page content
        page_source_snippet = driver.page_source[:500]
        logger.info(f"Page source snippet: {page_source_snippet}")

        # Check for access denied or other blocking
        if "access denied" in title.lower() or "access denied" in driver.page_source.lower():
            logger.error("Access denied detected!")
            return

        # Try to find inventory elements
        wait = WebDriverWait(driver, 20)

        # Try multiple selectors for vehicle cards
        selectors = [
            "[data-testid='inventory-card']",
            ".result",
            ".inventory-card",
            ".vehicle-card"
        ]

        found_selector = None
        for selector in selectors:
            try:
                wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                vehicle_cards = driver.find_elements(By.CSS_SELECTOR, selector)
                if vehicle_cards:
                    found_selector = selector
                    logger.info(f"Found {len(vehicle_cards)} vehicle cards using selector: {selector}")
                    break
            except TimeoutException:
                continue

        if not found_selector:
            logger.warning("No vehicle cards found on page")
            return

        # Extract some basic info from the first few cards
        vehicle_cards = driver.find_elements(By.CSS_SELECTOR, found_selector)
        for i, card in enumerate(vehicle_cards[:3], 1):
            try:
                card_text = card.text
                logger.info(f"\nVehicle {i} text: {card_text[:100]}...")

                # Try to extract price
                price_text = None
                price_selectors = [
                    "[data-testid='price']",
                    ".price",
                    ".vehicle-price",
                    ".result-price"
                ]

                for price_selector in price_selectors:
                    try:
                        price_element = card.find_element(By.CSS_SELECTOR, price_selector)
                        price_text = price_element.text
                        logger.info(f"Vehicle {i} price: {price_text}")
                        break
                    except NoSuchElementException:
                        continue
            except Exception as e:
                logger.warning(f"Error extracting data from card {i}: {e}")

        logger.info("Test completed successfully")

    except Exception as e:
        logger.error(f"Test failed: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    logger.info("Starting Tesla bot test")
    test_inventory_scraping()
    logger.info("Test completed")