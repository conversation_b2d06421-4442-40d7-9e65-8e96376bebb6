#!/usr/bin/env python3
"""
Tesla Bot Test Script
Tests the browser-based inventory monitoring without HTTP requests.
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import re

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_driver():
    """Setup Chrome WebDriver with stealth options."""
    try:
        chrome_options = Options()

        # Essential stealth options
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # Realistic browser behavior
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_argument("--disable-infobars")
        chrome_options.add_argument("--disable-notifications")

        # User agent (realistic)
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")

        # Additional preferences to appear more human
        prefs = {
            "profile.default_content_setting_values.notifications": 2,
            "profile.default_content_settings.popups": 0,
            "profile.managed_default_content_settings.images": 2
        }
        chrome_options.add_experimental_option("prefs", prefs)

        # Try webdriver-manager first, fallback to direct Chrome
        try:
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            logger.info("Using webdriver-manager ChromeDriver")
        except Exception as e:
            logger.warning(f"WebDriver manager failed: {e}")
            logger.info("Trying direct Chrome...")
            driver = webdriver.Chrome(options=chrome_options)
        # Execute stealth scripts
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        driver.execute_script("Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})")
        driver.execute_script("Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en', 'tr']})")
        driver.execute_script("window.chrome = { runtime: {} }")

        # First navigate to Tesla main page to establish session
        logger.info("Establishing session with Tesla main page...")
        driver.get("https://www.tesla.com/tr_TR")
        time.sleep(3)

        logger.info("Chrome WebDriver initialized successfully")
        return driver
    except Exception as e:
        logger.error(f"Failed to setup WebDriver: {e}")
        return None

def test_inventory_scraping():
    """Test scraping Tesla inventory without HTTP requests."""
    driver = setup_driver()
    if not driver:
        logger.error("Failed to initialize WebDriver")
        return

    try:
        # Test multiple Tesla URLs to see which ones work
        test_urls = [
            "https://www.tesla.com/tr_tr/modely/design#overview",
            "https://www.tesla.com/tr_TR/modely/design#overview",
            "https://www.tesla.com/tr_TR/model-y/design#inventory",
            "https://www.tesla.com/tr_TR/model-y/design#overview",
            "https://www.tesla.com/tr_TR/inventory/new/my"
        ]

        for url in test_urls:
            logger.info(f"Testing URL: {url}")
            try:
                driver.get(url)
                time.sleep(5)  # Wait for page to load

                title = driver.title
                logger.info(f"Page title: {title}")

                # Check for access denied
                if "access denied" in title.lower():
                    logger.error(f"❌ Access denied for: {url}")
                elif "tesla" in title.lower():
                    logger.info(f"✅ Successfully accessed: {url}")

                    # Try to find some content to verify it's working
                    page_text = driver.page_source[:1000]
                    if "model" in page_text.lower() or "tesla" in page_text.lower():
                        logger.info("✅ Page contains Tesla content")
                    else:
                        logger.warning("⚠️ Page may be blocked or empty")

                    break  # Stop at first successful URL
                else:
                    logger.warning(f"⚠️ Unexpected page title for {url}: {title}")

            except Exception as e:
                logger.error(f"❌ Error accessing {url}: {e}")

            time.sleep(2)  # Small delay between tests

        logger.info("URL testing completed")

    except Exception as e:
        logger.error(f"Test failed: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    logger.info("Starting Tesla bot test")
    test_inventory_scraping()
    logger.info("Test completed")