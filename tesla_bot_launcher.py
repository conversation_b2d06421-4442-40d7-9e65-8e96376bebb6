#!/usr/bin/env python3
"""
Tesla Bot Launcher
Main launcher script for Tesla inventory monitoring bots
"""

import sys
import os
import subprocess
from typing import Optional

def check_dependencies() -> bool:
    """Check if required dependencies are installed"""
    required_packages = [
        'requests',
        'keyboard'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n📦 Install missing packages with:")
        print("   pip install -r requirements.txt")
        return False
    
    return True

def check_selenium_for_auto_bot() -> bool:
    """Check if Selenium is available for auto bot"""
    try:
        import selenium
        from selenium import webdriver
        return True
    except ImportError:
        print("⚠️  Selenium not found. Auto bot will not be available.")
        print("   Install with: pip install selenium")
        print("   Also download ChromeDriver from: https://chromedriver.chromium.org/")
        return False

def show_menu():
    """Display bot selection menu"""
    print("🚗 Tesla Bot Launcher")
    print("=" * 50)
    print("⚠️  IMPORTANT: Tesla blocks ALL automation. Use option 2 (Manual Guide)!")
    print()
    print("Choose your Tesla monitoring method:")
    print()
    print("1. 📖 Manual Monitoring Guide (RECOMMENDED)")
    print("   - 100% reliable - no blocking possible")
    print("   - Timer reminders every 30 seconds")
    print("   - Opens real browser for you")
    print("   - Step-by-step ordering instructions")
    print()
    print("2. 🥷 Stealth Monitor (EXPERIMENTAL)")
    print("   - Advanced anti-detection measures")
    print("   - Human-like behavior simulation")
    print("   - May still be blocked by Tesla")
    print()
    print("3. 🔬 Browser Content Debug")
    print("   - Shows what bot sees in browser")
    print("   - Helps understand Tesla's blocking")
    print("   - Technical analysis tool")
    print()
    print("4. 🌐 Original Monitor Bot (BLOCKED)")
    print("   - ⚠️  Currently blocked by Tesla")
    print("   - Kept for reference only")
    print()
    print("5. 🤖 Auto Bot (BLOCKED)")
    print("   - ⚠️  Currently blocked by Tesla")
    print("   - Kept for reference only")
    print()
    print("6. ⚙️  Configure Settings")
    print("   - Edit configuration file")
    print()
    print("7. 📋 View Current Configuration")
    print("   - Display current settings")
    print()
    print("8. ❌ Exit")
    print("=" * 50)

def configure_settings():
    """Open configuration file for editing"""
    config_file = "config.py"
    
    if os.path.exists(config_file):
        print(f"📝 Opening {config_file} for editing...")
        
        # Try to open with default editor
        try:
            if sys.platform.startswith('win'):
                os.startfile(config_file)
            elif sys.platform.startswith('darwin'):  # macOS
                subprocess.call(['open', config_file])
            else:  # Linux
                subprocess.call(['xdg-open', config_file])
            
            print("✅ Configuration file opened in default editor.")
            print("💡 Make sure to save your changes before running the bot.")
            
        except Exception as e:
            print(f"❌ Could not open file automatically: {e}")
            print(f"📁 Please manually edit: {os.path.abspath(config_file)}")
    else:
        print(f"❌ Configuration file not found: {config_file}")

def view_configuration():
    """Display current configuration"""
    try:
        from config import get_config
        config = get_config()
        
        print("📋 Current Configuration:")
        print("=" * 50)
        
        # Target vehicle info
        target = config["target_vehicle"]
        print(f"🎯 Target Vehicle:")
        print(f"   Model: {target['model'].upper()}")
        print(f"   Condition: {target['condition'].title()}")
        print(f"   Keywords: {', '.join(target['variant_keywords'])}")
        print(f"   Target Price: {target['target_price']:,} {target['currency']}")
        print(f"   Price Tolerance: ±{target['max_price_tolerance']:,} {target['currency']}")
        
        # Monitoring settings
        monitoring = config["monitoring"]
        print(f"\n⏱️  Monitoring Settings:")
        print(f"   Check Interval: {monitoring['check_interval']} seconds")
        print(f"   Max Retries: {monitoring['max_retries']}")
        print(f"   Timeout: {monitoring['timeout']} seconds")
        print(f"   Notification Sound: {'✅' if monitoring['notification_sound'] else '❌'}")
        
        # Order form info (if configured)
        order_form = config["order_form"]
        personal_info = order_form["personal_info"]
        print(f"\n👤 Personal Information:")
        print(f"   Name: {personal_info.get('first_name', 'Not set')} {personal_info.get('last_name', 'Not set')}")
        print(f"   Email: {personal_info.get('email', 'Not set')}")
        print(f"   Phone: {personal_info.get('phone', 'Not set')}")
        print(f"   Address: {personal_info.get('address', 'Not set')}")
        
        # Payment info
        payment_info = order_form["payment_info"]
        print(f"\n💳 Payment Settings:")
        print(f"   Preorder Amount: {payment_info['preorder_amount']:,} TL")
        print(f"   Auto Fill Payment: {'✅' if payment_info['auto_fill_payment'] else '❌'}")
        
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ Error reading configuration: {e}")

def run_monitor_bot():
    """Run the monitor bot"""
    print("🌐 Starting Monitor Bot...")
    print("💡 This bot will open your browser when a vehicle is found.")
    print()
    
    try:
        from tesla_bot_monitor import main
        main()
    except ImportError as e:
        print(f"❌ Error importing monitor bot: {e}")
    except Exception as e:
        print(f"❌ Error running monitor bot: {e}")

def run_auto_bot():
    """Run the auto bot"""
    if not check_selenium_for_auto_bot():
        input("Press Enter to return to menu...")
        return

    print("🤖 Starting Auto Bot...")
    print("⚠️  WARNING: This bot will automatically fill order forms!")
    print("⚠️  Tesla may block this - consider using Browser Monitor instead!")
    print()

    # Check if personal info is configured
    try:
        from config import get_config
        config = get_config()
        personal_info = config["order_form"]["personal_info"]

        required_fields = ["first_name", "last_name", "email", "phone"]
        missing_fields = [field for field in required_fields if not personal_info.get(field)]

        if missing_fields:
            print(f"❌ Missing required personal information: {', '.join(missing_fields)}")
            print("📝 Please configure your personal information first (Option 5).")
            input("Press Enter to return to menu...")
            return

    except Exception as e:
        print(f"❌ Error checking configuration: {e}")
        input("Press Enter to return to menu...")
        return

    try:
        from tesla_bot_auto import main
        main()
    except ImportError as e:
        print(f"❌ Error importing auto bot: {e}")
    except Exception as e:
        print(f"❌ Error running auto bot: {e}")

def run_browser_monitor():
    """Run the browser monitor"""
    if not check_selenium_for_auto_bot():
        input("Press Enter to return to menu...")
        return

    print("🔧 Starting Browser Monitor...")
    print("💡 This uses a real browser to bypass Tesla's anti-bot protection.")
    print("⚠️  A Chrome browser window will open - do not close it!")
    print()

    try:
        from tesla_browser_monitor import main
        main()
    except ImportError as e:
        print(f"❌ Error importing browser monitor: {e}")
    except Exception as e:
        print(f"❌ Error running browser monitor: {e}")

def run_manual_guide():
    """Run the manual monitoring guide"""
    print("📖 Starting Manual Monitoring Guide...")
    print("💡 This is the RECOMMENDED approach - 100% reliable!")
    print("💡 Tesla cannot block manual monitoring.")
    print()

    try:
        from tesla_manual_guide import main
        main()
    except ImportError as e:
        print(f"❌ Error importing manual guide: {e}")
    except Exception as e:
        print(f"❌ Error running manual guide: {e}")

def run_stealth_monitor():
    """Run the stealth monitor"""
    print("🥷 Starting Stealth Monitor...")
    print("💡 This uses advanced anti-detection measures.")
    print("⚠️  May still be blocked by Tesla's sophisticated protection.")
    print()

    if not check_selenium_for_auto_bot():
        input("Press Enter to return to menu...")
        return

    try:
        from tesla_stealth_monitor import main
        main()
    except ImportError as e:
        print(f"❌ Error importing stealth monitor: {e}")
    except Exception as e:
        print(f"❌ Error running stealth monitor: {e}")

def run_browser_debug():
    """Run the browser content debug"""
    print("🔬 Starting Browser Content Debug...")
    print("💡 This shows exactly what the bot sees when accessing Tesla.")
    print("💡 Useful for understanding Tesla's blocking mechanisms.")
    print()

    if not check_selenium_for_auto_bot():
        input("Press Enter to return to menu...")
        return

    try:
        from debug_browser_content import main
        main()
    except ImportError as e:
        print(f"❌ Error importing browser debug: {e}")
    except Exception as e:
        print(f"❌ Error running browser debug: {e}")

def main():
    """Main launcher function"""
    print("🚗 Tesla Bot for Turkey - Juniper Model Y Monitoring")
    print("Version 1.0 - Created for Turkish Tesla inventory")
    print()
    
    # Check dependencies
    if not check_dependencies():
        input("Press Enter to exit...")
        return
    
    while True:
        try:
            show_menu()
            choice = input("Enter your choice (1-8): ").strip()

            if choice == "1":
                print()
                run_manual_guide()
                print()
                input("Press Enter to return to menu...")

            elif choice == "2":
                print()
                run_stealth_monitor()
                print()
                input("Press Enter to return to menu...")

            elif choice == "3":
                print()
                run_browser_debug()
                print()
                input("Press Enter to return to menu...")

            elif choice == "4":
                print()
                print("⚠️  WARNING: This bot is currently blocked by Tesla!")
                print("💡 Use Manual Monitoring Guide (option 1) instead.")
                response = input("Continue anyway for testing? (y/n): ").strip().lower()
                if response in ['y', 'yes']:
                    run_monitor_bot()
                print()
                input("Press Enter to return to menu...")

            elif choice == "5":
                print()
                print("⚠️  WARNING: This bot is currently blocked by Tesla!")
                print("💡 Use Manual Monitoring Guide (option 1) instead.")
                response = input("Continue anyway for testing? (y/n): ").strip().lower()
                if response in ['y', 'yes']:
                    run_auto_bot()
                print()
                input("Press Enter to return to menu...")

            elif choice == "6":
                print()
                configure_settings()
                print()
                input("Press Enter to return to menu...")

            elif choice == "7":
                print()
                view_configuration()
                print()
                input("Press Enter to return to menu...")

            elif choice == "8":
                print("👋 Thank you for using Tesla Bot!")
                break

            else:
                print("❌ Invalid choice. Please enter 1-8.")
                input("Press Enter to continue...")
            
            # Clear screen (Windows)
            if sys.platform.startswith('win'):
                os.system('cls')
            else:
                os.system('clear')
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            input("Press Enter to continue...")

if __name__ == "__main__":
    main()
