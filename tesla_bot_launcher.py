#!/usr/bin/env python3
"""
Tesla Bot Launcher
Main launcher script for Tesla inventory monitoring bots
"""

import sys
import os
import subprocess
from typing import Optional

def check_dependencies() -> bool:
    """Check if required dependencies are installed"""
    required_packages = [
        'requests',
        'keyboard'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n📦 Install missing packages with:")
        print("   pip install -r requirements.txt")
        return False
    
    return True

def check_selenium_for_auto_bot() -> bool:
    """Check if Selenium is available for auto bot"""
    try:
        import selenium
        from selenium import webdriver
        return True
    except ImportError:
        print("⚠️  Selenium not found. Auto bot will not be available.")
        print("   Install with: pip install selenium")
        print("   Also download ChromeDriver from: https://chromedriver.chromium.org/")
        return False

def show_menu():
    """Display bot selection menu"""
    print("🚗 Tesla Bot Launcher")
    print("=" * 50)
    print("Choose your Tesla bot version:")
    print()
    print("1. 🌐 Monitor Bot (Browser Version)")
    print("   - Monitors inventory continuously")
    print("   - Opens order page in browser when vehicle found")
    print("   - Manual form completion")
    print()
    print("2. 🤖 Auto Bot (Full Automation)")
    print("   - Monitors inventory continuously") 
    print("   - Automatically fills order forms")
    print("   - Requires personal info configuration")
    print()
    print("3. ⚙️  Configure Settings")
    print("   - Edit configuration file")
    print()
    print("4. 📋 View Current Configuration")
    print("   - Display current settings")
    print()
    print("5. ❌ Exit")
    print("=" * 50)

def configure_settings():
    """Open configuration file for editing"""
    config_file = "config.py"
    
    if os.path.exists(config_file):
        print(f"📝 Opening {config_file} for editing...")
        
        # Try to open with default editor
        try:
            if sys.platform.startswith('win'):
                os.startfile(config_file)
            elif sys.platform.startswith('darwin'):  # macOS
                subprocess.call(['open', config_file])
            else:  # Linux
                subprocess.call(['xdg-open', config_file])
            
            print("✅ Configuration file opened in default editor.")
            print("💡 Make sure to save your changes before running the bot.")
            
        except Exception as e:
            print(f"❌ Could not open file automatically: {e}")
            print(f"📁 Please manually edit: {os.path.abspath(config_file)}")
    else:
        print(f"❌ Configuration file not found: {config_file}")

def view_configuration():
    """Display current configuration"""
    try:
        from config import get_config
        config = get_config()
        
        print("📋 Current Configuration:")
        print("=" * 50)
        
        # Target vehicle info
        target = config["target_vehicle"]
        print(f"🎯 Target Vehicle:")
        print(f"   Model: {target['model'].upper()}")
        print(f"   Condition: {target['condition'].title()}")
        print(f"   Keywords: {', '.join(target['variant_keywords'])}")
        print(f"   Target Price: {target['target_price']:,} {target['currency']}")
        print(f"   Price Tolerance: ±{target['max_price_tolerance']:,} {target['currency']}")
        
        # Monitoring settings
        monitoring = config["monitoring"]
        print(f"\n⏱️  Monitoring Settings:")
        print(f"   Check Interval: {monitoring['check_interval']} seconds")
        print(f"   Max Retries: {monitoring['max_retries']}")
        print(f"   Timeout: {monitoring['timeout']} seconds")
        print(f"   Notification Sound: {'✅' if monitoring['notification_sound'] else '❌'}")
        
        # Order form info (if configured)
        order_form = config["order_form"]
        personal_info = order_form["personal_info"]
        print(f"\n👤 Personal Information:")
        print(f"   Name: {personal_info.get('first_name', 'Not set')} {personal_info.get('last_name', 'Not set')}")
        print(f"   Email: {personal_info.get('email', 'Not set')}")
        print(f"   Phone: {personal_info.get('phone', 'Not set')}")
        print(f"   Address: {personal_info.get('address', 'Not set')}")
        
        # Payment info
        payment_info = order_form["payment_info"]
        print(f"\n💳 Payment Settings:")
        print(f"   Preorder Amount: {payment_info['preorder_amount']:,} TL")
        print(f"   Auto Fill Payment: {'✅' if payment_info['auto_fill_payment'] else '❌'}")
        
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ Error reading configuration: {e}")

def run_monitor_bot():
    """Run the monitor bot"""
    print("🌐 Starting Monitor Bot...")
    print("💡 This bot will open your browser when a vehicle is found.")
    print()
    
    try:
        from tesla_bot_monitor import main
        main()
    except ImportError as e:
        print(f"❌ Error importing monitor bot: {e}")
    except Exception as e:
        print(f"❌ Error running monitor bot: {e}")

def run_auto_bot():
    """Run the auto bot"""
    if not check_selenium_for_auto_bot():
        input("Press Enter to return to menu...")
        return
    
    print("🤖 Starting Auto Bot...")
    print("⚠️  WARNING: This bot will automatically fill order forms!")
    print("⚠️  Make sure your personal information is configured correctly!")
    print()
    
    # Check if personal info is configured
    try:
        from config import get_config
        config = get_config()
        personal_info = config["order_form"]["personal_info"]
        
        required_fields = ["first_name", "last_name", "email", "phone"]
        missing_fields = [field for field in required_fields if not personal_info.get(field)]
        
        if missing_fields:
            print(f"❌ Missing required personal information: {', '.join(missing_fields)}")
            print("📝 Please configure your personal information first (Option 3).")
            input("Press Enter to return to menu...")
            return
            
    except Exception as e:
        print(f"❌ Error checking configuration: {e}")
        input("Press Enter to return to menu...")
        return
    
    try:
        from tesla_bot_auto import main
        main()
    except ImportError as e:
        print(f"❌ Error importing auto bot: {e}")
    except Exception as e:
        print(f"❌ Error running auto bot: {e}")

def main():
    """Main launcher function"""
    print("🚗 Tesla Bot for Turkey - Juniper Model Y Monitoring")
    print("Version 1.0 - Created for Turkish Tesla inventory")
    print()
    
    # Check dependencies
    if not check_dependencies():
        input("Press Enter to exit...")
        return
    
    while True:
        try:
            show_menu()
            choice = input("Enter your choice (1-5): ").strip()
            
            if choice == "1":
                print()
                run_monitor_bot()
                print()
                input("Press Enter to return to menu...")
                
            elif choice == "2":
                print()
                run_auto_bot()
                print()
                input("Press Enter to return to menu...")
                
            elif choice == "3":
                print()
                configure_settings()
                print()
                input("Press Enter to return to menu...")
                
            elif choice == "4":
                print()
                view_configuration()
                print()
                input("Press Enter to return to menu...")
                
            elif choice == "5":
                print("👋 Thank you for using Tesla Bot!")
                break
                
            else:
                print("❌ Invalid choice. Please enter 1-5.")
                input("Press Enter to continue...")
            
            # Clear screen (Windows)
            if sys.platform.startswith('win'):
                os.system('cls')
            else:
                os.system('clear')
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            input("Press Enter to continue...")

if __name__ == "__main__":
    main()
