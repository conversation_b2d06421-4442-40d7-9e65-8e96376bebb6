#!/usr/bin/env python3
"""
Test Access Difference
Shows the exact difference between manual guide and bot automation
"""

import time
import webbrowser

def test_manual_guide_approach():
    """Test how manual guide accesses Tesla (should work)"""
    print("🧪 Testing Manual Guide Approach")
    print("=" * 50)
    
    print("📖 Manual guide uses: webbrowser.open()")
    print("💡 This opens the URL in YOUR existing browser")
    print("💡 <PERSON><PERSON> sees this as a real human user")
    print()
    
    url = "https://www.tesla.com/tr_TR/inventory/new/my"
    
    try:
        print(f"🌐 Opening: {url}")
        print("⏳ This will open in your default browser...")
        
        # This is exactly what manual guide does
        webbrowser.open(url)
        
        print("✅ URL opened in browser successfully!")
        print("💡 Check your browser - you should see the Tesla inventory page")
        print("💡 This works because <PERSON><PERSON> sees your real browser, not automation")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to open browser: {e}")
        return False

def test_bot_automation_approach():
    """Test how bot automation accesses <PERSON><PERSON> (gets blocked)"""
    print("\n🤖 Testing Bot Automation Approach")
    print("=" * 50)
    
    print("🔧 Bot automation uses: selenium webdriver")
    print("💡 This creates a new automated browser instance")
    print("💡 Tesla detects this as bot automation and blocks it")
    print()
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        print("✅ Selenium available - testing bot approach...")
        
        # Setup browser (same as bots do)
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # Run in background
        
        print("🔧 Creating automated browser instance...")
        driver = webdriver.Chrome(options=chrome_options)
        
        url = "https://www.tesla.com/tr_TR/inventory/new/my"
        print(f"🌐 Bot navigating to: {url}")
        
        # This is what bots do - gets blocked
        driver.get(url)
        time.sleep(3)
        
        # Check what we got
        title = driver.title
        current_url = driver.current_url
        page_source = driver.page_source
        
        print(f"📄 Page title: {title}")
        print(f"📍 Current URL: {current_url}")
        print(f"📊 Page size: {len(page_source)} characters")
        
        # Check if blocked
        if "access denied" in title.lower() or "access denied" in page_source.lower():
            print("❌ BOT BLOCKED: Tesla returned 'Access Denied' page")
            print("💡 This is why bots don't work - Tesla detects automation")
        elif len(page_source) < 1000:
            print("⚠️  BOT BLOCKED: Page too small, likely an error page")
        else:
            print("✅ BOT SUCCESS: Somehow got through (unexpected!)")
        
        # Show first 200 characters of what bot sees
        print(f"\n📝 First 200 characters of what bot sees:")
        print("-" * 50)
        print(page_source[:200])
        print("-" * 50)
        
        driver.quit()
        return True
        
    except ImportError:
        print("❌ Selenium not installed - cannot test bot approach")
        print("💡 Install with: pip install selenium")
        return False
    except Exception as e:
        print(f"❌ Bot test failed: {e}")
        return False

def main():
    """Main comparison test"""
    print("🔍 Tesla Access Method Comparison")
    print("=" * 60)
    print("This test shows WHY manual guide works but bots don't")
    print("=" * 60)
    
    # Test 1: Manual guide approach
    manual_success = test_manual_guide_approach()
    
    # Wait a moment
    input("\nPress Enter to test bot automation approach...")
    
    # Test 2: Bot automation approach  
    bot_success = test_bot_automation_approach()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 COMPARISON RESULTS")
    print("=" * 60)
    
    print(f"📖 Manual Guide (webbrowser.open): {'✅ WORKS' if manual_success else '❌ FAILED'}")
    print(f"🤖 Bot Automation (selenium): {'✅ WORKS' if bot_success else '❌ BLOCKED'}")
    
    print("\n💡 KEY DIFFERENCES:")
    print("=" * 30)
    
    print("📖 Manual Guide:")
    print("   • Uses YOUR existing browser")
    print("   • Uses YOUR browser session/cookies")
    print("   • Tesla sees: Real human user")
    print("   • Result: ✅ Access granted")
    
    print("\n🤖 Bot Automation:")
    print("   • Creates NEW automated browser")
    print("   • No existing session/cookies")
    print("   • Tesla sees: Bot automation")
    print("   • Result: ❌ Access denied")
    
    print("\n🎯 CONCLUSION:")
    print("=" * 20)
    print("Tesla can distinguish between:")
    print("• Real browser usage (manual guide) ✅")
    print("• Automated browser usage (bots) ❌")
    print()
    print("This is why manual monitoring is the most reliable approach!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
