"""
Tesla Bot Utilities
Shared utility functions for Tesla inventory monitoring bots
"""

import json
import logging
import os
import time
import requests
import webbrowser
from datetime import datetime
from typing import Dict, List, Optional, Any
from urllib.parse import urljoin
import winsound
from config import get_config, PATHS, LOGGING_CONFIG

def setup_logging(bot_name: str = "tesla_bot") -> logging.Logger:
    """Setup logging configuration"""
    config = LOGGING_CONFIG
    
    # Create logs directory
    os.makedirs(PATHS["logs"], exist_ok=True)
    
    # Setup logger
    logger = logging.getLogger(bot_name)
    logger.setLevel(getattr(logging, config.get("log_level", "INFO")))
    
    # File handler
    log_file = os.path.join(PATHS["logs"], f"{bot_name}_{datetime.now().strftime('%Y%m%d')}.log")
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # Formatter
    formatter = logging.Formatter(
        config["format"],
        datefmt=config["date_format"]
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # Add handlers
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

def play_notification_sound():
    """Play notification sound when vehicle is found"""
    try:
        # Play Windows system sound
        winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
        time.sleep(0.5)
        winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
    except Exception:
        # Fallback: print bell character
        print("\a\a\a")

def save_response(data: Dict, filename: str):
    """Save API response to file for debugging"""
    os.makedirs(PATHS["responses"], exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filepath = os.path.join(PATHS["responses"], f"{timestamp}_{filename}.json")
    
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    except Exception as e:
        print(f"Failed to save response: {e}")

def make_api_request(url: str, params: Dict = None, headers: Dict = None, timeout: int = 30) -> Optional[Dict]:
    """Make API request with error handling and retries"""
    config = get_config()
    default_headers = config["headers"]
    
    if headers:
        request_headers = {**default_headers, **headers}
    else:
        request_headers = default_headers
    
    max_retries = config["monitoring"]["max_retries"]
    retry_delay = config["monitoring"]["retry_delay"]
    
    for attempt in range(max_retries):
        try:
            response = requests.get(
                url,
                params=params,
                headers=request_headers,
                timeout=timeout
            )
            response.raise_for_status()
            
            data = response.json()
            
            # Save response if enabled
            if config["monitoring"]["save_responses"]:
                save_response(data, "inventory_response")
            
            return data
            
        except requests.exceptions.RequestException as e:
            if attempt < max_retries - 1:
                print(f"Request failed (attempt {attempt + 1}/{max_retries}): {e}")
                time.sleep(retry_delay)
            else:
                print(f"All retry attempts failed: {e}")
                return None
        except json.JSONDecodeError as e:
            print(f"Failed to parse JSON response: {e}")
            return None
    
    return None

def check_vehicle_availability(api_response: Dict) -> List[Dict]:
    """Check if target vehicles are available in API response"""
    config = get_config()
    target = config["target_vehicle"]
    
    if not api_response or "results" not in api_response:
        return []
    
    matching_vehicles = []
    
    for vehicle in api_response.get("results", []):
        # Check basic criteria
        if (vehicle.get("Model", "").lower() != target["model"] or
            vehicle.get("TitleStatus", "").lower() != target["condition"]):
            continue
        
        # Check price
        price = vehicle.get("Price", 0)
        if not price:
            continue
            
        price_diff = abs(price - target["target_price"])
        if price_diff <= target["max_price_tolerance"]:
            # Check for Juniper keywords in vehicle details
            vehicle_text = json.dumps(vehicle).lower()
            if any(keyword.lower() in vehicle_text for keyword in target["variant_keywords"]):
                matching_vehicles.append({
                    "vehicle_data": vehicle,
                    "price": price,
                    "price_difference": price_diff,
                    "vin": vehicle.get("VIN", ""),
                    "trim": vehicle.get("TRIM", ""),
                    "paint": vehicle.get("PAINT", ""),
                    "interior": vehicle.get("INTERIOR", ""),
                    "wheels": vehicle.get("WHEELS", ""),
                    "availability": vehicle.get("AvailabilityStatus", ""),
                    "location": vehicle.get("DeliveryLocation", "")
                })
    
    return matching_vehicles

def open_order_page(vehicle_data: Dict = None):
    """Open Tesla order page in browser"""
    config = get_config()
    
    if vehicle_data and "VIN" in vehicle_data:
        # Open specific vehicle page if VIN is available
        vin = vehicle_data["VIN"]
        url = f"{config['urls']['base']}/tr_TR/new/{vin}"
    else:
        # Open general Model Y design page
        url = config["urls"]["order"]
    
    try:
        webbrowser.open(url)
        print(f"🌐 Opened browser: {url}")
        return True
    except Exception as e:
        print(f"Failed to open browser: {e}")
        return False

def format_vehicle_info(vehicle: Dict) -> str:
    """Format vehicle information for display"""
    info = []
    info.append(f"Price: {vehicle['price']:,} TL")
    
    if vehicle.get('trim'):
        info.append(f"Trim: {vehicle['trim']}")
    if vehicle.get('paint'):
        info.append(f"Color: {vehicle['paint']}")
    if vehicle.get('interior'):
        info.append(f"Interior: {vehicle['interior']}")
    if vehicle.get('wheels'):
        info.append(f"Wheels: {vehicle['wheels']}")
    if vehicle.get('availability'):
        info.append(f"Status: {vehicle['availability']}")
    if vehicle.get('location'):
        info.append(f"Location: {vehicle['location']}")
    if vehicle.get('vin'):
        info.append(f"VIN: {vehicle['vin']}")
    
    return " | ".join(info)

def get_user_confirmation(message: str, short_prompt: bool = True) -> bool:
    """Get user confirmation with keyboard input"""
    if short_prompt:
        prompt = f"{message} (y/n): "
        valid_yes = ['y', 'yes']
        valid_no = ['n', 'no']
    else:
        prompt = f"{message} (YES/NO): "
        valid_yes = ['yes']
        valid_no = ['no']
    
    while True:
        try:
            response = input(prompt).strip().lower()
            if response in valid_yes:
                return True
            elif response in valid_no:
                return False
            else:
                print("Please enter a valid response.")
        except KeyboardInterrupt:
            print("\nOperation cancelled by user.")
            return False

def wait_with_countdown(seconds: int, message: str = ""):
    """Wait with countdown display"""
    for i in range(seconds, 0, -1):
        print(f"\r{message} {i}s remaining...", end="", flush=True)
        time.sleep(1)
    print("\r" + " " * 50 + "\r", end="")  # Clear countdown line

def create_session() -> requests.Session:
    """Create a requests session with default configuration"""
    config = get_config()
    session = requests.Session()
    session.headers.update(config["headers"])
    return session

def log_vehicle_found(logger: logging.Logger, vehicles: List[Dict]):
    """Log when vehicles are found"""
    for vehicle in vehicles:
        logger.info(f"VEHICLE FOUND: {format_vehicle_info(vehicle)}")
        
def print_status(message: str, level: str = "INFO"):
    """Print status message with timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    symbols = {
        "INFO": "ℹ️",
        "SUCCESS": "✅", 
        "WARNING": "⚠️",
        "ERROR": "❌",
        "FOUND": "🚗"
    }
    symbol = symbols.get(level, "ℹ️")
    print(f"[{timestamp}] {symbol} {message}")

def handle_keyboard_interrupt():
    """Handle Ctrl+C gracefully"""
    print("\n🛑 Monitoring stopped by user.")
    print("Thank you for using Tesla Bot!")

if __name__ == "__main__":
    # Test utilities
    logger = setup_logging("test")
    logger.info("Testing utilities...")
    
    # Test notification sound
    print("Testing notification sound...")
    play_notification_sound()
    
    print("✅ Utilities test completed")
