
// Copyright 2012 Google Inc. All rights reserved.
 
(function(){

var data = {
"resource": {
  "version":"5",
  
  "macros":[{"function":"__e"},{"function":"__u","vtp_component":"URL","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__u","vtp_component":"HOST","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__u","vtp_component":"PATH","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__f","vtp_component":"URL"},{"function":"__e"}],
  "tags":[{"function":"__paused","vtp_originalTagType":"googtag","tag_id":3}],
  "predicates":[{"function":"_eq","arg0":["macro",0],"arg1":"gtm.load"}],
  "rules":[[["if",0],["add",0]]]
},
"runtime":[ [50,"__e",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getEventData"]],["$0","event"]]]]
 ,[50,"__f",[46,"a"],[52,"b",["require","copyFromDataLayer"]],[52,"c",["require","getReferrerUrl"]],[52,"d",["require","makeString"]],[52,"e",["require","parseUrl"]],[52,"f",[15,"__module_legacyUrls"]],[52,"g",[30,["b","gtm.referrer",1],["c"]]],[22,[28,[15,"g"]],[46,[36,["d",[15,"g"]]]]],[38,[17,[15,"a"],"component"],[46,"PROTOCOL","HOST","PORT","PATH","QUERY","FRAGMENT","URL"],[46,[5,[46,[36,[2,[15,"f"],"B",[7,[15,"g"]]]]]],[5,[46,[36,[2,[15,"f"],"C",[7,[15,"g"],[17,[15,"a"],"stripWww"]]]]]],[5,[46,[36,[2,[15,"f"],"D",[7,[15,"g"]]]]]],[5,[46,[36,[2,[15,"f"],"E",[7,[15,"g"],[17,[15,"a"],"defaultPages"]]]]]],[5,[46,[22,[17,[15,"a"],"queryKey"],[46,[53,[36,[2,[15,"f"],"H",[7,[15,"g"],[17,[15,"a"],"queryKey"]]]]]]],[52,"h",["e",[15,"g"]]],[36,[2,[17,[15,"h"],"search"],"replace",[7,"?",""]]]]],[5,[46,[36,[2,[15,"f"],"G",[7,[15,"g"]]]]]],[5,[46]],[9,[46,[36,[2,[15,"f"],"A",[7,["d",[15,"g"]]]]]]]]]]
 ,[50,"__paused",[46,"a"],[2,[15,"a"],"gtmOnFailure",[7]]]
 ,[50,"__u",[46,"a"],[50,"k",[46,"l","m"],[52,"n",[17,[15,"m"],"multiQueryKeys"]],[52,"o",[30,[17,[15,"m"],"queryKey"],""]],[52,"p",[17,[15,"m"],"ignoreEmptyQueryParam"]],[22,[20,[15,"o"],""],[46,[53,[52,"r",[2,[17,["i",[15,"l"]],"search"],"replace",[7,"?",""]]],[36,[39,[1,[28,[15,"r"]],[15,"p"]],[44],[15,"r"]]]]]],[41,"q"],[22,[15,"n"],[46,[53,[22,[20,["e",[15,"o"]],"array"],[46,[53,[3,"q",[15,"o"]]]],[46,[53,[52,"r",["c","\\s+","g"]],[3,"q",[2,[2,["f",[15,"o"]],"replace",[7,[15,"r"],""]],"split",[7,","]]]]]]]],[46,[53,[3,"q",[7,["f",[15,"o"]]]]]]],[65,"r",[15,"q"],[46,[53,[52,"s",[2,[15,"h"],"H",[7,[15,"l"],[15,"r"]]]],[22,[29,[15,"s"],[44]],[46,[53,[22,[1,[15,"p"],[20,[15,"s"],""]],[46,[53,[6]]]],[36,[15,"s"]]]]]]]],[36,[44]]],[52,"b",["require","copyFromDataLayer"]],[52,"c",["require","internal.createRegex"]],[52,"d",["require","getUrl"]],[52,"e",["require","getType"]],[52,"f",["require","makeString"]],[52,"g",["require","parseUrl"]],[52,"h",[15,"__module_legacyUrls"]],[52,"i",["require","internal.legacyParseUrl"]],[41,"j"],[22,[17,[15,"a"],"customUrlSource"],[46,[53,[3,"j",[17,[15,"a"],"customUrlSource"]]]],[46,[53,[3,"j",["b","gtm.url",1]]]]],[3,"j",[30,[15,"j"],["d"]]],[38,[17,[15,"a"],"component"],[46,"PROTOCOL","HOST","PORT","PATH","EXTENSION","QUERY","FRAGMENT","URL"],[46,[5,[46,[36,[2,[15,"h"],"B",[7,[15,"j"]]]]]],[5,[46,[36,[2,[15,"h"],"C",[7,[15,"j"],[17,[15,"a"],"stripWww"]]]]]],[5,[46,[36,[2,[15,"h"],"D",[7,[15,"j"]]]]]],[5,[46,[36,[2,[15,"h"],"E",[7,[15,"j"],[17,[15,"a"],"defaultPages"]]]]]],[5,[46,[36,[2,[15,"h"],"F",[7,[15,"j"]]]]]],[5,[46,[36,["k",[15,"j"],[15,"a"]]]]],[5,[46,[36,[2,[15,"h"],"G",[7,[15,"j"]]]]]],[5,[46]],[9,[46,[36,[2,[15,"h"],"A",[7,["f",[15,"j"]]]]]]]]]]
 ,[52,"__module_legacyUrls",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"h",[46,"p"],[52,"q",[2,[15,"p"],"indexOf",[7,"#"]]],[36,[39,[23,[15,"q"],0],[15,"p"],[2,[15,"p"],"substring",[7,0,[15,"q"]]]]]],[50,"i",[46,"p"],[52,"q",[17,["e",[15,"p"]],"protocol"]],[36,[39,[15,"q"],[2,[15,"q"],"replace",[7,":",""]],""]]],[50,"j",[46,"p","q"],[41,"r"],[3,"r",[17,["e",[15,"p"]],"hostname"]],[22,[28,[15,"r"]],[46,[36,""]]],[52,"s",["b",":[0-9]+"]],[3,"r",[2,[15,"r"],"replace",[7,[15,"s"],""]]],[22,[15,"q"],[46,[53,[52,"t",["b","^www\\d*\\."]],[52,"u",[2,[15,"r"],"match",[7,[15,"t"]]]],[22,[1,[15,"u"],[16,[15,"u"],0]],[46,[3,"r",[2,[15,"r"],"substring",[7,[17,[16,[15,"u"],0],"length"]]]]]]]]],[36,[15,"r"]]],[50,"k",[46,"p"],[52,"q",["e",[15,"p"]]],[41,"r"],[3,"r",["f",[17,[15,"q"],"port"]]],[22,[28,[15,"r"]],[46,[53,[22,[20,[17,[15,"q"],"protocol"],"http:"],[46,[53,[3,"r",80]]],[46,[22,[20,[17,[15,"q"],"protocol"],"https:"],[46,[53,[3,"r",443]]],[46,[53,[3,"r",""]]]]]]]]],[36,["g",[15,"r"]]]],[50,"l",[46,"p","q"],[52,"r",["e",[15,"p"]]],[41,"s"],[3,"s",[39,[20,[2,[17,[15,"r"],"pathname"],"indexOf",[7,"/"]],0],[17,[15,"r"],"pathname"],[0,"/",[17,[15,"r"],"pathName"]]]],[22,[20,["d",[15,"q"]],"array"],[46,[53,[52,"t",[2,[15,"s"],"split",[7,"/"]]],[22,[19,[2,[15,"q"],"indexOf",[7,[16,[15,"t"],[37,[17,[15,"t"],"length"],1]]]],0],[46,[53,[43,[15,"t"],[37,[17,[15,"t"],"length"],1],""],[3,"s",[2,[15,"t"],"join",[7,"/"]]]]]]]]],[36,[15,"s"]]],[50,"m",[46,"p"],[52,"q",[17,["e",[15,"p"]],"pathname"]],[52,"r",[2,[15,"q"],"split",[7,"."]]],[41,"s"],[3,"s",[39,[18,[17,[15,"r"],"length"],1],[16,[15,"r"],[37,[17,[15,"r"],"length"],1]],""]],[36,[16,[2,[15,"s"],"split",[7,"/"]],0]]],[50,"n",[46,"p"],[52,"q",[17,["e",[15,"p"]],"hash"]],[36,[2,[15,"q"],"replace",[7,"#",""]]]],[50,"o",[46,"p","q"],[50,"s",[46,"t"],[36,["c",[2,[15,"t"],"replace",[7,["b","\\+","g"]," "]]]]],[52,"r",[2,[17,["e",[15,"p"]],"search"],"replace",[7,"?",""]]],[65,"t",[2,[15,"r"],"split",[7,"&"]],[46,[53,[52,"u",[2,[15,"t"],"split",[7,"="]]],[22,[21,["s",[16,[15,"u"],0]],[15,"q"]],[46,[6]]],[36,["s",[2,[2,[15,"u"],"slice",[7,1]],"join",[7,"="]]]]]]],[36]],[52,"b",["require","internal.createRegex"]],[52,"c",["require","decodeUriComponent"]],[52,"d",["require","getType"]],[52,"e",["require","internal.legacyParseUrl"]],[52,"f",["require","makeNumber"]],[52,"g",["require","makeString"]],[36,[8,"F",[15,"m"],"H",[15,"o"],"G",[15,"n"],"C",[15,"j"],"E",[15,"l"],"D",[15,"k"],"B",[15,"i"],"A",[15,"h"]]]],[36,["a"]]]],["$0"]]]
 
]
,"entities":{
"__e":{"2":true,"5":true}
,
"__f":{"2":true,"5":true}
,
"__paused":{"5":true}
,
"__u":{"2":true,"5":true}


}
,"blob":{"1":"5","10":"GTM-MT96V2P9","14":"57f1","15":"0","16":"ChAI8JvdwwYQi7ejypXds4U8EiUATPn+ZC0gHnLmQ5MGsRp+KsjXwY32/v27TkKNSaXwCEEFKTPTGgJKyA==","19":"dataLayer","20":"","21":"www.googletagmanager.com","22":"eyIwIjoiVFIiLCIxIjoiVFItMDYiLCIyIjpmYWxzZSwiMyI6Imdvb2dsZS5jb20udHIiLCI0IjoiIiwiNSI6dHJ1ZSwiNiI6ZmFsc2UsIjciOiJhZF9zdG9yYWdlfGFuYWx5dGljc19zdG9yYWdlfGFkX3VzZXJfZGF0YXxhZF9wZXJzb25hbGl6YXRpb24ifQ","23":"google.tagmanager.debugui2.queue","24":"tagassistant.google.com","27":0.005,"3":"www.googletagmanager.com","30":"TR","31":"TR-06","32":true,"36":"https://adservice.google.com/pagead/regclk","37":"__TAGGY_INSTALLED","38":"cct.google","39":"googTaggyReferrer","40":"https://cct.google/taggy/agent.js","41":"google.tagmanager.ta.prodqueue","42":0.01,"43":"{\"keys\":[{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BAwGL9J4UjQSrZoeGPw6HXyx1FpeqVZ2dFE5XajDlAvzw02AuSOmKlwoPcwocJHM930uCTrWOKMNwTJ+2KaydlU=\",\"version\":0},\"id\":\"cae575be-831b-468e-9f99-1085c3758e94\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BGWfBEfQe56I9Nrd4XyuWEQMyyVehOzxBC9RxRsDMxE0f6ZrMZmfSH/ypDzzLLXPBjPjGczKO2R9CzT5Is6r8w8=\",\"version\":0},\"id\":\"bc8ed64c-953e-4d7e-b83d-76645dea206c\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BPtf11g37uPTHanbGmIxYwPiePfmW0km+5iZuV1PQ+68IVJdl8vZqaPD+DULG3zd75LqntxyTuxvJi7iBhrPLCY=\",\"version\":0},\"id\":\"b2761706-7127-4ba1-be63-2ad1e183de94\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BO8dKHIMk16rHWieNm48IIofxEmmfx4jNCUcNg7DjD/gmWGPJdgrTh9cXQV1yh60+KXYJC2VKGg5cZisdebXyvo=\",\"version\":0},\"id\":\"315bfc28-f0e8-4903-97ad-647ba5b6b664\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BFM+QN/v+JvDrSv3iDhh/5loondk70iUw0015xevqHVwONPqnWNBOg5SiECqcaGB2Jo36cTeMjO6GENSeasnWbw=\",\"version\":0},\"id\":\"1a54b6ca-b156-45dc-8ad7-01eb219e0b5b\"}]}","44":"101509157~103116026~103200004~103233427~103351869~103351871~104684208~104684211~104732253~104732255~104908321~104908323~104964065~104964067~104967141~104967143","5":"GTM-MT96V2P9","6":"192979098","8":"res_ts:1727167763439807,srv_cl:783675612,ds:live,cv:5","9":"GTM-MT96V2P9"}
,"permissions":{
"__e":{"read_event_data":{"eventDataAccess":"specific","keyPatterns":["event"]}}
,
"__f":{"read_data_layer":{"keyPatterns":["gtm.referrer"]},"get_referrer":{"urlParts":"any"}}
,
"__paused":{}
,
"__u":{"read_data_layer":{"keyPatterns":["gtm.url"]},"get_url":{"urlParts":"any"}}


}



,"security_groups":{
"google":[
"__e"
,
"__f"
,
"__u"

]


}



};




var k,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ca=function(a){for(var b=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global],c=0;c<b.length;++c){var d=b[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");
},ea=ca(this),fa=typeof Symbol==="function"&&typeof Symbol("x")==="symbol",ha={},la={},ma=function(a,b,c){if(!c||a!=null){var d=la[b];if(d==null)return a[b];var e=a[d];return e!==void 0?e:a[b]}},na=function(a,b,c){if(b)a:{var d=a.split("."),e=d.length===1,f=d[0],g;!e&&f in ha?g=ha:g=ea;for(var h=0;h<d.length-1;h++){var m=d[h];if(!(m in g))break a;g=g[m]}var n=d[d.length-1],p=fa&&c==="es6"?g[n]:null,q=b(p);if(q!=null)if(e)ba(ha,n,{configurable:!0,writable:!0,value:q});else if(q!==p){if(la[n]===void 0){var r=
Math.random()*1E9>>>0;la[n]=fa?ea.Symbol(n):"$jscp$"+r+"$"+n}ba(g,la[n],{configurable:!0,writable:!0,value:q})}}};na("Symbol",function(a){if(a)return a;var b=function(f,g){this.C=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.C};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e},"es6");
var oa=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},pa;if(fa&&typeof Object.setPrototypeOf=="function")pa=Object.setPrototypeOf;else{var qa;a:{var ra={a:!0},ta={};try{ta.__proto__=ra;qa=ta.a;break a}catch(a){}qa=!1}pa=qa?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var ua=pa,va=function(a,b){a.prototype=oa(b.prototype);a.prototype.constructor=a;if(ua)ua(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Iq=b.prototype},l=function(a){var b=typeof ha.Symbol!="undefined"&&ha.Symbol.iterator&&a[ha.Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},wa=function(a){for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);return c},xa=function(a){return a instanceof Array?a:wa(l(a))},Aa=function(a){return za(a,a)},za=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},Ba=fa&&typeof ma(Object,"assign")=="function"?ma(Object,"assign"):function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};na("Object.assign",function(a){return a||Ba},"es6");
var Ca=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var Da=this||self,Ea=function(a,b){function c(){}c.prototype=b.prototype;a.Iq=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Hr=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Fa=function(a,b){this.type=a;this.data=b};var Ga=function(){this.map={};this.C={}};Ga.prototype.get=function(a){return this.map["dust."+a]};Ga.prototype.set=function(a,b){var c="dust."+a;this.C.hasOwnProperty(c)||(this.map[c]=b)};Ga.prototype.has=function(a){return this.map.hasOwnProperty("dust."+a)};Ga.prototype.remove=function(a){var b="dust."+a;this.C.hasOwnProperty(b)||delete this.map[b]};
var Ha=function(a,b){var c=[],d;for(d in a.map)if(a.map.hasOwnProperty(d)){var e=d.substring(5);switch(b){case 1:c.push(e);break;case 2:c.push(a.map[d]);break;case 3:c.push([e,a.map[d]])}}return c};Ga.prototype.Aa=function(){return Ha(this,1)};Ga.prototype.Ac=function(){return Ha(this,2)};Ga.prototype.ac=function(){return Ha(this,3)};var Ia=function(){};Ia.prototype.reset=function(){};var Ja=function(a,b){this.P=a;this.parent=b;this.N=this.C=void 0;this.Cb=!1;this.H=function(c,d,e){return c.apply(d,e)};this.values=new Ga};Ja.prototype.add=function(a,b){Ka(this,a,b,!1)};Ja.prototype.nh=function(a,b){Ka(this,a,b,!0)};var Ka=function(a,b,c,d){if(!a.Cb)if(d){var e=a.values;e.set(b,c);e.C["dust."+b]=!0}else a.values.set(b,c)};k=Ja.prototype;k.set=function(a,b){this.Cb||(!this.values.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.values.set(a,b))};
k.get=function(a){return this.values.has(a)?this.values.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.values.has(a)||!(!this.parent||!this.parent.has(a))};k.tb=function(){var a=new Ja(this.P,this);this.C&&a.Nb(this.C);a.Vc(this.H);a.Ld(this.N);return a};k.Cd=function(){return this.P};k.Nb=function(a){this.C=a};k.am=function(){return this.C};k.Vc=function(a){this.H=a};k.aj=function(){return this.H};k.Wa=function(){this.Cb=!0};k.Ld=function(a){this.N=a};k.ub=function(){return this.N};var La=function(a,b){this.fa=a;this.parent=b;this.P=this.H=void 0;this.Cb=!1;this.N=function(c,d,e){return c.apply(d,e)};this.C=new Map;this.R=new Set};La.prototype.add=function(a,b){Ma(this,a,b,!1)};La.prototype.nh=function(a,b){Ma(this,a,b,!0)};var Ma=function(a,b,c,d){a.Cb||a.R.has(b)||(d&&a.R.add(b),a.C.set(b,c))};k=La.prototype;k.set=function(a,b){this.Cb||(!this.C.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.R.has(a)||this.C.set(a,b))};
k.get=function(a){return this.C.has(a)?this.C.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.C.has(a)||!(!this.parent||!this.parent.has(a))};k.tb=function(){var a=new La(this.fa,this);this.H&&a.Nb(this.H);a.Vc(this.N);a.Ld(this.P);return a};k.Cd=function(){return this.fa};k.Nb=function(a){this.H=a};k.am=function(){return this.H};k.Vc=function(a){this.N=a};k.aj=function(){return this.N};k.Wa=function(){this.Cb=!0};k.Ld=function(a){this.P=a};k.ub=function(){return this.P};var Na=function(a,b,c){var d;d=Error.call(this,a.message);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.om=a;this.Sl=c===void 0?!1:c;this.debugInfo=[];this.C=b};va(Na,Error);var Oa=function(a){return a instanceof Na?a:new Na(a,void 0,!0)};var Pa=[],Qa={};function Ra(a){return Pa[a]===void 0?!1:Pa[a]};var Sa=new Map;function Ta(a,b){for(var c,d=l(b),e=d.next();!e.done&&!(c=Ua(a,e.value),c instanceof Fa);e=d.next());return c}
function Ua(a,b){try{if(Ra(16)){var c=b[0],d=b.slice(1),e=String(c),f=Sa.has(e)?Sa.get(e):a.get(e);if(!f||typeof f.invoke!=="function")throw Oa(Error("Attempting to execute non-function "+b[0]+"."));return f.apply(a,d)}var g=l(b),h=g.next().value,m=wa(g),n=a.get(String(h));if(!n||typeof n.invoke!=="function")throw Oa(Error("Attempting to execute non-function "+b[0]+"."));return n.invoke.apply(n,[a].concat(xa(m)))}catch(q){var p=a.am();p&&p(q,b.context?{id:b[0],line:b.context.line}:null);throw q;}}
;var Wa=function(){this.H=new Ia;this.C=Ra(16)?new La(this.H):new Ja(this.H)};k=Wa.prototype;k.Cd=function(){return this.H};k.Nb=function(a){this.C.Nb(a)};k.Vc=function(a){this.C.Vc(a)};k.execute=function(a){return this.Bj([a].concat(xa(Ca.apply(1,arguments))))};k.Bj=function(){for(var a,b=l(Ca.apply(0,arguments)),c=b.next();!c.done;c=b.next())a=Ua(this.C,c.value);return a};
k.ho=function(a){var b=Ca.apply(1,arguments),c=this.C.tb();c.Ld(a);for(var d,e=l(b),f=e.next();!f.done;f=e.next())d=Ua(c,f.value);return d};k.Wa=function(){this.C.Wa()};var Xa=function(){this.Ea=!1;this.aa=new Ga};k=Xa.prototype;k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ea||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ea||this.aa.remove(a)};k.Aa=function(){return this.aa.Aa()};k.Ac=function(){return this.aa.Ac()};k.ac=function(){return this.aa.ac()};k.Wa=function(){this.Ea=!0};k.Cb=function(){return this.Ea};function Za(){for(var a=$a,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function ab(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var $a,bb;function cb(a){$a=$a||ab();bb=bb||Za();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,h=e?a.charCodeAt(c+2):0,m=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|h>>6,q=h&63;e||(q=64,d||(p=64));b.push($a[m],$a[n],$a[p],$a[q])}return b.join("")}
function db(a){function b(m){for(;d<a.length;){var n=a.charAt(d++),p=bb[n];if(p!=null)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return m}$a=$a||ab();bb=bb||Za();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),h=b(64);if(h===64&&e===-1)return c;c+=String.fromCharCode(e<<2|f>>4);g!==64&&(c+=String.fromCharCode(f<<4&240|g>>2),h!==64&&(c+=String.fromCharCode(g<<6&192|h)))}};var eb={};function fb(a,b){eb[a]=eb[a]||[];eb[a][b]=!0}function gb(){eb.GTAG_EVENT_FEATURE_CHANNEL=hb}function ib(a){var b=eb[a];if(!b||b.length===0)return"";for(var c=[],d=0,e=0;e<b.length;e++)e%8===0&&e>0&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);d>0&&c.push(String.fromCharCode(d));return cb(c.join("")).replace(/\.+$/,"")}function jb(){for(var a=[],b=eb.fdr||[],c=0;c<b.length;c++)b[c]&&a.push(c);return a.length>0?a:void 0};function kb(){}function lb(a){return typeof a==="function"}function mb(a){return typeof a==="string"}function nb(a){return typeof a==="number"&&!isNaN(a)}function ob(a){return Array.isArray(a)?a:[a]}function pb(a,b){if(a&&Array.isArray(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]}function qb(a,b){if(!nb(a)||!nb(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)}
function rb(a,b){for(var c=new sb,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1}function tb(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])}function ub(a){return!!a&&(Object.prototype.toString.call(a)==="[object Arguments]"||Object.prototype.hasOwnProperty.call(a,"callee"))}function vb(a){return Math.round(Number(a))||0}function wb(a){return"false"===String(a).toLowerCase()?!1:!!a}
function xb(a){var b=[];if(Array.isArray(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b}function yb(a){return a?a.replace(/^\s+|\s+$/g,""):""}function zb(){return new Date(Date.now())}function Ab(){return zb().getTime()}var sb=function(){this.prefix="gtm.";this.values={}};sb.prototype.set=function(a,b){this.values[this.prefix+a]=b};sb.prototype.get=function(a){return this.values[this.prefix+a]};sb.prototype.contains=function(a){return this.get(a)!==void 0};
function Bb(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c}function Cb(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}}function Db(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])}function Eb(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c}function Fb(a,b){return a.length>=b.length&&a.substring(0,b.length)===b}
function Gb(a,b,c){c=c||[];for(var d=a,e=0;e<b.length-1;e++){if(!d.hasOwnProperty(b[e]))return;d=d[b[e]];if(c.indexOf(d)>=0)return}return d}function Hb(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c}var Jb=/^\w{1,9}$/;function Kb(a,b){a=a||{};b=b||",";var c=[];tb(a,function(d,e){Jb.test(d)&&e&&c.push(d)});return c.join(b)}function Lb(a,b){function c(){e&&++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c}
function Mb(a){if(!a)return a;var b=a;try{b=decodeURIComponent(a)}catch(d){}var c=b.split(",");return c.length===2&&c[0]===c[1]?c[0]:a}
function Nb(a,b,c){function d(n){var p=n.split("=")[0];if(a.indexOf(p)<0)return n;if(c!==void 0)return p+"="+c}function e(n){return n.split("&").map(d).filter(function(p){return p!==void 0}).join("&")}var f=b.href.split(/[?#]/)[0],g=b.search,h=b.hash;g[0]==="?"&&(g=g.substring(1));h[0]==="#"&&(h=h.substring(1));g=e(g);h=e(h);g!==""&&(g="?"+g);h!==""&&(h="#"+h);var m=""+f+g+h;m[m.length-1]==="/"&&(m=m.substring(0,m.length-1));return m}
function Ob(a){for(var b=0;b<3;++b)try{var c=decodeURIComponent(a).replace(/\+/g," ");if(c===a)break;a=c}catch(d){return""}return a}function Pb(){var a=x.crypto||x.msCrypto;if(a&&a.getRandomValues)try{var b=new Uint8Array(25);a.getRandomValues(b);return btoa(String.fromCharCode.apply(String,xa(b))).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}catch(c){}};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Qb=globalThis.trustedTypes,Rb;function Sb(){var a=null;if(!Qb)return a;try{var b=function(c){return c};a=Qb.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a}function Tb(){Rb===void 0&&(Rb=Sb());return Rb};var Vb=function(a){this.C=a};Vb.prototype.toString=function(){return this.C+""};function Wb(a){var b=a,c=Tb(),d=c?c.createScriptURL(b):b;return new Vb(d)}function Xb(a){if(a instanceof Vb)return a.C;throw Error("");};var Yb=Aa([""]),Zb=za(["\x00"],["\\0"]),$b=za(["\n"],["\\n"]),bc=za(["\x00"],["\\u0000"]);function cc(a){return a.toString().indexOf("`")===-1}cc(function(a){return a(Yb)})||cc(function(a){return a(Zb)})||cc(function(a){return a($b)})||cc(function(a){return a(bc)});var dc=function(a){this.C=a};dc.prototype.toString=function(){return this.C};var ec=function(a){this.Xp=a};function hc(a){return new ec(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var ic=[hc("data"),hc("http"),hc("https"),hc("mailto"),hc("ftp"),new ec(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function jc(a){var b;b=b===void 0?ic:b;if(a instanceof dc)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof ec&&d.Xp(a))return new dc(a)}}var kc=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function lc(a){var b;if(a instanceof dc)if(a instanceof dc)b=a.C;else throw Error("");else b=kc.test(a)?a:void 0;return b};function mc(a,b){var c=lc(b);c!==void 0&&(a.action=c)};function nc(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var oc=function(a){this.C=a};oc.prototype.toString=function(){return this.C+""};var qc=function(){this.C=pc[0].toLowerCase()};qc.prototype.toString=function(){return this.C};function rc(a,b){var c=[new qc];if(c.length===0)throw Error("");var d=c.map(function(f){var g;if(f instanceof qc)g=f.C;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return e.indexOf(f)!==0}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};var sc=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function tc(a){return a===null?"null":a===void 0?"undefined":a};var x=window,uc=window.history,z=document,vc=navigator;function wc(){var a;try{a=vc.serviceWorker}catch(b){return}return a}var xc=z.currentScript,yc=xc&&xc.src;function zc(a,b){var c=x,d=c[a];c[a]=d===void 0?b:d;return c[a]}function Ac(a){return(vc.userAgent||"").indexOf(a)!==-1}function Bc(){return Ac("Firefox")||Ac("FxiOS")}function Cc(){return(Ac("GSA")||Ac("GoogleApp"))&&(Ac("iPhone")||Ac("iPad"))}function Dc(){return Ac("Edg/")||Ac("EdgA/")||Ac("EdgiOS/")}
var Ec={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},Fc={onload:1,src:1,width:1,height:1,style:1};function Gc(a,b,c){b&&tb(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
function Hc(a,b,c,d,e){var f=z.createElement("script");Gc(f,d,Ec);f.type="text/javascript";f.async=d&&d.async===!1?!1:!0;var g;g=Wb(tc(a));f.src=Xb(g);var h,m=f.ownerDocument;m=m===void 0?document:m;var n,p,q=(p=(n=m).querySelector)==null?void 0:p.call(n,"script[nonce]");(h=q==null?"":q.nonce||q.getAttribute("nonce")||"")&&f.setAttribute("nonce",h);b&&(f.onload=b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var r=z.getElementsByTagName("script")[0]||z.body||z.head;r.parentNode.insertBefore(f,r)}return f}
function Ic(){if(yc){var a=yc.toLowerCase();if(a.indexOf("https://")===0)return 2;if(a.indexOf("http://")===0)return 3}return 1}function Jc(a,b,c,d,e,f){f=f===void 0?!0:f;var g=e,h=!1;g||(g=z.createElement("iframe"),h=!0);Gc(g,c,Fc);d&&tb(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");a!==void 0&&(g.src=a);if(h){var m=z.body&&z.body.lastChild||z.body||z.head;m.parentNode.insertBefore(g,m)}b&&(g.onload=b);return g}
function Kc(a,b,c,d){return Lc(a,b,c,d)}function Mc(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,!!d)}function Nc(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}function Oc(a){x.setTimeout(a,0)}function Pc(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null}function Qc(a){var b=a.innerText||a.textContent||"";b&&b!==" "&&(b=b.replace(/^[\s\xa0]+/g,""),b=b.replace(/[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b}
function Rc(a){var b=z.createElement("div"),c=b,d,e=tc("A<div>"+a+"</div>"),f=Tb(),g=f?f.createHTML(e):e;d=new oc(g);if(c.nodeType===1&&/^(script|style)$/i.test(c.tagName))throw Error("");var h;if(d instanceof oc)h=d.C;else throw Error("");c.innerHTML=h;b=b.lastChild;for(var m=[];b&&b.firstChild;)m.push(b.removeChild(b.firstChild));return m}
function Sc(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null}function Tc(a,b,c){var d;try{d=vc.sendBeacon&&vc.sendBeacon(a)}catch(e){fb("TAGGING",15)}d?b==null||b():Lc(a,b,c)}function Vc(a,b){try{return vc.sendBeacon(a,b)}catch(c){fb("TAGGING",15)}return!1}var Wc={cache:"no-store",credentials:"include",keepalive:!0,method:"POST",mode:"no-cors",redirect:"follow"};
function Xc(a,b,c,d,e){if(Yc()){var f=ma(Object,"assign").call(Object,{},Wc);b&&(f.body=b);c&&(c.attributionReporting&&(f.attributionReporting=c.attributionReporting),c.browsingTopics&&(f.browsingTopics=c.browsingTopics),c.credentials&&(f.credentials=c.credentials),c.mode&&(f.mode=c.mode),c.method&&(f.method=c.method));try{var g=x.fetch(a,f);if(g)return g.then(function(m){m&&(m.ok||m.status===0)?d==null||d():e==null||e()}).catch(function(){e==null||e()}),!0}catch(m){}}if(c&&c.Ch)return e==null||e(),
!1;if(b){var h=Vc(a,b);h?d==null||d():e==null||e();return h}Zc(a,d,e);return!0}function Yc(){return typeof x.fetch==="function"}function $c(a,b){var c=a[b];c&&typeof c.animVal==="string"&&(c=c.animVal);return c}function ad(){var a=x.performance;if(a&&lb(a.now))return a.now()}
function bd(){var a,b=x.performance;if(b&&b.getEntriesByType)try{var c=b.getEntriesByType("navigation");c&&c.length>0&&(a=c[0].type)}catch(d){return"e"}if(!a)return"u";switch(a){case "navigate":return"n";case "back_forward":return"h";case "reload":return"r";case "prerender":return"p";default:return"x"}}function cd(){return x.performance||void 0}function dd(){var a=x.webPixelsManager;return a?a.createShopifyExtend!==void 0:!1}
var Lc=function(a,b,c,d){var e=new Image(1,1);Gc(e,d,{});e.onload=function(){e.onload=null;b&&b()};e.onerror=function(){e.onerror=null;c&&c()};e.src=a;return e},Zc=Tc;function ed(a,b){return this.evaluate(a)&&this.evaluate(b)}function fd(a,b){return this.evaluate(a)===this.evaluate(b)}function gd(a,b){return this.evaluate(a)||this.evaluate(b)}function hd(a,b){var c=this.evaluate(a),d=this.evaluate(b);return String(c).indexOf(String(d))>-1}function id(a,b){var c=String(this.evaluate(a)),d=String(this.evaluate(b));return c.substring(0,d.length)===d}
function jd(a,b){var c=this.evaluate(a),d=this.evaluate(b);switch(c){case "pageLocation":var e=x.location.href;d instanceof Xa&&d.get("stripProtocol")&&(e=e.replace(/^https?:\/\//,""));return e}};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license.
*/
var kd=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,ld=function(a){if(a==null)return String(a);var b=kd.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},md=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},nd=function(a){if(!a||ld(a)!="object"||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!md(a,"constructor")&&!md(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return b===void 0||
md(a,b)},od=function(a,b){var c=b||(ld(a)=="array"?[]:{}),d;for(d in a)if(md(a,d)){var e=a[d];ld(e)=="array"?(ld(c[d])!="array"&&(c[d]=[]),c[d]=od(e,c[d])):nd(e)?(nd(c[d])||(c[d]={}),c[d]=od(e,c[d])):c[d]=e}return c};function pd(a){if(a==void 0||Array.isArray(a)||nd(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1}function qd(a){return typeof a==="number"&&a>=0&&isFinite(a)&&a%1===0||typeof a==="string"&&a[0]!=="-"&&a===""+parseInt(a)};var rd=function(a){a=a===void 0?[]:a;this.aa=new Ga;this.values=[];this.Ea=!1;for(var b in a)a.hasOwnProperty(b)&&(qd(b)?this.values[Number(b)]=a[Number(b)]:this.aa.set(b,a[b]))};k=rd.prototype;k.toString=function(a){if(a&&a.indexOf(this)>=0)return"";for(var b=[],c=0;c<this.values.length;c++){var d=this.values[c];d===null||d===void 0?b.push(""):d instanceof rd?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(String(d))}return b.join(",")};
k.set=function(a,b){if(!this.Ea)if(a==="length"){if(!qd(b))throw Oa(Error("RangeError: Length property must be a valid integer."));this.values.length=Number(b)}else qd(a)?this.values[Number(a)]=b:this.aa.set(a,b)};k.get=function(a){return a==="length"?this.length():qd(a)?this.values[Number(a)]:this.aa.get(a)};k.length=function(){return this.values.length};k.Aa=function(){for(var a=this.aa.Aa(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(String(b));return a};
k.Ac=function(){for(var a=this.aa.Ac(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(this.values[b]);return a};k.ac=function(){for(var a=this.aa.ac(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push([String(b),this.values[b]]);return a};k.remove=function(a){qd(a)?delete this.values[Number(a)]:this.Ea||this.aa.remove(a)};k.pop=function(){return this.values.pop()};
k.push=function(){var a=Ca.apply(0,arguments);return Ra(17)&&arguments.length===1?this.values.push(arguments[0]):this.values.push.apply(this.values,xa(a))};k.shift=function(){return this.values.shift()};k.splice=function(a,b){var c=Ca.apply(2,arguments);return b===void 0&&c.length===0?new rd(this.values.splice(a)):new rd(this.values.splice.apply(this.values,[a,b||0].concat(xa(c))))};k.unshift=function(){return this.values.unshift.apply(this.values,xa(Ca.apply(0,arguments)))};
k.has=function(a){return qd(a)&&this.values.hasOwnProperty(a)||this.aa.has(a)};k.Wa=function(){this.Ea=!0;Object.freeze(this.values)};k.Cb=function(){return this.Ea};function sd(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var td=function(a,b){this.functionName=a;this.Sc=b;this.aa=new Ga;this.Ea=!1};k=td.prototype;k.toString=function(){return this.functionName};k.getName=function(){return this.functionName};k.getKeys=function(){return new rd(this.Aa())};k.invoke=function(a){var b=Ca.apply(1,arguments);return Ra(18)?this.Sc.apply(new ud(this,a),b):this.Sc.call.apply(this.Sc,[new ud(this,a)].concat(xa(b)))};k.apply=function(a,b){return this.Sc.apply(new ud(this,a),b)};
k.Lb=function(a){var b=Ca.apply(1,arguments);try{return this.invoke.apply(this,[a].concat(xa(b)))}catch(c){}};k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ea||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ea||this.aa.remove(a)};k.Aa=function(){return this.aa.Aa()};k.Ac=function(){return this.aa.Ac()};k.ac=function(){return this.aa.ac()};k.Wa=function(){this.Ea=!0};k.Cb=function(){return this.Ea};var vd=function(a,b){td.call(this,a,b)};
va(vd,td);var wd=function(a,b){td.call(this,a,b)};va(wd,td);var ud=function(a,b){this.Sc=a;this.K=b};ud.prototype.evaluate=function(a){var b=this.K;return Array.isArray(a)?Ua(b,a):a};ud.prototype.getName=function(){return this.Sc.getName()};ud.prototype.Cd=function(){return this.K.Cd()};var xd=function(){this.map=new Map};xd.prototype.set=function(a,b){this.map.set(a,b)};xd.prototype.get=function(a){return this.map.get(a)};var yd=function(){this.keys=[];this.values=[]};yd.prototype.set=function(a,b){this.keys.push(a);this.values.push(b)};yd.prototype.get=function(a){var b=this.keys.indexOf(a);if(b>-1)return this.values[b]};function zd(){try{return Map?new xd:new yd}catch(a){return new yd}};var Ad=function(a){if(a instanceof Ad)return a;if(pd(a))throw Error("Type of given value has an equivalent Pixie type.");this.value=a};Ad.prototype.getValue=function(){return this.value};Ad.prototype.toString=function(){return String(this.value)};var Cd=function(a){this.promise=a;this.Ea=!1;this.aa=new Ga;this.aa.set("then",Bd(this));this.aa.set("catch",Bd(this,!0));this.aa.set("finally",Bd(this,!1,!0))};k=Cd.prototype;k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ea||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ea||this.aa.remove(a)};k.Aa=function(){return this.aa.Aa()};k.Ac=function(){return this.aa.Ac()};k.ac=function(){return this.aa.ac()};
var Bd=function(a,b,c){b=b===void 0?!1:b;c=c===void 0?!1:c;return new vd("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof vd||(d=void 0);e instanceof vd||(e=void 0);var f=this.K.tb(),g=function(m){return function(n){try{return c?(m.invoke(f),a.promise):m.invoke(f,n)}catch(p){return Promise.reject(p instanceof Error?new Ad(p):String(p))}}},h=a.promise.then(d&&g(d),e&&g(e));return new Cd(h)})};Cd.prototype.Wa=function(){this.Ea=!0};Cd.prototype.Cb=function(){return this.Ea};function Dd(a,b,c){var d=zd(),e=function(g,h){for(var m=g.Aa(),n=0;n<m.length;n++)h[m[n]]=f(g.get(m[n]))},f=function(g){if(g===null||g===void 0)return g;var h=d.get(g);if(h)return h;if(g instanceof rd){var m=[];d.set(g,m);for(var n=g.Aa(),p=0;p<n.length;p++)m[n[p]]=f(g.get(n[p]));return m}if(g instanceof Cd)return g.promise.then(function(u){return Dd(u,b,1)},function(u){return Promise.reject(Dd(u,b,1))});if(g instanceof Xa){var q={};d.set(g,q);e(g,q);return q}if(g instanceof vd){var r=function(){for(var u=
[],v=0;v<arguments.length;v++)u[v]=Ed(arguments[v],b,c);var w=new Ja(b?b.Cd():new Ia);b&&w.Ld(b.ub());return f(Ra(16)?g.apply(w,u):g.invoke.apply(g,[w].concat(xa(u))))};d.set(g,r);e(g,r);return r}var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;case 3:t=!1;break;default:}if(g instanceof Ad&&t)return g.getValue();switch(typeof g){case "boolean":case "number":case "string":case "undefined":return g;
case "object":if(g===null)return null}};return f(a)}
function Ed(a,b,c){var d=zd(),e=function(g,h){for(var m in g)g.hasOwnProperty(m)&&h.set(m,f(g[m]))},f=function(g){var h=d.get(g);if(h)return h;if(Array.isArray(g)||ub(g)){var m=new rd;d.set(g,m);for(var n in g)g.hasOwnProperty(n)&&m.set(n,f(g[n]));return m}if(nd(g)){var p=new Xa;d.set(g,p);e(g,p);return p}if(typeof g==="function"){var q=new vd("",function(){for(var u=Ca.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=Dd(this.evaluate(u[w]),b,c);return f(this.K.aj()(g,g,v))});d.set(g,q);e(g,q);return q}var r=typeof g;if(g===null||r==="string"||r==="number"||r==="boolean")return g;var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;default:}if(g!==void 0&&t)return new Ad(g)};return f(a)};var Fd={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a){for(var b=[],c=0;c<this.length();c++)b.push(this.get(c));for(var d=1;d<arguments.length;d++)if(arguments[d]instanceof rd)for(var e=arguments[d],f=0;f<e.length();f++)b.push(e.get(f));else b.push(arguments[d]);return new rd(b)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.invoke(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.invoke(a,this.get(e),e,this)&&d.push(this.get(e));return new rd(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.invoke(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=c===void 0?0:Number(c);e<0&&(e=Math.max(d+e,0));for(var f=
e;f<d;f++)if(this.has(f)&&this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;c!==void 0&&(e=c<0?d+c:Math.min(c,e));for(var f=e;f>=0;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.invoke(a,this.get(e),e,this));return new rd(d)},pop:function(){return this.pop()},push:function(a){return this.push.apply(this,
xa(Ca.apply(1,arguments)))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(c!==void 0)e=c;else{if(d===0)throw Oa(Error("TypeError: Reduce on List with no elements."));for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw Oa(Error("TypeError: Reduce on List with no elements."));}for(var h=f;h<d;h++)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(c!==void 0)e=c;else{if(d===0)throw Oa(Error("TypeError: ReduceRight on List with no elements."));
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw Oa(Error("TypeError: ReduceRight on List with no elements."));}for(var h=f;h>=0;h--)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=sd(this),b=a.length-1,c=0;b>=0;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();b===void 0&&(b=0);b=b<0?Math.max(d+b,0):Math.min(b,d);c=c===
void 0?d:c<0?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new rd(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.invoke(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=sd(this);b===void 0?c.sort():c.sort(function(e,f){return Number(b.invoke(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d);return this},splice:function(a,b,c){return this.splice.apply(this,
[b,c].concat(xa(Ca.apply(3,arguments))))},toString:function(){return this.toString()},unshift:function(a){return this.unshift.apply(this,xa(Ca.apply(1,arguments)))}};var Gd={charAt:1,concat:1,indexOf:1,lastIndexOf:1,match:1,replace:1,search:1,slice:1,split:1,substring:1,toLowerCase:1,toLocaleLowerCase:1,toString:1,toUpperCase:1,toLocaleUpperCase:1,trim:1},Hd=new Fa("break"),Id=new Fa("continue");function Jd(a,b){return this.evaluate(a)+this.evaluate(b)}function Kd(a,b){return this.evaluate(a)&&this.evaluate(b)}
function Ld(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!(f instanceof rd))throw Error("Error: Non-List argument given to Apply instruction.");if(d===null||d===void 0)throw Oa(Error("TypeError: Can't read property "+e+" of "+d+"."));var g=typeof d==="number";if(typeof d==="boolean"||g){if(e==="toString"){if(g&&f.length()){var h=Dd(f.get(0));try{return d.toString(h)}catch(v){}}return d.toString()}throw Oa(Error("TypeError: "+d+"."+e+" is not a function."));}if(typeof d===
"string"){if(Gd.hasOwnProperty(e)){var m=2;m=1;var n=Dd(f,void 0,m);return Ed(d[e].apply(d,n),this.K)}throw Oa(Error("TypeError: "+e+" is not a function"));}if(d instanceof rd){if(d.has(e)){var p=d.get(String(e));if(p instanceof vd){var q=sd(f);return Ra(16)?p.apply(this.K,q):p.invoke.apply(p,[this.K].concat(xa(q)))}throw Oa(Error("TypeError: "+e+" is not a function"));
}if(Fd.supportedMethods.indexOf(e)>=0){var r=sd(f);return Fd[e].call.apply(Fd[e],[d,this.K].concat(xa(r)))}}if(d instanceof vd||d instanceof Xa||d instanceof Cd){if(d.has(e)){var t=d.get(e);if(t instanceof vd){var u=sd(f);return Ra(16)?t.apply(this.K,u):t.invoke.apply(t,[this.K].concat(xa(u)))}throw Oa(Error("TypeError: "+e+" is not a function"));}if(e==="toString")return d instanceof vd?d.getName():d.toString();if(e==="hasOwnProperty")return d.has(f.get(0))}if(d instanceof Ad&&e==="toString")return d.toString();
throw Oa(Error("TypeError: Object has no '"+e+"' property."));}function Md(a,b){a=this.evaluate(a);if(typeof a!=="string")throw Error("Invalid key name given for assignment.");var c=this.K;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=this.evaluate(b);c.set(a,d);return d}function Nd(){var a=Ca.apply(0,arguments),b=this.K.tb(),c=Ta(b,a);if(c instanceof Fa)return c}function Od(){return Hd}
function Pd(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof Fa)return d}}function Qd(){for(var a=this.K,b=0;b<arguments.length-1;b+=2){var c=arguments[b];if(typeof c==="string"){var d=this.evaluate(arguments[b+1]);a.nh(c,d)}}}function Rd(){return Id}function Sd(a,b){return new Fa(a,this.evaluate(b))}
function Td(a,b){var c=Ca.apply(2,arguments),d;if(Ra(17)){for(var e=[],f=this.evaluate(b),g=0;g<f.length;g++)e.push(f[g]);d=new rd(e)}else{d=new rd;for(var h=this.evaluate(b),m=0;m<h.length;m++)d.push(h[m])}var n=[51,a,d].concat(xa(c));this.K.add(a,this.evaluate(n))}function Ud(a,b){return this.evaluate(a)/this.evaluate(b)}function Vd(a,b){var c=this.evaluate(a),d=this.evaluate(b),e=c instanceof Ad,f=d instanceof Ad;return e||f?e&&f?c.getValue()===d.getValue():!1:c==d}
function Wd(){for(var a,b=0;b<arguments.length;b++)a=this.evaluate(arguments[b]);return a}function Xd(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=Ta(f,d);if(g instanceof Fa){if(g.type==="break")break;if(g.type==="return")return g}}}function Yd(a,b,c){if(typeof b==="string")return Xd(a,function(){return b.length},function(f){return f},c);if(b instanceof Xa||b instanceof Cd||b instanceof rd||b instanceof vd){var d=b.Aa(),e=d.length;return Xd(a,function(){return e},function(f){return d[f]},c)}}
function Zd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Yd(function(h){g.set(d,h);return g},e,f)}function $d(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Yd(function(h){var m=g.tb();m.nh(d,h);return m},e,f)}function ae(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Yd(function(h){var m=g.tb();m.add(d,h);return m},e,f)}
function be(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return ce(function(h){g.set(d,h);return g},e,f)}function de(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return ce(function(h){var m=g.tb();m.nh(d,h);return m},e,f)}function ee(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return ce(function(h){var m=g.tb();m.add(d,h);return m},e,f)}
function ce(a,b,c){if(typeof b==="string")return Xd(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof rd)return Xd(a,function(){return b.length()},function(d){return b.get(d)},c);throw Oa(Error("The value is not iterable."));}
function fe(a,b,c,d){function e(q,r){for(var t=0;t<f.length();t++){var u=f.get(t);r.add(u,q.get(u))}}var f=this.evaluate(a);if(!(f instanceof rd))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.K,h=this.evaluate(d),m=g.tb();for(e(g,m);Ua(m,b);){var n=Ta(m,h);if(n instanceof Fa){if(n.type==="break")break;if(n.type==="return")return n}var p=g.tb();e(m,p);Ua(p,c);m=p}}
function ge(a,b){var c=Ca.apply(2,arguments),d=this.K,e=this.evaluate(b);if(!(e instanceof rd))throw Error("Error: non-List value given for Fn argument names.");return new vd(a,function(){return function(){var f=Ca.apply(0,arguments),g=d.tb();g.ub()===void 0&&g.Ld(this.K.ub());for(var h=[],m=0;m<f.length;m++){var n=this.evaluate(f[m]);h[m]=n}for(var p=e.get("length"),q=0;q<p;q++)q<h.length?g.add(e.get(q),h[q]):g.add(e.get(q),void 0);g.add("arguments",new rd(h));var r=Ta(g,c);if(r instanceof Fa)return r.type===
"return"?r.data:r}}())}function he(a){var b=this.evaluate(a),c=this.K;if(ie&&!c.has(b))throw new ReferenceError(b+" is not defined.");return c.get(b)}
function je(a,b){var c,d=this.evaluate(a),e=this.evaluate(b);if(d===void 0||d===null)throw Oa(Error("TypeError: Cannot read properties of "+d+" (reading '"+e+"')"));if(d instanceof Xa||d instanceof Cd||d instanceof rd||d instanceof vd)c=d.get(e);else if(typeof d==="string")e==="length"?c=d.length:qd(e)&&(c=d[e]);else if(d instanceof Ad)return;return c}function ke(a,b){return this.evaluate(a)>this.evaluate(b)}function le(a,b){return this.evaluate(a)>=this.evaluate(b)}
function me(a,b){var c=this.evaluate(a),d=this.evaluate(b);c instanceof Ad&&(c=c.getValue());d instanceof Ad&&(d=d.getValue());return c===d}function ne(a,b){return!me.call(this,a,b)}function oe(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=Ta(this.K,d);if(e instanceof Fa)return e}var ie=!1;
function pe(a,b){return this.evaluate(a)<this.evaluate(b)}function qe(a,b){return this.evaluate(a)<=this.evaluate(b)}function re(){if(Ra(17)){for(var a=[],b=0;b<arguments.length;b++){var c=this.evaluate(arguments[b]);a.push(c)}return new rd(a)}for(var d=new rd,e=0;e<arguments.length;e++){var f=this.evaluate(arguments[e]);d.push(f)}return d}function se(){for(var a=new Xa,b=0;b<arguments.length-1;b+=2){var c=String(this.evaluate(arguments[b])),d=this.evaluate(arguments[b+1]);a.set(c,d)}return a}
function te(a,b){return this.evaluate(a)%this.evaluate(b)}function ue(a,b){return this.evaluate(a)*this.evaluate(b)}function ve(a){return-this.evaluate(a)}function we(a){return!this.evaluate(a)}function xe(a,b){return!Vd.call(this,a,b)}function ye(){return null}function ze(a,b){return this.evaluate(a)||this.evaluate(b)}function Ae(a,b){var c=this.evaluate(a);this.evaluate(b);return c}function Be(a){return this.evaluate(a)}function Ce(){return Ca.apply(0,arguments)}
function De(a){return new Fa("return",this.evaluate(a))}function Ee(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(d===null||d===void 0)throw Oa(Error("TypeError: Can't set property "+e+" of "+d+"."));(d instanceof vd||d instanceof rd||d instanceof Xa)&&d.set(String(e),f);return f}function Fe(a,b){return this.evaluate(a)-this.evaluate(b)}
function Ge(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!Array.isArray(e)||!Array.isArray(f))throw Error("Error: Malformed switch instruction.");for(var g,h=!1,m=0;m<e.length;m++)if(h||d===this.evaluate(e[m]))if(g=this.evaluate(f[m]),g instanceof Fa){var n=g.type;if(n==="break")return;if(n==="return"||n==="continue")return g}else h=!0;if(f.length===e.length+1&&(g=this.evaluate(f[f.length-1]),g instanceof Fa&&(g.type==="return"||g.type==="continue")))return g}
function He(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)}function Ie(a){var b=this.evaluate(a);return b instanceof vd?"function":typeof b}function Je(){for(var a=this.K,b=0;b<arguments.length;b++){var c=arguments[b];typeof c!=="string"||a.add(c,void 0)}}
function Ke(a,b,c,d){var e=this.evaluate(d);if(this.evaluate(c)){var f=Ta(this.K,e);if(f instanceof Fa){if(f.type==="break")return;if(f.type==="return")return f}}for(;this.evaluate(a);){var g=Ta(this.K,e);if(g instanceof Fa){if(g.type==="break")break;if(g.type==="return")return g}this.evaluate(b)}}function Le(a){return~Number(this.evaluate(a))}function Me(a,b){return Number(this.evaluate(a))<<Number(this.evaluate(b))}function Ne(a,b){return Number(this.evaluate(a))>>Number(this.evaluate(b))}
function Oe(a,b){return Number(this.evaluate(a))>>>Number(this.evaluate(b))}function Pe(a,b){return Number(this.evaluate(a))&Number(this.evaluate(b))}function Qe(a,b){return Number(this.evaluate(a))^Number(this.evaluate(b))}function Re(a,b){return Number(this.evaluate(a))|Number(this.evaluate(b))}function Se(){}
function Te(a,b,c){try{var d=this.evaluate(b);if(d instanceof Fa)return d}catch(h){if(!(h instanceof Na&&h.Sl))throw h;var e=this.K.tb();a!==""&&(h instanceof Na&&(h=h.om),e.add(a,new Ad(h)));var f=this.evaluate(c),g=Ta(e,f);if(g instanceof Fa)return g}}function Ue(a,b){var c,d;try{d=this.evaluate(a)}catch(f){if(!(f instanceof Na&&f.Sl))throw f;c=f}var e=this.evaluate(b);if(e instanceof Fa)return e;if(c)throw c;if(d instanceof Fa)return d};var We=function(){this.C=new Wa;Ve(this)};We.prototype.execute=function(a){return this.C.Bj(a)};var Ve=function(a){var b=function(c,d){var e=new wd(String(c),d);e.Wa();var f=String(c);a.C.C.set(f,e);Sa.set(f,e)};b("map",se);b("and",ed);b("contains",hd);b("equals",fd);b("or",gd);b("startsWith",id);b("variable",jd)};We.prototype.Nb=function(a){this.C.Nb(a)};var Ye=function(){this.H=!1;this.C=new Wa;Xe(this);this.H=!0};Ye.prototype.execute=function(a){return Ze(this.C.Bj(a))};var $e=function(a,b,c){return Ze(a.C.ho(b,c))};Ye.prototype.Wa=function(){this.C.Wa()};
var Xe=function(a){var b=function(c,d){var e=String(c),f=new wd(e,d);f.Wa();a.C.C.set(e,f);Sa.set(e,f)};b(0,Jd);b(1,Kd);b(2,Ld);b(3,Md);b(56,Pe);b(57,Me);b(58,Le);b(59,Re);b(60,Ne);b(61,Oe);b(62,Qe);b(53,Nd);b(4,Od);b(5,Pd);b(68,Te);b(52,Qd);b(6,Rd);b(49,Sd);b(7,re);b(8,se);b(9,Pd);b(50,Td);b(10,Ud);b(12,Vd);b(13,Wd);b(67,Ue);b(51,ge);b(47,Zd);b(54,$d);b(55,ae);b(63,fe);b(64,be);b(65,de);b(66,ee);b(15,he);b(16,je);b(17,je);b(18,ke);b(19,le);b(20,me);b(21,ne);b(22,oe);b(23,pe);b(24,qe);b(25,te);b(26,
ue);b(27,ve);b(28,we);b(29,xe);b(45,ye);b(30,ze);b(32,Ae);b(33,Ae);b(34,Be);b(35,Be);b(46,Ce);b(36,De);b(43,Ee);b(37,Fe);b(38,Ge);b(39,He);b(40,Ie);b(44,Se);b(41,Je);b(42,Ke)};Ye.prototype.Cd=function(){return this.C.Cd()};Ye.prototype.Nb=function(a){this.C.Nb(a)};Ye.prototype.Vc=function(a){this.C.Vc(a)};
function Ze(a){if(a instanceof Fa||a instanceof vd||a instanceof rd||a instanceof Xa||a instanceof Cd||a instanceof Ad||a===null||a===void 0||typeof a==="string"||typeof a==="number"||typeof a==="boolean")return a};var af=function(a){this.message=a};function bf(a){a.Or=!0;return a};var cf=bf(function(a){return typeof a==="string"});function df(a){var b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a];return b===void 0?new af("Value "+a+" can not be encoded in web-safe base64 dictionary."):b};function ef(a){switch(a){case 1:return"1";case 2:case 4:return"0";default:return"-"}};var ff=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function gf(a,b){for(var c="",d=!0;a>7;){var e=a&31;a>>=5;d?d=!1:e|=32;c=""+df(e)+c}a<<=2;d||(a|=32);return c=""+df(a|b)+c}
function hf(a,b){var c;var d=a.Uc,e=a.Ah;d===void 0?c="":(e||(e=0),c=""+gf(1,1)+df(d<<2|e));var f=a.Rl,g=a.Po,h="4"+c+(f?""+gf(2,1)+df(f):"")+(g?""+gf(12,1)+df(g):""),m,n=a.Cj;m=n&&ff.test(n)?""+gf(3,2)+n:"";var p,q=a.yj;p=q?""+gf(4,1)+df(q):"";var r;var t=a.ctid;if(t&&b){var u=gf(5,3),v=t.split("-"),w=v[0].toUpperCase();if(w!=="GTM"&&w!=="OPT")r="";else{var y=v[1];r=""+u+df(1+y.length)+(a.fm||0)+y}}else r="";var A=a.Hq,C=a.we,D=a.Oa,G=a.Sr,I=h+m+p+r+(A?""+gf(6,1)+df(A):"")+(C?""+gf(7,3)+df(C.length)+
C:"")+(D?""+gf(8,3)+df(D.length)+D:"")+(G?""+gf(9,3)+df(G.length)+G:""),M;var T=a.Tl;T=T===void 0?{}:T;for(var da=[],O=l(Object.keys(T)),V=O.next();!V.done;V=O.next()){var ia=V.value;da[Number(ia)]=T[ia]}if(da.length){var ka=gf(10,3),X;if(da.length===0)X=df(0);else{for(var Y=[],ja=0,ya=!1,sa=0;sa<da.length;sa++){ya=!0;var Va=sa%6;da[sa]&&(ja|=1<<Va);Va===5&&(Y.push(df(ja)),ja=0,ya=!1)}ya&&Y.push(df(ja));X=Y.join("")}var Ya=X;M=""+ka+df(Ya.length)+Ya}else M="";var Ub=a.qm,ac=a.yq;return I+M+(Ub?""+
gf(11,3)+df(Ub.length)+Ub:"")+(ac?""+gf(13,3)+df(ac.length)+ac:"")};var jf=function(){function a(b){return{toString:function(){return b}}}return{Rm:a("consent"),Rj:a("convert_case_to"),Sj:a("convert_false_to"),Tj:a("convert_null_to"),Uj:a("convert_true_to"),Vj:a("convert_undefined_to"),Uq:a("debug_mode_metadata"),Ta:a("function"),Ai:a("instance_name"),ko:a("live_only"),lo:a("malware_disabled"),METADATA:a("metadata"),oo:a("original_activity_id"),rr:a("original_vendor_template_id"),qr:a("once_on_load"),no:a("once_per_event"),ql:a("once_per_load"),vr:a("priority_override"),
yr:a("respected_consent_types"),Bl:a("setup_tags"),kh:a("tag_id"),Jl:a("teardown_tags")}}();var Ff;var Gf=[],Hf=[],If=[],Jf=[],Kf=[],Lf,Nf,Of;function Pf(a){Of=Of||a}
function Qf(){for(var a=data.resource||{},b=a.macros||[],c=0;c<b.length;c++)Gf.push(b[c]);for(var d=a.tags||[],e=0;e<d.length;e++)Jf.push(d[e]);for(var f=a.predicates||[],g=0;g<f.length;g++)If.push(f[g]);for(var h=a.rules||[],m=0;m<h.length;m++){for(var n=h[m],p={},q=0;q<n.length;q++){var r=n[q][0];p[r]=Array.prototype.slice.call(n[q],1);r!=="if"&&r!=="unless"||Rf(p[r])}Hf.push(p)}}
function Rf(a){}var Sf,Tf=[],Uf=[];function Vf(a,b){var c={};c[jf.Ta]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c}
function Wf(a,b,c){try{return Nf(Xf(a,b,c))}catch(d){JSON.stringify(a)}return 2}
var Xf=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=Yf(a[e],b,c));return d},Yf=function(a,b,c){if(Array.isArray(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(Yf(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=Gf[f];if(!g||b.isBlocked(g))return;c[f]=!0;var h=String(g[jf.Ai]);try{var m=Xf(g,b,c);m.vtp_gtmEventId=b.id;b.priorityId&&(m.vtp_gtmPriorityId=b.priorityId);d=Zf(m,{event:b,index:f,type:2,
name:h});Sf&&(d=Sf.So(d,m))}catch(A){b.logMacroError&&b.logMacroError(A,Number(f),h),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[Yf(a[n],b,c)]=Yf(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=Yf(a[q],b,c);Of&&(p=p||Of.Up(r));d.push(r)}return Of&&p?Of.Xo(d):d.join("");case "escape":d=Yf(a[1],b,c);if(Of&&Array.isArray(a[1])&&a[1][0]==="macro"&&Of.Vp(a))return Of.mq(d);d=String(d);for(var t=2;t<a.length;t++)qf[a[t]]&&(d=qf[a[t]](d));return d;
case "tag":var u=a[1];if(!Jf[u])throw Error("Unable to resolve tag reference "+u+".");return{Xl:a[2],index:u};case "zb":var v={arg0:a[2],arg1:a[3],ignore_case:a[5]};v[jf.Ta]=a[1];var w=Wf(v,b,c),y=!!a[4];return y||w!==2?y!==(w===1):null;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");}}return a},Zf=function(a,b){var c=a[jf.Ta],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=Lf[c],f=b&&b.type===2&&(d==null?void 0:d.reportMacroDiscrepancy)&&
e&&Tf.indexOf(c)!==-1,g={},h={},m;for(m in a)a.hasOwnProperty(m)&&Fb(m,"vtp_")&&(e&&(g[m]=a[m]),!e||f)&&(h[m.substring(4)]=a[m]);e&&d&&d.cachedModelValues&&(g.vtp_gtmCachedValues=d.cachedModelValues);if(b){if(b.name==null){var n;a:{var p=b.type,q=b.index;if(q==null)n="";else{var r;switch(p){case 2:r=Gf[q];break;case 1:r=Jf[q];break;default:n="";break a}var t=r&&r[jf.Ai];n=t?String(t):""}}b.name=n}e&&(g.vtp_gtmEntityIndex=b.index,g.vtp_gtmEntityName=b.name)}var u,v,w;if(f&&Uf.indexOf(c)===-1){Uf.push(c);
var y=Ab();u=e(g);var A=Ab()-y,C=Ab();v=Ff(c,h,b);w=A-(Ab()-C)}else if(e&&(u=e(g)),!e||f)v=Ff(c,h,b);f&&d&&(d.reportMacroDiscrepancy(d.id,c,void 0,!0),pd(u)?(Array.isArray(u)?Array.isArray(v):nd(u)?nd(v):typeof u==="function"?typeof v==="function":u===v)||d.reportMacroDiscrepancy(d.id,c):u!==v&&d.reportMacroDiscrepancy(d.id,c),w!==void 0&&d.reportMacroDiscrepancy(d.id,c,w));return e?u:v};var $f=function(a,b,c){var d;d=Error.call(this,c);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.permissionId=a;this.parameters=b;this.name="PermissionError"};va($f,Error);$f.prototype.getMessage=function(){return this.message};function ag(a,b){if(Array.isArray(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)ag(a[c],b[c])}};function bg(){return function(a,b){var c;var d=cg;a instanceof Na?(a.C=d,c=a):c=new Na(a,d);var e=c;b&&e.debugInfo.push(b);throw e;}}function cg(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;b>0;b--)nb(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;c>0;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};function dg(a){function b(r){for(var t=0;t<r.length;t++)d[r[t]]=!0}for(var c=[],d=[],e=eg(a),f=0;f<Hf.length;f++){var g=Hf[f],h=fg(g,e);if(h){for(var m=g.add||[],n=0;n<m.length;n++)c[m[n]]=!0;b(g.block||[])}else h===null&&b(g.block||[]);}for(var p=[],q=0;q<Jf.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p}
function fg(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(e===0)return!1;if(e===2)return null}for(var f=a.unless||[],g=0;g<f.length;g++){var h=b(f[g]);if(h===2)return null;if(h===1)return!1}return!0}function eg(a){var b=[];return function(c){b[c]===void 0&&(b[c]=Wf(If[c],a));return b[c]}};function gg(a,b){b[jf.Rj]&&typeof a==="string"&&(a=b[jf.Rj]===1?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(jf.Tj)&&a===null&&(a=b[jf.Tj]);b.hasOwnProperty(jf.Vj)&&a===void 0&&(a=b[jf.Vj]);b.hasOwnProperty(jf.Uj)&&a===!0&&(a=b[jf.Uj]);b.hasOwnProperty(jf.Sj)&&a===!1&&(a=b[jf.Sj]);return a};var hg=function(){this.C={}},jg=function(a,b){var c=ig.C,d;(d=c.C)[a]!=null||(d[a]=[]);c.C[a].push(function(){return b.apply(null,xa(Ca.apply(0,arguments)))})};function kg(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e](b,c,d),g+="."}catch(h){g=typeof h==="string"?g+(": "+h):h instanceof Error?g+(": "+h.message):g+"."}if(!f)throw new $f(c,d,g);}}
function lg(a,b,c){return function(d){if(d){var e=a.C[d],f=a.C.all;if(e||f){var g=c.apply(void 0,[d].concat(xa(Ca.apply(1,arguments))));kg(e,b,d,g);kg(f,b,d,g)}}}};var pg=function(){var a=data.permissions||{},b=mg.ctid,c=this;this.H={};this.C=new hg;var d={},e={},f=lg(this.C,b,function(g){return g&&d[g]?d[g].apply(void 0,[g].concat(xa(Ca.apply(1,arguments)))):{}});tb(a,function(g,h){function m(p){var q=Ca.apply(1,arguments);if(!n[p])throw ng(p,{},"The requested additional permission "+p+" is not configured.");f.apply(null,[p].concat(xa(q)))}var n={};tb(h,function(p,q){var r=og(p,q);n[p]=r.assert;d[p]||(d[p]=r.T);r.Pl&&!e[p]&&(e[p]=r.Pl)});c.H[g]=function(p,
q){var r=n[p];if(!r)throw ng(p,{},"The requested permission "+p+" is not configured.");var t=Array.prototype.slice.call(arguments,0);r.apply(void 0,t);f.apply(void 0,t);var u=e[p];u&&u.apply(null,[m].concat(xa(t.slice(1))))}})},qg=function(a){return ig.H[a]||function(){}};
function og(a,b){var c=Vf(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=ng;try{return Zf(c)}catch(d){return{assert:function(e){throw new $f(e,{},"Permission "+e+" is unknown.");},T:function(){throw new $f(a,{},"Permission "+a+" is unknown.");}}}}function ng(a,b,c){return new $f(a,b,c)};var rg=!1;var sg={};sg.Im=wb('');sg.kp=wb('');function xg(a,b){if(a==="")return b;var c=Number(a);return isNaN(c)?b:c};var yg=[];function zg(a){switch(a){case 1:return 0;case 216:return 15;case 222:return 18;case 38:return 12;case 219:return 9;case 220:return 10;case 53:return 1;case 54:return 2;case 52:return 6;case 203:return 16;case 75:return 3;case 103:return 13;case 197:return 14;case 114:return 11;case 116:return 4;case 221:return 17;case 135:return 8;case 136:return 5}}function Ag(a,b){yg[a]=b;var c=zg(a);c!==void 0&&(Pa[c]=b)}function B(a){Ag(a,!0)}B(39);
B(34);B(35);B(36);B(56);
B(145);B(153);
B(144);
B(120);B(5);B(111);
B(139);B(87);B(92);B(159);
B(132);B(20);B(72);
B(113);B(154);B(116);Ag(23,!1),B(24);Qa[1]=xg('1',6E4);Qa[3]=xg('10',1);Qa[2]=xg('',50);B(29);
Bg(26,25);
B(37);B(9);
B(91);B(123);
B(158);B(71);B(136);B(127);B(27);B(69);B(135);
B(95);B(38);B(103);B(112);
B(63);
B(152);
B(101);B(122);B(121);
B(134);
B(22);B(19);
B(90);B(114);
B(59);B(208);
B(171);
B(175);
B(185);B(186);

B(192);B(200);B(202);function E(a){return!!yg[a]}function Bg(a,b){for(var c=!1,d=!1,e=0;c===d;)if(c=((Math.random()*4294967296|0)&1)===0,d=((Math.random()*4294967296|0)&1)===0,e++,e>30)return;c?B(b):B(a)};var Dg={},Eg=(Dg.uaa=!0,Dg.uab=!0,Dg.uafvl=!0,Dg.uamb=!0,Dg.uam=!0,Dg.uap=!0,Dg.uapv=!0,Dg.uaw=!0,Dg);
var Mg=function(a,b){for(var c=0;c<b.length;c++){var d=a,e=b[c];if(!Kg.exec(e))throw Error("Invalid key wildcard");var f=e.indexOf(".*"),g=f!==-1&&f===e.length-2,h=g?e.slice(0,e.length-2):e,m;a:if(d.length===0)m=!1;else{for(var n=d.split("."),p=0;p<n.length;p++)if(!Lg.exec(n[p])){m=!1;break a}m=!0}if(!m||h.length>d.length||!g&&d.length!==e.length?0:g?Fb(d,h)&&(d===h||d.charAt(h.length)==="."):d===h)return!0}return!1},Lg=/^[a-z$_][\w-$]*$/i,Kg=/^(?:[a-z_$][a-z-_$0-9]*\.)*[a-z_$][a-z-_$0-9]*(?:\.\*)?$/i;
var Ng=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function Og(a,b){var c=String(a),d=String(b),e=c.length-d.length;return e>=0&&c.indexOf(d,e)===e}function Pg(a,b){return String(a).split(",").indexOf(String(b))>=0}var Qg=new sb;function Rg(a,b,c){var d=c?"i":void 0;try{var e=String(b)+String(d),f=Qg.get(e);f||(f=new RegExp(b,d),Qg.set(e,f));return f.test(a)}catch(g){return!1}}function Sg(a,b){return String(a).indexOf(String(b))>=0}
function Tg(a,b){return String(a)===String(b)}function Ug(a,b){return Number(a)>=Number(b)}function Vg(a,b){return Number(a)<=Number(b)}function Wg(a,b){return Number(a)>Number(b)}function Xg(a,b){return Number(a)<Number(b)}function Yg(a,b){return Fb(String(a),String(b))};var eh=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|PixieMap|List|OpaqueValue)$/i,fh={Fn:"function",PixieMap:"Object",List:"Array"};
function gh(a,b){for(var c=["input:!*"],d=0;d<c.length;d++){var e=eh.exec(c[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g=e[2]==="!",h=e[3],m=b[d];if(m==null){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if(h!=="*"){var n=typeof m;m instanceof vd?n="Fn":m instanceof rd?n="List":m instanceof Xa?n="PixieMap":m instanceof Cd?n="PixiePromise":m instanceof Ad&&(n="OpaqueValue");if(n!==h)throw Error("Error in "+a+". Argument "+f+" has type "+((fh[n]||n)+", which does not match required type ")+
((fh[h]||h)+"."));}}}function F(a,b,c){for(var d=[],e=l(c),f=e.next();!f.done;f=e.next()){var g=f.value;g instanceof vd?d.push("function"):g instanceof rd?d.push("Array"):g instanceof Xa?d.push("Object"):g instanceof Cd?d.push("Promise"):g instanceof Ad?d.push("OpaqueValue"):d.push(typeof g)}return Error("Argument error in "+a+". Expected argument types ["+(b.join(",")+"], but received [")+(d.join(",")+"]."))}function hh(a){return a instanceof Xa}function ih(a){return hh(a)||a===null||jh(a)}
function kh(a){return a instanceof vd}function lh(a){return kh(a)||a===null||jh(a)}function mh(a){return a instanceof rd}function nh(a){return a instanceof Ad}function oh(a){return typeof a==="string"}function ph(a){return oh(a)||a===null||jh(a)}function qh(a){return typeof a==="boolean"}function rh(a){return qh(a)||jh(a)}function sh(a){return qh(a)||a===null||jh(a)}function th(a){return typeof a==="number"}function jh(a){return a===void 0};function uh(a){return""+a}
function vh(a,b){var c=[];return c};function wh(a,b){var c=new vd(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=this.evaluate(d[e]);try{return b.apply(this,d)}catch(g){throw Oa(g);}});c.Wa();return c}
function xh(a,b){var c=new Xa,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];lb(e)?c.set(d,wh(a+"_"+d,e)):nd(e)?c.set(d,xh(a+"_"+d,e)):(nb(e)||mb(e)||typeof e==="boolean")&&c.set(d,e)}c.Wa();return c};function yh(a,b){if(!oh(a))throw F(this.getName(),["string"],arguments);if(!ph(b))throw F(this.getName(),["string","undefined"],arguments);var c={},d=new Xa;return d=xh("AssertApiSubject",
c)};function zh(a,b){if(!ph(b))throw F(this.getName(),["string","undefined"],arguments);if(a instanceof Cd)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new Xa;return d=xh("AssertThatSubject",c)};function Ah(a){return function(){for(var b=Ca.apply(0,arguments),c=[],d=this.K,e=0;e<b.length;++e)c.push(Dd(b[e],d));return Ed(a.apply(null,c))}}function Bh(){for(var a=Math,b=Ch,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=Ah(a[e].bind(a)))}return c};function Dh(a){return a!=null&&Fb(a,"__cvt_")};function Eh(a){var b;return b};function Fh(a){var b;if(!oh(a))throw F(this.getName(),["string"],arguments);try{b=decodeURIComponent(a)}catch(c){}return b};function Gh(a){try{return encodeURI(a)}catch(b){}};function Hh(a){try{return encodeURIComponent(String(a))}catch(b){}};function Mh(a){if(!ph(a))throw F(this.getName(),["string|undefined"],arguments);};function Nh(a,b){if(!th(a)||!th(b))throw F(this.getName(),["number","number"],arguments);return qb(a,b)};function Oh(){return(new Date).getTime()};function Ph(a){if(a===null)return"null";if(a instanceof rd)return"array";if(a instanceof vd)return"function";if(a instanceof Ad){var b=a.getValue();if((b==null?void 0:b.constructor)===void 0||b.constructor.name===void 0){var c=String(b);return c.substring(8,c.length-1)}return String(b.constructor.name)}return typeof a};function Qh(a){function b(c){return function(d){try{return c(d)}catch(e){(rg||sg.Im)&&a.call(this,e.message)}}}return{parse:b(function(c){return Ed(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(Dd(c))}),publicName:"JSON"}};function Rh(a){return vb(Dd(a,this.K))};function Sh(a){return Number(Dd(a,this.K))};function Th(a){return a===null?"null":a===void 0?"undefined":a.toString()};function Uh(a,b,c){var d=null,e=!1;return e?d:null};var Ch="floor ceil round max min abs pow sqrt".split(" ");function Vh(){var a={};return{wp:function(b){return a.hasOwnProperty(b)?a[b]:void 0},Em:function(b,c){a[b]=c},reset:function(){a={}}}}function Wh(a,b){return function(){return vd.prototype.invoke.apply(a,[b].concat(xa(Ca.apply(0,arguments))))}}
function Xh(a,b){if(!oh(a))throw F(this.getName(),["string","any"],arguments);}
function Yh(a,b){if(!oh(a)||!hh(b))throw F(this.getName(),["string","PixieMap"],arguments);};var Zh={};
Zh.keys=function(a){return new rd};
Zh.values=function(a){return new rd};
Zh.entries=function(a){return new rd};
Zh.freeze=function(a){return a};Zh.delete=function(a,b){return!1};function H(a,b){var c=Ca.apply(2,arguments),d=a.K.ub();if(!d)throw Error("Missing program state.");if(d.uq){try{d.Ql.apply(null,[b].concat(xa(c)))}catch(e){throw fb("TAGGING",21),e;}return}d.Ql.apply(null,[b].concat(xa(c)))};var ai=function(){this.H={};this.C={};this.N=!0;};ai.prototype.get=function(a,b){var c=this.contains(a)?this.H[a]:void 0;return c};ai.prototype.contains=function(a){return this.H.hasOwnProperty(a)};
ai.prototype.add=function(a,b,c){if(this.contains(a))throw Error("Attempting to add a function which already exists: "+a+".");if(this.C.hasOwnProperty(a))throw Error("Attempting to add an API with an existing private API name: "+a+".");this.H[a]=c?void 0:lb(b)?wh(a,b):xh(a,b)};function bi(a,b){var c=void 0;return c};function ci(){var a={};return a};var J={m:{La:"ad_personalization",U:"ad_storage",V:"ad_user_data",ja:"analytics_storage",hc:"region",da:"consent_updated",rg:"wait_for_update",dn:"app_remove",fn:"app_store_refund",gn:"app_store_subscription_cancel",hn:"app_store_subscription_convert",jn:"app_store_subscription_renew",kn:"consent_update",Zj:"add_payment_info",bk:"add_shipping_info",Pd:"add_to_cart",Qd:"remove_from_cart",dk:"view_cart",Xc:"begin_checkout",Rd:"select_item",kc:"view_item_list",Gc:"select_promotion",mc:"view_promotion",
lb:"purchase",Sd:"refund",yb:"view_item",ek:"add_to_wishlist",ln:"exception",mn:"first_open",nn:"first_visit",qa:"gtag.config",Eb:"gtag.get",on:"in_app_purchase",Yc:"page_view",pn:"screen_view",qn:"session_start",rn:"source_update",sn:"timing_complete",tn:"track_social",Td:"user_engagement",un:"user_id_update",Le:"gclid_link_decoration_source",Me:"gclid_storage_source",nc:"gclgb",mb:"gclid",fk:"gclid_len",Ud:"gclgs",Vd:"gcllp",Wd:"gclst",ya:"ads_data_redaction",Ne:"gad_source",Oe:"gad_source_src",
Zc:"gclid_url",gk:"gclsrc",Pe:"gbraid",Xd:"wbraid",Ga:"allow_ad_personalization_signals",yg:"allow_custom_scripts",Qe:"allow_direct_google_requests",zg:"allow_display_features",Ag:"allow_enhanced_conversions",Ob:"allow_google_signals",nb:"allow_interest_groups",vn:"app_id",wn:"app_installer_id",xn:"app_name",yn:"app_version",Pb:"auid",zn:"auto_detection_enabled",bd:"aw_remarketing",Ph:"aw_remarketing_only",Bg:"discount",Cg:"aw_feed_country",Dg:"aw_feed_language",ra:"items",Eg:"aw_merchant_id",hk:"aw_basket_type",
Re:"campaign_content",Se:"campaign_id",Te:"campaign_medium",Ue:"campaign_name",Ve:"campaign",We:"campaign_source",Xe:"campaign_term",Qb:"client_id",ik:"rnd",Qh:"consent_update_type",An:"content_group",Bn:"content_type",Rb:"conversion_cookie_prefix",Ye:"conversion_id",Qa:"conversion_linker",Rh:"conversion_linker_disabled",dd:"conversion_api",Fg:"cookie_deprecation",ob:"cookie_domain",pb:"cookie_expires",zb:"cookie_flags",ed:"cookie_name",Sb:"cookie_path",eb:"cookie_prefix",Hc:"cookie_update",fd:"country",
Ra:"currency",Sh:"customer_buyer_stage",Ze:"customer_lifetime_value",Th:"customer_loyalty",Uh:"customer_ltv_bucket",af:"custom_map",Vh:"gcldc",gd:"dclid",jk:"debug_mode",oa:"developer_id",Cn:"disable_merchant_reported_purchases",hd:"dc_custom_params",Dn:"dc_natural_search",kk:"dynamic_event_settings",lk:"affiliation",Gg:"checkout_option",Wh:"checkout_step",mk:"coupon",bf:"item_list_name",Xh:"list_name",En:"promotions",Yd:"shipping",Yh:"tax",Hg:"engagement_time_msec",Ig:"enhanced_client_id",Zh:"enhanced_conversions",
nk:"enhanced_conversions_automatic_settings",cf:"estimated_delivery_date",ai:"euid_logged_in_state",df:"event_callback",Gn:"event_category",Tb:"event_developer_id_string",Hn:"event_label",jd:"event",Jg:"event_settings",Kg:"event_timeout",In:"description",Jn:"fatal",Kn:"experiments",bi:"firebase_id",Zd:"first_party_collection",Lg:"_x_20",qc:"_x_19",pk:"fledge_drop_reason",qk:"fledge",rk:"flight_error_code",sk:"flight_error_message",tk:"fl_activity_category",uk:"fl_activity_group",di:"fl_advertiser_id",
vk:"fl_ar_dedupe",ef:"match_id",wk:"fl_random_number",xk:"tran",yk:"u",Mg:"gac_gclid",ae:"gac_wbraid",zk:"gac_wbraid_multiple_conversions",Ak:"ga_restrict_domain",ei:"ga_temp_client_id",Ln:"ga_temp_ecid",kd:"gdpr_applies",Bk:"geo_granularity",Ic:"value_callback",rc:"value_key",sc:"google_analysis_params",be:"_google_ng",ce:"google_signals",Ck:"google_tld",ff:"gpp_sid",hf:"gpp_string",Ng:"groups",Dk:"gsa_experiment_id",jf:"gtag_event_feature_usage",Ek:"gtm_up",Jc:"iframe_state",kf:"ignore_referrer",
fi:"internal_traffic_results",Fk:"_is_fpm",Kc:"is_legacy_converted",Lc:"is_legacy_loaded",Og:"is_passthrough",ld:"_lps",Ab:"language",Pg:"legacy_developer_id_string",Sa:"linker",de:"accept_incoming",uc:"decorate_forms",la:"domains",Mc:"url_position",ee:"merchant_feed_label",fe:"merchant_feed_language",he:"merchant_id",Gk:"method",Mn:"name",Hk:"navigation_type",lf:"new_customer",Qg:"non_interaction",Nn:"optimize_id",Ik:"page_hostname",nf:"page_path",Ya:"page_referrer",Fb:"page_title",Jk:"passengers",
Kk:"phone_conversion_callback",On:"phone_conversion_country_code",Lk:"phone_conversion_css_class",Pn:"phone_conversion_ids",Mk:"phone_conversion_number",Nk:"phone_conversion_options",Qn:"_platinum_request_status",Rn:"_protected_audience_enabled",ie:"quantity",Rg:"redact_device_info",gi:"referral_exclusion_definition",Xq:"_request_start_time",Vb:"restricted_data_processing",Sn:"retoken",Tn:"sample_rate",hi:"screen_name",Nc:"screen_resolution",Ok:"_script_source",Un:"search_term",qb:"send_page_view",
md:"send_to",nd:"server_container_url",pf:"session_duration",Sg:"session_engaged",ii:"session_engaged_time",Wb:"session_id",Tg:"session_number",qf:"_shared_user_id",je:"delivery_postal_code",Yq:"_tag_firing_delay",Zq:"_tag_firing_time",ar:"temporary_client_id",ji:"_timezone",ki:"topmost_url",Vn:"tracking_id",li:"traffic_type",Ma:"transaction_id",vc:"transport_url",Pk:"trip_type",pd:"update",Gb:"url_passthrough",Qk:"uptgs",rf:"_user_agent_architecture",tf:"_user_agent_bitness",uf:"_user_agent_full_version_list",
vf:"_user_agent_mobile",wf:"_user_agent_model",xf:"_user_agent_platform",yf:"_user_agent_platform_version",zf:"_user_agent_wow64",fb:"user_data",mi:"user_data_auto_latency",ni:"user_data_auto_meta",oi:"user_data_auto_multi",ri:"user_data_auto_selectors",si:"user_data_auto_status",wc:"user_data_mode",Ug:"user_data_settings",Na:"user_id",Xb:"user_properties",Rk:"_user_region",Af:"us_privacy_string",za:"value",Sk:"wbraid_multiple_conversions",sd:"_fpm_parameters",yi:"_host_name",bl:"_in_page_command",
fl:"_ip_override",ml:"_is_passthrough_cid",xc:"non_personalized_ads",Ji:"_sst_parameters",oc:"conversion_label",Ca:"page_location",Ub:"global_developer_id_string",od:"tc_privacy_string"}};var di={},ei=(di[J.m.da]="gcu",di[J.m.nc]="gclgb",di[J.m.mb]="gclaw",di[J.m.fk]="gclid_len",di[J.m.Ud]="gclgs",di[J.m.Vd]="gcllp",di[J.m.Wd]="gclst",di[J.m.Pb]="auid",di[J.m.Bg]="dscnt",di[J.m.Cg]="fcntr",di[J.m.Dg]="flng",di[J.m.Eg]="mid",di[J.m.hk]="bttype",di[J.m.Qb]="gacid",di[J.m.oc]="label",di[J.m.dd]="capi",di[J.m.Fg]="pscdl",di[J.m.Ra]="currency_code",di[J.m.Sh]="clobs",di[J.m.Ze]="vdltv",di[J.m.Th]="clolo",di[J.m.Uh]="clolb",di[J.m.jk]="_dbg",di[J.m.cf]="oedeld",di[J.m.Tb]="edid",di[J.m.pk]=
"fdr",di[J.m.qk]="fledge",di[J.m.Mg]="gac",di[J.m.ae]="gacgb",di[J.m.zk]="gacmcov",di[J.m.kd]="gdpr",di[J.m.Ub]="gdid",di[J.m.be]="_ng",di[J.m.ff]="gpp_sid",di[J.m.hf]="gpp",di[J.m.Dk]="gsaexp",di[J.m.jf]="_tu",di[J.m.Jc]="frm",di[J.m.Og]="gtm_up",di[J.m.ld]="lps",di[J.m.Pg]="did",di[J.m.ee]="fcntr",di[J.m.fe]="flng",di[J.m.he]="mid",di[J.m.lf]=void 0,di[J.m.Fb]="tiba",di[J.m.Vb]="rdp",di[J.m.Wb]="ecsid",di[J.m.qf]="ga_uid",di[J.m.je]="delopc",di[J.m.od]="gdpr_consent",di[J.m.Ma]="oid",di[J.m.Qk]=
"uptgs",di[J.m.rf]="uaa",di[J.m.tf]="uab",di[J.m.uf]="uafvl",di[J.m.vf]="uamb",di[J.m.wf]="uam",di[J.m.xf]="uap",di[J.m.yf]="uapv",di[J.m.zf]="uaw",di[J.m.mi]="ec_lat",di[J.m.ni]="ec_meta",di[J.m.oi]="ec_m",di[J.m.ri]="ec_sel",di[J.m.si]="ec_s",di[J.m.wc]="ec_mode",di[J.m.Na]="userId",di[J.m.Af]="us_privacy",di[J.m.za]="value",di[J.m.Sk]="mcov",di[J.m.yi]="hn",di[J.m.bl]="gtm_ee",di[J.m.xc]="npa",di[J.m.Ye]=null,di[J.m.Nc]=null,di[J.m.Ab]=null,di[J.m.ra]=null,di[J.m.Ca]=null,di[J.m.Ya]=null,di[J.m.ki]=
null,di[J.m.sd]=null,di[J.m.Le]=null,di[J.m.Me]=null,di[J.m.sc]=null,di);function fi(a,b){if(a){var c=a.split("x");c.length===2&&(gi(b,"u_w",c[0]),gi(b,"u_h",c[1]))}}
function hi(a){var b=ii;b=b===void 0?ji:b;var c;var d=b;if(a&&a.length){for(var e=[],f=0;f<a.length;++f){var g=a[f];g&&e.push({item_id:d(g),quantity:g.quantity,value:g.price,start_date:g.start_date,end_date:g.end_date})}c=e}else c=[];var h;var m=c;if(m){for(var n=[],p=0;p<m.length;p++){var q=m[p],r=[];q&&(r.push(ki(q.value)),r.push(ki(q.quantity)),r.push(ki(q.item_id)),r.push(ki(q.start_date)),r.push(ki(q.end_date)),n.push("("+r.join("*")+")"))}h=n.length>0?n.join(""):""}else h="";return h}
function ji(a){return li(a.item_id,a.id,a.item_name)}function li(){for(var a=l(Ca.apply(0,arguments)),b=a.next();!b.done;b=a.next()){var c=b.value;if(c!==null&&c!==void 0)return c}}function mi(a){if(a&&a.length){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&d.estimated_delivery_date?b.push(""+d.estimated_delivery_date):b.push("")}return b.join(",")}}function gi(a,b,c){c===void 0||c===null||c===""&&!Eg[b]||(a[b]=c)}function ki(a){return typeof a!=="number"&&typeof a!=="string"?"":a.toString()};var ni={},oi=function(){for(var a=!1,b=!1,c=0;a===b;)if(a=qb(0,1)===0,b=qb(0,1)===0,c++,c>30)return;return a},qi={Aq:pi};function ri(a,b){var c=ni[b],d=c.studyId,e=c.experimentId,f=c.probability;if(!(a.studies||{})[d]){var g=a.studies||{};g[d]=!0;a.studies=g;ni[b].active||(ni[b].probability>.5?si(a,e):f<=0||f>1||qi.Aq(a,b))}}
function pi(a,b){var c=ni[b],d=c.controlId2;if(!(qb(0,9999)<c.probability*(c.controlId2&&c.probability<=.25?4:2)*1E4))return a;ti(a,{experimentId:c.experimentId,controlId:c.controlId,controlId2:c.controlId2&&c.probability<=.25?d:void 0,experimentCallback:function(){}});return a}function si(a,b){var c=a.exp||{};c[b]=!0;a.exp=c}
function ti(a,b){var c=b.experimentId,d=b.controlId,e=b.controlId2,f=b.experimentCallback;if((a.exp||{})[c])f();else if(!((a.exp||{})[d]||e&&(a.exp||{})[e])){var g=oi()?0:1;e&&(g|=(oi()?0:1)<<1);g===0?(si(a,c),f()):g===1?si(a,d):g===2&&si(a,e)}};var K={J:{Lj:"call_conversion",W:"conversion",Wn:"floodlight",Cf:"ga_conversion",Fi:"landing_page",Ha:"page_view",na:"remarketing",Va:"user_data_lead",Ja:"user_data_web"}};function wi(a){return xi?z.querySelectorAll(a):null}
function yi(a,b){if(!xi)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!z.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(d!==null&&d.nodeType===1);return null}var zi=!1;
if(z.querySelectorAll)try{var Ai=z.querySelectorAll(":root");Ai&&Ai.length==1&&Ai[0]==z.documentElement&&(zi=!0)}catch(a){}var xi=zi;function Bi(a){switch(a){case 0:break;case 9:return"e4";case 6:return"e5";case 14:return"e6";default:return"e7"}};function Ci(){this.blockSize=-1};function Di(a,b){this.blockSize=-1;this.blockSize=64;this.N=Da.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.P=this.H=0;this.C=[];this.fa=a;this.R=b;this.ma=Da.Int32Array?new Int32Array(64):Array(64);Ei===void 0&&(Da.Int32Array?Ei=new Int32Array(Fi):Ei=Fi);this.reset()}Ea(Di,Ci);for(var Gi=[],Hi=0;Hi<63;Hi++)Gi[Hi]=0;var Ii=[].concat(128,Gi);
Di.prototype.reset=function(){this.P=this.H=0;var a;if(Da.Int32Array)a=new Int32Array(this.R);else{var b=this.R,c=b.length;if(c>0){for(var d=Array(c),e=0;e<c;e++)d[e]=b[e];a=d}else a=[]}this.C=a};
var Ji=function(a){for(var b=a.N,c=a.ma,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=d*4;for(var f=16;f<64;f++){var g=c[f-15]|0,h=c[f-2]|0;c[f]=((c[f-16]|0)+((g>>>7|g<<25)^(g>>>18|g<<14)^g>>>3)|0)+((c[f-7]|0)+((h>>>17|h<<15)^(h>>>19|h<<13)^h>>>10)|0)|0}for(var m=a.C[0]|0,n=a.C[1]|0,p=a.C[2]|0,q=a.C[3]|0,r=a.C[4]|0,t=a.C[5]|0,u=a.C[6]|0,v=a.C[7]|0,w=0;w<64;w++){var y=((m>>>2|m<<30)^(m>>>13|m<<19)^(m>>>22|m<<10))+(m&n^m&p^n&p)|0,A=(v+((r>>>6|r<<26)^(r>>>11|r<<21)^(r>>>25|r<<7))|
0)+(((r&t^~r&u)+(Ei[w]|0)|0)+(c[w]|0)|0)|0;v=u;u=t;t=r;r=q+A|0;q=p;p=n;n=m;m=A+y|0}a.C[0]=a.C[0]+m|0;a.C[1]=a.C[1]+n|0;a.C[2]=a.C[2]+p|0;a.C[3]=a.C[3]+q|0;a.C[4]=a.C[4]+r|0;a.C[5]=a.C[5]+t|0;a.C[6]=a.C[6]+u|0;a.C[7]=a.C[7]+v|0};
Di.prototype.update=function(a,b){b===void 0&&(b=a.length);var c=0,d=this.H;if(typeof a==="string")for(;c<b;)this.N[d++]=a.charCodeAt(c++),d==this.blockSize&&(Ji(this),d=0);else{var e,f=typeof a;e=f!="object"?f:a?Array.isArray(a)?"array":f:"null";if(e=="array"||e=="object"&&typeof a.length=="number")for(;c<b;){var g=a[c++];if(!("number"==typeof g&&0<=g&&255>=g&&g==(g|0)))throw Error("message must be a byte array");this.N[d++]=g;d==this.blockSize&&(Ji(this),d=0)}else throw Error("message must be string or array");
}this.H=d;this.P+=b};Di.prototype.digest=function(){var a=[],b=this.P*8;this.H<56?this.update(Ii,56-this.H):this.update(Ii,this.blockSize-(this.H-56));for(var c=63;c>=56;c--)this.N[c]=b&255,b/=256;Ji(this);for(var d=0,e=0;e<this.fa;e++)for(var f=24;f>=0;f-=8)a[d++]=this.C[e]>>f&255;return a};
var Fi=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,
4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],Ei;function Ki(){Di.call(this,8,Li)}Ea(Ki,Di);var Li=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var Mi=/^[0-9A-Fa-f]{64}$/;function Ni(a){try{return(new TextEncoder).encode(a)}catch(e){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b.push(d):d<2048?b.push(192|d>>6,128|d&63):d<55296||d>=57344?b.push(224|d>>12,128|d>>6&63,128|d&63):(d=65536+((d&1023)<<10|a.charCodeAt(++c)&1023),b.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|d&63))}return new Uint8Array(b)}}
function Oi(a){var b=x;if(a===""||a==="e0")return Promise.resolve(a);var c;if((c=b.crypto)==null?0:c.subtle){if(Mi.test(a))return Promise.resolve(a);try{var d=Ni(a);return b.crypto.subtle.digest("SHA-256",d).then(function(e){return Pi(e,b)}).catch(function(){return"e2"})}catch(e){return Promise.resolve("e2")}}else return Promise.resolve("e1")}
function Pi(a,b){var c=Array.from(new Uint8Array(a)).map(function(d){return String.fromCharCode(d)}).join("");return b.btoa(c).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")};var Qi=[],Ri;function Si(a){Ri?Ri(a):Qi.push(a)}function Ti(a,b){if(!E(190))return b;var c,d=!1;d=d===void 0?!1:d;var e,f;c=((e=data)==null?0:(f=e.blob)==null?0:f.hasOwnProperty(a))?!!data.blob[a]:d;return c!==b?(Si(a),b):c}function Ui(a,b){if(!E(190))return b;var c=Vi(a,"");return c!==b?(Si(a),b):c}function Vi(a,b){b=b===void 0?"":b;var c,d;return((c=data)==null?0:(d=c.blob)==null?0:d.hasOwnProperty(a))?String(data.blob[a]):b}
function Wi(a,b){if(!E(190))return b;var c,d,e;c=((d=data)==null?0:(e=d.blob)==null?0:e.hasOwnProperty(a))?Number(data.blob[a]):0;return c===b||isNaN(c)&&isNaN(b)?c:(Si(a),b)}function Xi(){Ri=Yi;for(var a=l(Qi),b=a.next();!b.done;b=a.next())Ri(b.value);Qi.length=0};var Zi={Om:'',Pm:'',Ym:'512',Zm:'',bn:'1000',ao:'US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD',bo:'US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD',xo:Ui(44,'101509157~103116026~103200004~103233427~103351869~103351871~104684208~104684211~104732253~104732255~104908321~104908323~104964065~104964067~104967141~104967143')},$i={Fo:Number(Zi.Om)||-1,Go:Number(Zi.Pm)||-1,ep:Number(Zi.Ym)||
0,fp:Number(Zi.Zm)||0,jp:Number(Zi.bn)||0,Ap:Zi.ao.split("~"),Bp:Zi.bo.split("~"),Qq:Zi.xo};ma(Object,"assign").call(Object,{},$i);function L(a){fb("GTM",a)};var Lj={},Mj=(Lj[J.m.nb]=1,Lj[J.m.nd]=2,Lj[J.m.vc]=2,Lj[J.m.ya]=3,Lj[J.m.Ze]=4,Lj[J.m.yg]=5,Lj[J.m.Hc]=6,Lj[J.m.eb]=6,Lj[J.m.ob]=6,Lj[J.m.ed]=6,Lj[J.m.Sb]=6,Lj[J.m.zb]=6,Lj[J.m.pb]=7,Lj[J.m.Vb]=9,Lj[J.m.zg]=10,Lj[J.m.Ob]=11,Lj),Nj={},Oj=(Nj.unknown=13,Nj.standard=14,Nj.unique=15,Nj.per_session=16,Nj.transactions=17,Nj.items_sold=18,Nj);var hb=[];function Pj(a,b){b=b===void 0?!1:b;for(var c=Object.keys(a),d=l(Object.keys(Mj)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c.includes(f)){var g=Mj[f],h=b;h=h===void 0?!1:h;fb("GTAG_EVENT_FEATURE_CHANNEL",g);h&&(hb[g]=!0)}}};var Qj=function(){this.C=new Set;this.H=new Set},Sj=function(a){var b=Rj.R;a=a===void 0?[]:a;var c=[].concat(xa(b.C)).concat([].concat(xa(b.H))).concat(a);c.sort(function(d,e){return d-e});return c},Tj=function(){var a=[].concat(xa(Rj.R.C));a.sort(function(b,c){return b-c});return a},Uj=function(){var a=Rj.R,b=$i.Qq;a.C=new Set;if(b!=="")for(var c=l(b.split("~")),d=c.next();!d.done;d=c.next()){var e=Number(d.value);isNaN(e)||a.C.add(e)}};var Vj={},Wj=Ui(14,"57f1"),Xj=Wi(15,Number("0")),Yj=Ui(19,"dataLayer");Ui(20,"");Ui(16,"ChAI8JvdwwYQi7ejypXds4U8EiUATPn+ZC0gHnLmQ5MGsRp+KsjXwY32/v27TkKNSaXwCEEFKTPTGgJKyA\x3d\x3d");var Zj={__cl:1,__ecl:1,__ehl:1,__evl:1,__fal:1,__fil:1,__fsl:1,__hl:1,__jel:1,__lcl:1,__sdl:1,__tl:1,__ytl:1},ak={__paused:1,__tg:1},bk;for(bk in Zj)Zj.hasOwnProperty(bk)&&(ak[bk]=1);var ck=Ti(11,wb("")),dk=!1;
function ek(){var a=!1;return a}var fk=E(218)?Ti(45,ek()):ek(),gk,hk=!1;gk=hk;Vj.wg=Ui(3,"www.googletagmanager.com");var ik=""+Vj.wg+(fk?"/gtag/js":"/gtm.js"),jk=null,kk=null,lk={},mk={};Vj.Sm=Ti(2,wb(""));var nk="";
Vj.Ki=nk;var Rj=new function(){this.R=new Qj;this.C=this.N=!1;this.H=0;this.Da=this.Ua=this.rb=this.P="";this.fa=this.ma=!1};function ok(){var a;a=a===void 0?[]:a;return Sj(a).join("~")}function pk(){var a=Rj.P.length;return Rj.P[a-1]==="/"?Rj.P.substring(0,a-1):Rj.P}function qk(){return Rj.C?E(84)?Rj.H===0:Rj.H!==1:!1}function rk(a){for(var b={},c=l(a.split("|")),d=c.next();!d.done;d=c.next())b[d.value]=!0;return b};var sk=new sb,tk={},uk={},xk={name:Yj,set:function(a,b){od(Hb(a,b),tk);vk()},get:function(a){return wk(a,2)},reset:function(){sk=new sb;tk={};vk()}};function wk(a,b){return b!=2?sk.get(a):yk(a)}function yk(a,b){var c=a.split(".");b=b||[];for(var d=tk,e=0;e<c.length;e++){if(d===null)return!1;if(d===void 0)break;d=d[c[e]];if(b.indexOf(d)!==-1)return}return d}function zk(a,b){uk.hasOwnProperty(a)||(sk.set(a,b),od(Hb(a,b),tk),vk())}
function Ak(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist","gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=wk(c,1);if(Array.isArray(d)||nd(d))d=od(d,null);uk[c]=d}}function vk(a){tb(uk,function(b,c){sk.set(b,c);od(Hb(b),tk);od(Hb(b,c),tk);a&&delete uk[b]})}function Bk(a,b){var c,d=(b===void 0?2:b)!==1?yk(a):sk.get(a);ld(d)==="array"||ld(d)==="object"?c=od(d,null):c=d;return c};var Lk=/:[0-9]+$/,Mk=/^\d+\.fls\.doubleclick\.net$/;function Nk(a,b,c,d){var e=Ok(a,!!d,b),f,g;return c?(g=e[b])!=null?g:[]:(f=e[b])==null?void 0:f[0]}function Ok(a,b,c){for(var d={},e=l(a.split("&")),f=e.next();!f.done;f=e.next()){var g=l(f.value.split("=")),h=g.next().value,m=wa(g),n=decodeURIComponent(h.replace(/\+/g," "));if(c===void 0||n===c){var p=m.join("=");d[n]||(d[n]=[]);d[n].push(b?p:decodeURIComponent(p.replace(/\+/g," ")))}}return d}
function Pk(a){try{return decodeURIComponent(a)}catch(b){}}function Qk(a,b,c,d,e){b&&(b=String(b).toLowerCase());if(b==="protocol"||b==="port")a.protocol=Rk(a.protocol)||Rk(x.location.protocol);b==="port"?a.port=String(Number(a.hostname?a.port:x.location.port)||(a.protocol==="http"?80:a.protocol==="https"?443:"")):b==="host"&&(a.hostname=(a.hostname||x.location.hostname).replace(Lk,"").toLowerCase());return Sk(a,b,c,d,e)}
function Sk(a,b,c,d,e){var f,g=Rk(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=Tk(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(Lk,"").toLowerCase();if(c){var h=/^www\d*\./.exec(f);h&&h[0]&&(f=f.substring(h[0].length))}break;case "port":f=String(Number(a.port)||(g==="http"?80:g==="https"?443:""));break;case "path":a.pathname||a.hostname||fb("TAGGING",1);f=a.pathname.substring(0,1)==="/"?a.pathname:"/"+a.pathname;var m=f.split("/");(d||[]).indexOf(m[m.length-
1])>=0&&(m[m.length-1]="");f=m.join("/");break;case "query":f=a.search.replace("?","");e&&(f=Nk(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=n.length>1?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f}function Rk(a){return a?a.replace(":","").toLowerCase():""}function Tk(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=c<0?a.href:a.href.substring(0,c)}return b}var Uk={},Vk=0;
function Wk(a){var b=Uk[a];if(!b){var c=z.createElement("a");a&&(c.href=a);var d=c.pathname;d[0]!=="/"&&(a||fb("TAGGING",1),d="/"+d);var e=c.hostname.replace(Lk,"");b={href:c.href,protocol:c.protocol,host:c.host,hostname:e,pathname:d,search:c.search,hash:c.hash,port:c.port};Vk<5&&(Uk[a]=b,Vk++)}return b}function Xk(a,b,c){var d=Wk(a);return Nb(b,d,c)}
function Yk(a){var b=Wk(x.location.href),c=Qk(b,"host",!1);if(c&&c.match(Mk)){var d=Qk(b,"path");if(d){var e=d.split(a+"=");if(e.length>1)return e[1].split(";")[0].split("?")[0]}}};var Zk={"https://www.google.com":"/g","https://www.googleadservices.com":"/as","https://pagead2.googlesyndication.com":"/gs"},$k=["/as/d/ccm/conversion","/g/d/ccm/conversion","/gs/ccm/conversion","/d/ccm/form-data"];function al(a,b){if(a){var c=""+a;c.indexOf("http://")!==0&&c.indexOf("https://")!==0&&(c="https://"+c);c[c.length-1]==="/"&&(c=c.substring(0,c.length-1));return Wk(""+c+b).href}}function bl(a,b){if(qk()||Rj.N)return al(a,b)}
function cl(){return!!Vj.Ki&&Vj.Ki.split("@@").join("")!=="SGTM_TOKEN"}function dl(a){for(var b=l([J.m.nd,J.m.vc]),c=b.next();!c.done;c=b.next()){var d=N(a,c.value);if(d)return d}}function el(a,b,c){c=c===void 0?"":c;if(!qk())return a;var d=b?Zk[a]||"":"";d==="/gs"&&(c="");return""+pk()+d+c}function fl(a){if(!qk())return a;for(var b=l($k),c=b.next();!c.done;c=b.next())if(Fb(a,""+pk()+c.value))return a+"&_uip="+encodeURIComponent("::");return a};function gl(a){var b=String(a[jf.Ta]||"").replace(/_/g,"");return Fb(b,"cvt")?"cvt":b}var hl=x.location.search.indexOf("?gtm_latency=")>=0||x.location.search.indexOf("&gtm_latency=")>=0;var il={wq:Wi(27,Number("0.005000")),bp:Wi(42,Number("0.010000"))},jl=Math.random(),kl=hl||jl<Number(il.wq),ll=hl||jl>=1-Number(il.bp);var ml=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},nl=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};var ol,pl;a:{for(var ql=["CLOSURE_FLAGS"],rl=Da,sl=0;sl<ql.length;sl++)if(rl=rl[ql[sl]],rl==null){pl=null;break a}pl=rl}var tl=pl&&pl[610401301];ol=tl!=null?tl:!1;function ul(){var a=Da.navigator;if(a){var b=a.userAgent;if(b)return b}return""}var vl,wl=Da.navigator;vl=wl?wl.userAgentData||null:null;function xl(a){if(!ol||!vl)return!1;for(var b=0;b<vl.brands.length;b++){var c=vl.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function yl(a){return ul().indexOf(a)!=-1};function zl(){return ol?!!vl&&vl.brands.length>0:!1}function Al(){return zl()?!1:yl("Opera")}function Bl(){return yl("Firefox")||yl("FxiOS")}function Cl(){return zl()?xl("Chromium"):(yl("Chrome")||yl("CriOS"))&&!(zl()?0:yl("Edge"))||yl("Silk")};var Dl=function(a){Dl[" "](a);return a};Dl[" "]=function(){};var El=function(a){return decodeURIComponent(a.replace(/\+/g," "))};function Fl(){return ol?!!vl&&!!vl.platform:!1}function Gl(){return yl("iPhone")&&!yl("iPod")&&!yl("iPad")}function Hl(){Gl()||yl("iPad")||yl("iPod")};Al();zl()||yl("Trident")||yl("MSIE");yl("Edge");!yl("Gecko")||ul().toLowerCase().indexOf("webkit")!=-1&&!yl("Edge")||yl("Trident")||yl("MSIE")||yl("Edge");ul().toLowerCase().indexOf("webkit")!=-1&&!yl("Edge")&&yl("Mobile");Fl()||yl("Macintosh");Fl()||yl("Windows");(Fl()?vl.platform==="Linux":yl("Linux"))||Fl()||yl("CrOS");Fl()||yl("Android");Gl();yl("iPad");yl("iPod");Hl();ul().toLowerCase().indexOf("kaios");var Il=function(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{Dl(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}},Jl=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},Kl=function(a,b){for(var c=a,d=0;d<50;++d){var e;try{e=!(!c.frames||!c.frames[b])}catch(h){e=!1}if(e)return c;var f;a:{try{var g=c.parent;if(g&&g!=c){f=g;break a}}catch(h){}f=null}if(!(c=f))break}return null},Ll=function(a){var b=x;if(b.top==b)return 0;if(a===void 0?0:a){var c=
b.location.ancestorOrigins;if(c)return c[c.length-1]==b.location.origin?1:2}return Il(b.top)?1:2},Ml=function(a){a=a===void 0?document:a;return a.createElement("img")},Nl=function(){for(var a=x,b=a;a&&a!=a.parent;)a=a.parent,Il(a)&&(b=a);return b};function Ol(a){var b;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!c.allowedFeatures().includes(a))};function Pl(){return Ol("join-ad-interest-group")&&lb(vc.joinAdInterestGroup)}
function Ql(a,b,c){var d=Qa[3]===void 0?1:Qa[3],e='iframe[data-tagging-id="'+b+'"]',f=[];try{if(d===1){var g=z.querySelector(e);g&&(f=[g])}else f=Array.from(z.querySelectorAll(e))}catch(r){}var h;a:{try{h=z.querySelectorAll('iframe[allow="join-ad-interest-group"][data-tagging-id*="-"]');break a}catch(r){}h=void 0}var m=h,n=((m==null?void 0:m.length)||0)>=(Qa[2]===void 0?50:Qa[2]),p;if(p=f.length>=1){var q=Number(f[f.length-1].dataset.loadTime);q!==void 0&&Ab()-q<(Qa[1]===void 0?6E4:Qa[1])?(fb("TAGGING",
9),p=!0):p=!1}if(p)return!1;if(d===1)if(f.length>=1)Rl(f[0]);else{if(n)return fb("TAGGING",10),!1}else f.length>=d?Rl(f[0]):n&&Rl(m[0]);Jc(a,c,{allow:"join-ad-interest-group"},{taggingId:b,loadTime:Ab()});return!0}function Rl(a){try{a.parentNode.removeChild(a)}catch(b){}};function Sl(a,b,c){var d,e=a.GooglebQhCsO;e||(e={},a.GooglebQhCsO=e);d=e;if(d[b])return!1;d[b]=[];d[b][0]=c;return!0};var Tl=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};Bl();Gl()||yl("iPod");yl("iPad");!yl("Android")||Cl()||Bl()||Al()||yl("Silk");Cl();!yl("Safari")||Cl()||(zl()?0:yl("Coast"))||Al()||(zl()?0:yl("Edge"))||(zl()?xl("Microsoft Edge"):yl("Edg/"))||(zl()?xl("Opera"):yl("OPR"))||Bl()||yl("Silk")||yl("Android")||Hl();var Ul={},Vl=null,Wl=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;f===void 0&&(f=0);if(!Vl){Vl={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=["+/=","+/","-_=","-_.","-_"],m=0;m<5;m++){var n=g.concat(h[m].split(""));Ul[m]=n;for(var p=0;p<n.length;p++){var q=n[p];Vl[q]===void 0&&(Vl[q]=p)}}}for(var r=Ul[f],t=Array(Math.floor(b.length/3)),u=r[64]||"",v=0,w=0;v<b.length-2;v+=3){var y=b[v],
A=b[v+1],C=b[v+2],D=r[y>>2],G=r[(y&3)<<4|A>>4],I=r[(A&15)<<2|C>>6],M=r[C&63];t[w++]=""+D+G+I+M}var T=0,da=u;switch(b.length-v){case 2:T=b[v+1],da=r[(T&15)<<2]||u;case 1:var O=b[v];t[w]=""+r[O>>2]+r[(O&3)<<4|T>>4]+da+u}return t.join("")};var Xl=function(a,b,c,d){for(var e=b,f=c.length;(e=a.indexOf(c,e))>=0&&e<d;){var g=a.charCodeAt(e-1);if(g==38||g==63){var h=a.charCodeAt(e+f);if(!h||h==61||h==38||h==35)return e}e+=f+1}return-1},Yl=/#|$/,Zl=function(a,b){var c=a.search(Yl),d=Xl(a,0,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return El(a.slice(d,e!==-1?e:0))},$l=/[?&]($|#)/,am=function(a,b,c){for(var d,e=a.search(Yl),f=0,g,h=[];(g=Xl(a,f,b,e))>=0;)h.push(a.substring(f,g)),f=Math.min(a.indexOf("&",g)+
1||e,e);h.push(a.slice(f));d=h.join("").replace($l,"$1");var m,n=c!=null?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");r<0&&(r=d.length);var t=d.indexOf("?"),u;t<0||t>r?(t=r,u=""):u=d.substring(t+1,r);q=[d.slice(0,t),u,d.slice(r)];var v=q[1];q[1]=p?v?v+"&"+p:p:v;m=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else m=d;return m};function bm(a,b,c,d,e,f,g){var h=Zl(c,"fmt");if(d){var m=Zl(c,"random"),n=Zl(c,"label")||"";if(!m)return!1;var p=Wl(El(n)+":"+El(m));if(!Sl(a,p,d))return!1}h&&Number(h)!==4&&(c=am(c,"rfmt",h));var q=am(c,"fmt",4),r=b.getElementsByTagName("script")[0].parentElement;g==null||cm(g);Hc(q,function(){g==null||dm(g);a.google_noFurtherRedirects&&d&&(a.google_noFurtherRedirects=null,d())},function(){g==null||dm(g);e==null||e()},f,r||void 0);return!0};var em={},fm=(em[1]={},em[2]={},em[3]={},em[4]={},em);function gm(a,b,c){var d=hm(b,c);if(d){var e=fm[b][d];e||(e=fm[b][d]=[]);e.push(ma(Object,"assign").call(Object,{},a))}}function im(a,b){var c=hm(a,b);if(c){var d=fm[a][c];d&&(fm[a][c]=d.filter(function(e){return!e.Am}))}}function jm(a){switch(a){case "script-src":case "script-src-elem":return 1;case "frame-src":return 4;case "connect-src":return 2;case "img-src":return 3}}
function hm(a,b){var c=b;if(b[0]==="/"){var d;c=((d=x.location)==null?void 0:d.origin)+b}try{var e=new URL(c);return a===4?e.origin:e.origin+e.pathname}catch(f){}}function km(a){var b=Ca.apply(1,arguments);ll&&(gm(a,2,b[0]),gm(a,3,b[0]));Tc.apply(null,xa(b))}function lm(a){var b=Ca.apply(1,arguments);ll&&gm(a,2,b[0]);return Vc.apply(null,xa(b))}function mm(a){var b=Ca.apply(1,arguments);ll&&gm(a,3,b[0]);Kc.apply(null,xa(b))}
function nm(a){var b=Ca.apply(1,arguments),c=b[0];ll&&(gm(a,2,c),gm(a,3,c));return Xc.apply(null,xa(b))}function om(a){var b=Ca.apply(1,arguments);ll&&gm(a,1,b[0]);Hc.apply(null,xa(b))}function pm(a){var b=Ca.apply(1,arguments);b[0]&&ll&&gm(a,4,b[0]);Jc.apply(null,xa(b))}function qm(a){var b=Ca.apply(1,arguments);ll&&gm(a,1,b[2]);return bm.apply(null,xa(b))}function rm(a){var b=Ca.apply(1,arguments);ll&&gm(a,4,b[0]);Ql.apply(null,xa(b))};var sm=/gtag[.\/]js/,tm=/gtm[.\/]js/,um=!1;function vm(a){if(um)return"1";var b,c=(b=a.scriptElement)==null?void 0:b.src;if(c){if(sm.test(c))return"3";if(tm.test(c))return"2"}return"0"};function wm(a,b,c){var d=xm(),e=ym().container[a];e&&e.state!==3||(ym().container[a]={state:1,context:b,parent:d},zm({ctid:a,isDestination:!1},c))}function zm(a,b){var c=ym();c.pending||(c.pending=[]);pb(c.pending,function(d){return d.target.ctid===a.ctid&&d.target.isDestination===a.isDestination})||c.pending.push({target:a,onLoad:b})}function Am(){var a=x.google_tags_first_party;Array.isArray(a)||(a=[]);for(var b={},c=l(a),d=c.next();!d.done;d=c.next())b[d.value]=!0;return Object.freeze(b)}
var Bm=function(){this.container={};this.destination={};this.canonical={};this.pending=[];this.injectedFirstPartyContainers={};this.injectedFirstPartyContainers=Am()};function ym(){var a=zc("google_tag_data",{}),b=a.tidr;b&&typeof b==="object"||(b=new Bm,a.tidr=b);var c=b;c.container||(c.container={});c.destination||(c.destination={});c.canonical||(c.canonical={});c.pending||(c.pending=[]);c.injectedFirstPartyContainers||(c.injectedFirstPartyContainers=Am());return c};var Cm={},mg={ctid:Ui(5,"GTM-MT96V2P9"),canonicalContainerId:Ui(6,"192979098"),sm:Ui(10,"GTM-MT96V2P9"),tm:Ui(9,"GTM-MT96V2P9")};Cm.qe=Ti(7,wb(""));function Dm(){return Cm.qe&&Em().some(function(a){return a===mg.ctid})}function Fm(){return mg.canonicalContainerId||"_"+mg.ctid}function Gm(){return mg.sm?mg.sm.split("|"):[mg.ctid]}
function Em(){return mg.tm?mg.tm.split("|").filter(function(a){return a.indexOf("GTM-")!==0}):[]}function Hm(){var a=Im(xm()),b=a&&a.parent;if(b)return Im(b)}function Jm(){var a=Im(xm());if(a){for(;a.parent;){var b=Im(a.parent);if(!b)break;a=b}return a}}function Im(a){var b=ym();return a.isDestination?b.destination[a.ctid]:b.container[a.ctid]}
function Km(){var a=ym();if(a.pending){for(var b,c=[],d=!1,e=Gm(),f=Em(),g={},h=0;h<a.pending.length;g={kg:void 0},h++)g.kg=a.pending[h],pb(g.kg.target.isDestination?f:e,function(m){return function(n){return n===m.kg.target.ctid}}(g))?d||(b=g.kg.onLoad,d=!0):c.push(g.kg);a.pending=c;if(b)try{b(Fm())}catch(m){}}}
function Lm(){for(var a=mg.ctid,b=Gm(),c=Em(),d=function(n,p){var q={canonicalContainerId:mg.canonicalContainerId,scriptContainerId:a,state:2,containers:b.slice(),destinations:c.slice()};xc&&(q.scriptElement=xc);yc&&(q.scriptSource=yc);if(Hm()===void 0){var r;a:{if((q.scriptContainerId||"").indexOf("GTM-")>=0){var t;b:{var u,v=(u=q.scriptElement)==null?void 0:u.src;if(v){for(var w=Rj.C,y=Wk(v),A=w?y.pathname:""+y.hostname+y.pathname,C=z.scripts,D="",G=0;G<C.length;++G){var I=C[G];if(!(I.innerHTML.length===
0||!w&&I.innerHTML.indexOf(q.scriptContainerId||"SHOULD_NOT_BE_SET")<0||I.innerHTML.indexOf(A)<0)){if(I.innerHTML.indexOf("(function(w,d,s,l,i)")>=0){t=String(G);break b}D=String(G)}}if(D){t=D;break b}}t=void 0}var M=t;if(M){um=!0;r=M;break a}}var T=[].slice.call(z.scripts);r=q.scriptElement?String(T.indexOf(q.scriptElement)):"-1"}q.htmlLoadOrder=r;q.loadScriptType=vm(q)}var da=p?e.destination:e.container,O=da[n];O?(p&&O.state===0&&L(93),ma(Object,"assign").call(Object,O,q)):da[n]=q},e=ym(),f=l(b),
g=f.next();!g.done;g=f.next())d(g.value,!1);for(var h=l(c),m=h.next();!m.done;m=h.next())d(m.value,!0);e.canonical[Fm()]={};Km()}function Mm(){var a=Fm();return!!ym().canonical[a]}function Nm(a){return!!ym().container[a]}function Om(a){var b=ym().destination[a];return!!b&&!!b.state}function xm(){return{ctid:mg.ctid,isDestination:Cm.qe}}function Pm(){var a=ym().container,b;for(b in a)if(a.hasOwnProperty(b)&&a[b].state===1)return!0;return!1}
function Qm(){var a={};tb(ym().destination,function(b,c){c.state===0&&(a[b]=c)});return a}function Rm(a){return!!(a&&a.parent&&a.context&&a.context.source===1&&a.parent.ctid.indexOf("GTM-")!==0)}function Sm(){for(var a=ym(),b=l(Gm()),c=b.next();!c.done;c=b.next())if(a.injectedFirstPartyContainers[c.value])return!0;return!1};var Tm={Ia:{me:0,pe:1,Gi:2}};Tm.Ia[Tm.Ia.me]="FULL_TRANSMISSION";Tm.Ia[Tm.Ia.pe]="LIMITED_TRANSMISSION";Tm.Ia[Tm.Ia.Gi]="NO_TRANSMISSION";var Um={X:{Hb:0,Fa:1,Fc:2,Oc:3}};Um.X[Um.X.Hb]="NO_QUEUE";Um.X[Um.X.Fa]="ADS";Um.X[Um.X.Fc]="ANALYTICS";Um.X[Um.X.Oc]="MONITORING";function Vm(){var a=zc("google_tag_data",{});return a.ics=a.ics||new Wm}var Wm=function(){this.entries={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.C=[]};
Wm.prototype.default=function(a,b,c,d,e,f,g){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;fb("TAGGING",19);b==null?fb("TAGGING",18):Xm(this,a,b==="granted",c,d,e,f,g)};Wm.prototype.waitForUpdate=function(a,b,c){for(var d=0;d<a.length;d++)Xm(this,a[d],void 0,void 0,"","",b,c)};
var Xm=function(a,b,c,d,e,f,g,h){var m=a.entries,n=m[b]||{},p=n.region,q=d&&mb(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(e===""||q===f||(q===e?p!==f:!q&&!p)){var r=!!(g&&g>0&&n.update===void 0),t={region:q,declare_region:n.declare_region,implicit:n.implicit,default:c!==void 0?c:n.default,declare:n.declare,update:n.update,quiet:r};if(e!==""||n.default!==!1)m[b]=t;r&&x.setTimeout(function(){m[b]===t&&t.quiet&&(fb("TAGGING",2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0,h),
a.notifyListeners())},g)}};k=Wm.prototype;k.clearTimeout=function(a,b,c){var d=[a],e=c.delegatedConsentTypes,f;for(f in e)e.hasOwnProperty(f)&&e[f]===a&&d.push(f);var g=this.entries[a]||{},h=this.getConsentState(a,c);if(g.quiet){g.quiet=!1;for(var m=l(d),n=m.next();!n.done;n=m.next())Ym(this,n.value)}else if(b!==void 0&&h!==b)for(var p=l(d),q=p.next();!q.done;q=p.next())Ym(this,q.value)};
k.update=function(a,b,c){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(b!=null){var d=this.getConsentState(a,c),e=this.entries;(e[a]=e[a]||{}).update=b==="granted";this.clearTimeout(a,d,c)}};
k.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},h=g.declare_region,m=c&&mb(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(d===""||m===e||(m===d?h!==e:!m&&!h)){var n={region:g.region,declare_region:m,declare:b==="granted",implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet};if(d!==""||g.declare!==!1)f[a]=n}};
k.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries,d=c[a]=c[a]||{};d.implicit!==!1&&(d.implicit=b==="granted")};
k.getConsentState=function(a,b){var c=this.entries,d=c[a]||{},e=d.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var f=b.containerScopedDefaults[a];if(f===3)return 1;if(f===2)return 2}else if(e=d.default,e!==void 0)return e?1:2;if(b==null?0:b.delegatedConsentTypes.hasOwnProperty(a)){var g=b.delegatedConsentTypes[a],h=c[g]||{};e=h.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var m=b.containerScopedDefaults[g];if(m===3)return 1;if(m===2)return 2}else if(e=
h.default,e!==void 0)return e?1:2}e=d.declare;if(e!==void 0)return e?1:2;e=d.implicit;return e!==void 0?e?3:4:0};k.addListener=function(a,b){this.C.push({consentTypes:a,Sc:b})};var Ym=function(a,b){for(var c=0;c<a.C.length;++c){var d=a.C[c];Array.isArray(d.consentTypes)&&d.consentTypes.indexOf(b)!==-1&&(d.vm=!0)}};Wm.prototype.notifyListeners=function(a,b){for(var c=0;c<this.C.length;++c){var d=this.C[c];if(d.vm){d.vm=!1;try{d.Sc({consentEventId:a,consentPriorityId:b})}catch(e){}}}};var Zm=!1,$m=!1,an={},bn={delegatedConsentTypes:{},corePlatformServices:{},usedCorePlatformServices:!1,selectedAllCorePlatformServices:!1,containerScopedDefaults:(an.ad_storage=1,an.analytics_storage=1,an.ad_user_data=1,an.ad_personalization=1,an),usedContainerScopedDefaults:!1};function cn(a){var b=Vm();b.accessedAny=!0;return(mb(a)?[a]:a).every(function(c){switch(b.getConsentState(c,bn)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})}
function dn(a){var b=Vm();b.accessedAny=!0;return b.getConsentState(a,bn)}function en(a){var b=Vm();b.accessedAny=!0;return!(b.entries[a]||{}).quiet}function fn(){if(!Ra(7))return!1;var a=Vm();a.accessedAny=!0;if(a.active)return!0;if(!bn.usedContainerScopedDefaults)return!1;for(var b=l(Object.keys(bn.containerScopedDefaults)),c=b.next();!c.done;c=b.next())if(bn.containerScopedDefaults[c.value]!==1)return!0;return!1}function gn(a,b){Vm().addListener(a,b)}
function hn(a,b){Vm().notifyListeners(a,b)}function jn(a,b){function c(){for(var e=0;e<b.length;e++)if(!en(b[e]))return!0;return!1}if(c()){var d=!1;gn(b,function(e){d||c()||(d=!0,a(e))})}else a({})}
function kn(a,b){function c(){for(var h=[],m=0;m<e.length;m++){var n=e[m];cn(n)&&!f[n]&&h.push(n)}return h}function d(h){for(var m=0;m<h.length;m++)f[h[m]]=!0}var e=mb(b)?[b]:b,f={},g=c();g.length!==e.length&&(d(g),gn(e,function(h){function m(q){q.length!==0&&(d(q),h.consentTypes=q,a(h))}var n=c();if(n.length!==0){var p=Object.keys(f).length;n.length+p>=e.length?m(n):x.setTimeout(function(){m(c())},500)}}))};var ln={},mn=(ln[Um.X.Hb]=Tm.Ia.me,ln[Um.X.Fa]=Tm.Ia.me,ln[Um.X.Fc]=Tm.Ia.me,ln[Um.X.Oc]=Tm.Ia.me,ln),nn=function(a,b){this.C=a;this.consentTypes=b};nn.prototype.isConsentGranted=function(){switch(this.C){case 0:return this.consentTypes.every(function(a){return cn(a)});case 1:return this.consentTypes.some(function(a){return cn(a)});default:nc(this.C,"consentsRequired had an unknown type")}};
var on={},pn=(on[Um.X.Hb]=new nn(0,[]),on[Um.X.Fa]=new nn(0,["ad_storage"]),on[Um.X.Fc]=new nn(0,["analytics_storage"]),on[Um.X.Oc]=new nn(1,["ad_storage","analytics_storage"]),on);var rn=function(a){var b=this;this.type=a;this.C=[];gn(pn[a].consentTypes,function(){qn(b)||b.flush()})};rn.prototype.flush=function(){for(var a=l(this.C),b=a.next();!b.done;b=a.next()){var c=b.value;c()}this.C=[]};var qn=function(a){return mn[a.type]===Tm.Ia.Gi&&!pn[a.type].isConsentGranted()},sn=function(a,b){qn(a)?a.C.push(b):b()},tn=new Map;function un(a){tn.has(a)||tn.set(a,new rn(a));return tn.get(a)};var vn={Z:{Nm:"aw_user_data_cache",Lh:"cookie_deprecation_label",xg:"diagnostics_page_id",Xn:"fl_user_data_cache",Zn:"ga4_user_data_cache",Df:"ip_geo_data_cache",Bi:"ip_geo_fetch_in_progress",pl:"nb_data",rl:"page_experiment_ids",Nf:"pt_data",sl:"pt_listener_set",Al:"service_worker_endpoint",Cl:"shared_user_id",Dl:"shared_user_id_requested",jh:"shared_user_id_source"}};var wn=function(a){return bf(function(b){for(var c in a)if(b===a[c]&&!/^[0-9]+$/.test(c))return!0;return!1})}(vn.Z);
function xn(a,b){b=b===void 0?!1:b;if(wn(a)){var c,d,e=(d=(c=zc("google_tag_data",{})).xcd)!=null?d:c.xcd={};if(e[a])return e[a];if(b){var f=void 0,g=1,h={},m={set:function(n){f=n;m.notify()},get:function(){return f},subscribe:function(n){h[String(g)]=n;return g++},unsubscribe:function(n){var p=String(n);return h.hasOwnProperty(p)?(delete h[p],!0):!1},notify:function(){for(var n=l(Object.keys(h)),p=n.next();!p.done;p=n.next()){var q=p.value;try{h[q](a,f)}catch(r){}}}};return e[a]=m}}}
function yn(a,b){var c=xn(a,!0);c&&c.set(b)}function zn(a){var b;return(b=xn(a))==null?void 0:b.get()}function An(a){var b={},c=xn(a);if(!c){c=xn(a,!0);if(!c)return;c.set(b)}return c.get()}function Bn(a,b){if(typeof b==="function"){var c;return(c=xn(a,!0))==null?void 0:c.subscribe(b)}}function Cn(a,b){var c=xn(a);return c?c.unsubscribe(b):!1};var Dn="https://"+Ui(21,"www.googletagmanager.com"),En="/td?id="+mg.ctid,Fn={},Gn=(Fn.tdp=1,Fn.exp=1,Fn.pid=1,Fn.dl=1,Fn.seq=1,Fn.t=1,Fn.v=1,Fn),Hn=["mcc"],In={},Jn={},Kn=!1,Ln=void 0;function Mn(a,b,c){Jn[a]=b;(c===void 0||c)&&Nn(a)}function Nn(a,b){In[a]!==void 0&&(b===void 0||!b)||Fb(mg.ctid,"GTM-")&&a==="mcc"||(In[a]=!0)}
function On(a){a=a===void 0?!1:a;var b=Object.keys(In).filter(function(c){return In[c]===!0&&Jn[c]!==void 0&&(a||!Hn.includes(c))}).map(function(c){var d=Jn[c];typeof d==="function"&&(d=d());return d?"&"+c+"="+d:""}).join("");return""+el(Dn)+En+(""+b+"&z=0")}function Pn(){Object.keys(In).forEach(function(a){Gn[a]||(In[a]=!1)})}
function Qn(a){a=a===void 0?!1:a;if(Rj.fa&&ll&&mg.ctid){var b=un(Um.X.Oc);if(qn(b))Kn||(Kn=!0,sn(b,Qn));else{var c=On(a),d={destinationId:mg.ctid,endpoint:61};a?nm(d,c,void 0,{Ch:!0},void 0,function(){mm(d,c+"&img=1")}):mm(d,c);Pn();Kn=!1}}}var Rn={};
function Sn(a){var b=String(a);Rn.hasOwnProperty(b)||(Rn[b]=!0,Mn("csp",Object.keys(Rn).join("~")),Nn("csp",!0),Ln===void 0&&E(171)&&(Ln=x.setTimeout(function(){var c=In.csp;In.csp=!0;In.seq=!1;var d=On(!1);In.csp=c;In.seq=!0;Hc(d+"&script=1");Ln=void 0},500)))}function Tn(){Object.keys(In).filter(function(a){return In[a]&&!Gn[a]}).length>0&&Qn(!0)}var Un;
function Vn(){if(zn(vn.Z.xg)===void 0){var a=function(){yn(vn.Z.xg,qb());Un=0};a();x.setInterval(a,864E5)}else Bn(vn.Z.xg,function(){Un=0});Un=0}function Wn(){Vn();Mn("v","3");Mn("t","t");Mn("pid",function(){return String(zn(vn.Z.xg))});Mn("seq",function(){return String(++Un)});Mn("exp",ok());Mc(x,"pagehide",Tn)};var Xn=["ad_storage","analytics_storage","ad_user_data","ad_personalization"],Yn=[J.m.nd,J.m.vc,J.m.Zd,J.m.Qb,J.m.Wb,J.m.Na,J.m.Sa,J.m.eb,J.m.ob,J.m.Sb],Zn=!1,$n=!1,ao={},bo={};function co(){!$n&&Zn&&(Xn.some(function(a){return bn.containerScopedDefaults[a]!==1})||eo("mbc"));$n=!0}function eo(a){ll&&(Mn(a,"1"),Qn())}function fo(a,b){if(!ao[b]&&(ao[b]=!0,bo[b]))for(var c=l(Yn),d=c.next();!d.done;d=c.next())if(N(a,d.value)){eo("erc");break}};function go(a){fb("HEALTH",a)};var ho={vp:Ui(22,"eyIwIjoiVFIiLCIxIjoiVFItMDYiLCIyIjpmYWxzZSwiMyI6Imdvb2dsZS5jb20udHIiLCI0IjoiIiwiNSI6dHJ1ZSwiNiI6ZmFsc2UsIjciOiJhZF9zdG9yYWdlfGFuYWx5dGljc19zdG9yYWdlfGFkX3VzZXJfZGF0YXxhZF9wZXJzb25hbGl6YXRpb24ifQ")},io={},jo=!1;function ko(){function a(){c!==void 0&&Cn(vn.Z.Df,c);try{var e=zn(vn.Z.Df);io=JSON.parse(e)}catch(f){L(123),go(2),io={}}jo=!0;b()}var b=lo,c=void 0,d=zn(vn.Z.Df);d?a(d):(c=Bn(vn.Z.Df,a),mo())}
function mo(){function a(c){yn(vn.Z.Df,c||"{}");yn(vn.Z.Bi,!1)}if(!zn(vn.Z.Bi)){yn(vn.Z.Bi,!0);var b="";try{x.fetch(b,{method:"GET",cache:"no-store",mode:"cors",credentials:"omit"}).then(function(c){c.ok?c.text().then(function(d){a(d)},function(){a()}):a()},function(){a()})}catch(c){a()}}}
function no(){var a=ho.vp;try{return JSON.parse(db(a))}catch(b){return L(123),go(2),{}}}function oo(){return io["0"]||""}function po(){return io["1"]||""}function qo(){var a=!1;return a}function ro(){return io["6"]!==!1}function so(){var a="";return a}
function to(){var a=!1;return a}function uo(){var a="";return a};var vo={},wo=Object.freeze((vo[J.m.Ga]=1,vo[J.m.zg]=1,vo[J.m.Ag]=1,vo[J.m.Ob]=1,vo[J.m.ra]=1,vo[J.m.ob]=1,vo[J.m.pb]=1,vo[J.m.zb]=1,vo[J.m.ed]=1,vo[J.m.Sb]=1,vo[J.m.eb]=1,vo[J.m.Hc]=1,vo[J.m.af]=1,vo[J.m.oa]=1,vo[J.m.kk]=1,vo[J.m.df]=1,vo[J.m.Jg]=1,vo[J.m.Kg]=1,vo[J.m.Zd]=1,vo[J.m.Ak]=1,vo[J.m.sc]=1,vo[J.m.ce]=1,vo[J.m.Ck]=1,vo[J.m.Ng]=1,vo[J.m.fi]=1,vo[J.m.Kc]=1,vo[J.m.Lc]=1,vo[J.m.Sa]=1,vo[J.m.gi]=1,vo[J.m.Vb]=1,vo[J.m.qb]=1,vo[J.m.md]=1,vo[J.m.nd]=1,vo[J.m.pf]=1,vo[J.m.ii]=1,vo[J.m.je]=1,vo[J.m.vc]=
1,vo[J.m.pd]=1,vo[J.m.Ug]=1,vo[J.m.Xb]=1,vo[J.m.sd]=1,vo[J.m.Ji]=1,vo));Object.freeze([J.m.Ca,J.m.Ya,J.m.Fb,J.m.Ab,J.m.hi,J.m.Na,J.m.bi,J.m.An]);
var xo={},yo=Object.freeze((xo[J.m.dn]=1,xo[J.m.fn]=1,xo[J.m.gn]=1,xo[J.m.hn]=1,xo[J.m.jn]=1,xo[J.m.mn]=1,xo[J.m.nn]=1,xo[J.m.on]=1,xo[J.m.qn]=1,xo[J.m.Td]=1,xo)),zo={},Ao=Object.freeze((zo[J.m.Zj]=1,zo[J.m.bk]=1,zo[J.m.Pd]=1,zo[J.m.Qd]=1,zo[J.m.dk]=1,zo[J.m.Xc]=1,zo[J.m.Rd]=1,zo[J.m.kc]=1,zo[J.m.Gc]=1,zo[J.m.mc]=1,zo[J.m.lb]=1,zo[J.m.Sd]=1,zo[J.m.yb]=1,zo[J.m.ek]=1,zo)),Bo=Object.freeze([J.m.Ga,J.m.Qe,J.m.Ob,J.m.Hc,J.m.Zd,J.m.kf,J.m.qb,J.m.pd]),Co=Object.freeze([].concat(xa(Bo))),Do=Object.freeze([J.m.pb,
J.m.Kg,J.m.pf,J.m.ii,J.m.Hg]),Eo=Object.freeze([].concat(xa(Do))),Fo={},Go=(Fo[J.m.U]="1",Fo[J.m.ja]="2",Fo[J.m.V]="3",Fo[J.m.La]="4",Fo),Ho={},Io=Object.freeze((Ho.search="s",Ho.youtube="y",Ho.playstore="p",Ho.shopping="h",Ho.ads="a",Ho.maps="m",Ho));function Jo(a){return typeof a!=="object"||a===null?{}:a}function Ko(a){return a===void 0||a===null?"":typeof a==="object"?a.toString():String(a)}function Lo(a){if(a!==void 0&&a!==null)return Ko(a)}function Mo(a){return typeof a==="number"?a:Lo(a)};function No(a){return a&&a.indexOf("pending:")===0?Oo(a.substr(8)):!1}function Oo(a){if(a==null||a.length===0)return!1;var b=Number(a),c=Ab();return b<c+3E5&&b>c-9E5};var Po=!1,Qo=!1,Ro=!1,So=0,To=!1,Uo=[];function Vo(a){if(So===0)To&&Uo&&(Uo.length>=100&&Uo.shift(),Uo.push(a));else if(Wo()){var b=Ui(41,'google.tagmanager.ta.prodqueue'),c=zc(b,[]);c.length>=50&&c.shift();c.push(a)}}function Xo(){Yo();Nc(z,"TAProdDebugSignal",Xo)}function Yo(){if(!Qo){Qo=!0;Zo();var a=Uo;Uo=void 0;a==null||a.forEach(function(b){Vo(b)})}}
function Zo(){var a=z.documentElement.getAttribute("data-tag-assistant-prod-present");Oo(a)?So=1:!No(a)||Po||Ro?So=2:(Ro=!0,Mc(z,"TAProdDebugSignal",Xo,!1),x.setTimeout(function(){Yo();Po=!0},200))}function Wo(){if(!To)return!1;switch(So){case 1:case 0:return!0;case 2:return!1;default:return!1}};var $o=!1;function ap(a,b){var c=Gm(),d=Em();if(Wo()){var e=bp("INIT");e.containerLoadSource=a!=null?a:0;b&&(e.parentTargetReference=b);e.aliases=c;e.destinations=d;Vo(e)}}
function cp(a){var b,c,d,e;b=a.targetId;c=a.request;d=a.Za;e=a.isBatched;var f;if(f=Wo()){var g;a:switch(c.endpoint){case 19:case 47:case 44:g=!0;break a;default:g=!1}f=!g}if(f){var h=bp("GTAG_HIT",{eventId:d.eventId,priorityId:d.priorityId});h.target=b;h.url=c.url;c.postBody&&(h.postBody=c.postBody);h.parameterEncoding=c.parameterEncoding;h.endpoint=c.endpoint;e!==void 0&&(h.isBatched=e);Vo(h)}}function dp(a){Wo()&&cp(a())}
function bp(a,b){b=b===void 0?{}:b;b.groupId=ep;var c,d=b,e={publicId:fp};d.eventId!=null&&(e.eventId=d.eventId);d.priorityId!=null&&(e.priorityId=d.priorityId);d.eventName&&(e.eventName=d.eventName);d.groupId&&(e.groupId=d.groupId);d.tagName&&(e.tagName=d.tagName);c={containerProduct:"GTM",key:e,version:'5',messageType:a};c.containerProduct=$o?"OGT":"GTM";c.key.targetRef=gp;return c}var fp="",gp={ctid:"",isDestination:!1},ep;
function hp(a){var b=mg.ctid,c=Dm();So=0;To=!0;Zo();ep=a;fp=b;$o=fk;gp={ctid:b,isDestination:c}};var ip=[J.m.U,J.m.ja,J.m.V,J.m.La],jp,kp;function lp(a){var b=a[J.m.hc];b||(b=[""]);for(var c={cg:0};c.cg<b.length;c={cg:c.cg},++c.cg)tb(a,function(d){return function(e,f){if(e!==J.m.hc){var g=Ko(f),h=b[d.cg],m=oo(),n=po();$m=!0;Zm&&fb("TAGGING",20);Vm().declare(e,g,h,m,n)}}}(c))}
function mp(a){co();!kp&&jp&&eo("crc");kp=!0;var b=a[J.m.rg];b&&L(41);var c=a[J.m.hc];c?L(40):c=[""];for(var d={dg:0};d.dg<c.length;d={dg:d.dg},++d.dg)tb(a,function(e){return function(f,g){if(f!==J.m.hc&&f!==J.m.rg){var h=Lo(g),m=c[e.dg],n=Number(b),p=oo(),q=po();n=n===void 0?0:n;Zm=!0;$m&&fb("TAGGING",20);Vm().default(f,h,m,p,q,n,bn)}}}(d))}
function np(a){bn.usedContainerScopedDefaults=!0;var b=a[J.m.hc];if(b){var c=Array.isArray(b)?b:[b];if(!c.includes(po())&&!c.includes(oo()))return}tb(a,function(d,e){switch(d){case "ad_storage":case "analytics_storage":case "ad_user_data":case "ad_personalization":break;default:return}bn.usedContainerScopedDefaults=!0;bn.containerScopedDefaults[d]=e==="granted"?3:2})}
function op(a,b){co();jp=!0;tb(a,function(c,d){var e=Ko(d);Zm=!0;$m&&fb("TAGGING",20);Vm().update(c,e,bn)});hn(b.eventId,b.priorityId)}function pp(a){a.hasOwnProperty("all")&&(bn.selectedAllCorePlatformServices=!0,tb(Io,function(b){bn.corePlatformServices[b]=a.all==="granted";bn.usedCorePlatformServices=!0}));tb(a,function(b,c){b!=="all"&&(bn.corePlatformServices[b]=c==="granted",bn.usedCorePlatformServices=!0)})}function P(a){Array.isArray(a)||(a=[a]);return a.every(function(b){return cn(b)})}
function qp(a,b){gn(a,b)}function rp(a,b){kn(a,b)}function sp(a,b){jn(a,b)}function tp(){var a=[J.m.U,J.m.La,J.m.V];Vm().waitForUpdate(a,500,bn)}function up(a){for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;Vm().clearTimeout(d,void 0,bn)}hn()}function vp(){if(!gk)for(var a=ro()?rk(Rj.Ua):rk(Rj.rb),b=0;b<ip.length;b++){var c=ip[b],d=c,e=a[c]?"granted":"denied";Vm().implicit(d,e)}};var wp=!1,xp=[];function yp(){if(!wp){wp=!0;for(var a=xp.length-1;a>=0;a--)xp[a]();xp=[]}};var zp=x.google_tag_manager=x.google_tag_manager||{};function Ap(a,b){return zp[a]=zp[a]||b()}function Bp(){var a=mg.ctid,b=Cp;zp[a]=zp[a]||b}function Dp(){var a=zp.sequence||1;zp.sequence=a+1;return a}x.google_tag_data=x.google_tag_data||{};function Ep(){if(zp.pscdl!==void 0)zn(vn.Z.Lh)===void 0&&yn(vn.Z.Lh,zp.pscdl);else{var a=function(c){zp.pscdl=c;yn(vn.Z.Lh,c)},b=function(){a("error")};try{vc.cookieDeprecationLabel?(a("pending"),vc.cookieDeprecationLabel.getValue().then(a).catch(b)):a("noapi")}catch(c){b(c)}}};var Fp=0;function Gp(a){ll&&a===void 0&&Fp===0&&(Mn("mcc","1"),Fp=1)};var Hp={Bf:{Tm:"cd",Um:"ce",Vm:"cf",Wm:"cpf",Xm:"cu"}};var Ip=/^(?:AW|DC|G|GF|GT|HA|MC|UA)$/,Jp=/\s/;
function Kp(a,b){if(mb(a)){a=yb(a);var c=a.indexOf("-");if(!(c<0)){var d=a.substring(0,c);if(Ip.test(d)){var e=a.substring(c+1),f;if(b){var g=function(n){var p=n.indexOf("/");return p<0?[n]:[n.substring(0,p),n.substring(p+1)]};f=g(e);if(d==="DC"&&f.length===2){var h=g(f[1]);h.length===2&&(f[1]=h[0],f.push(h[1]))}}else{f=e.split("/");for(var m=0;m<f.length;m++)if(!f[m]||Jp.test(f[m])&&(d!=="AW"||m!==1))return}return{id:a,prefix:d,destinationId:d+"-"+f[0],ids:f}}}}}
function Lp(a,b){for(var c={},d=0;d<a.length;++d){var e=Kp(a[d],b);e&&(c[e.id]=e)}var f=[],g;for(g in c)if(c.hasOwnProperty(g)){var h=c[g];h.prefix==="AW"&&h.ids[Mp[1]]&&f.push(h.destinationId)}for(var m=0;m<f.length;++m)delete c[f[m]];for(var n=[],p=l(Object.keys(c)),q=p.next();!q.done;q=p.next())n.push(c[q.value]);return n}var Np={},Mp=(Np[0]=0,Np[1]=1,Np[2]=2,Np[3]=0,Np[4]=1,Np[5]=0,Np[6]=0,Np[7]=0,Np);var Op=Number('')||500,Pp={},Qp={},Rp={initialized:11,complete:12,interactive:13},Sp={},Tp=Object.freeze((Sp[J.m.qb]=!0,Sp)),Up=void 0;function Vp(a,b){if(b.length&&ll){var c;(c=Pp)[a]!=null||(c[a]=[]);Qp[a]!=null||(Qp[a]=[]);var d=b.filter(function(e){return!Qp[a].includes(e)});Pp[a].push.apply(Pp[a],xa(d));Qp[a].push.apply(Qp[a],xa(d));!Up&&d.length>0&&(Nn("tdc",!0),Up=x.setTimeout(function(){Qn();Pp={};Up=void 0},Op))}}
function Wp(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}
function Xp(a,b,c,d){c=c===void 0?{}:c;d=d===void 0?"":d;if(a===b)return[];var e=function(r,t){var u;ld(t)==="object"?u=t[r]:ld(t)==="array"&&(u=t[r]);return u===void 0?Tp[r]:u},f=Wp(a,b),g;for(g in f)if(f.hasOwnProperty(g)){var h=(d?d+".":"")+g,m=e(g,a),n=e(g,b),p=ld(m)==="object"||ld(m)==="array",q=ld(n)==="object"||ld(n)==="array";if(p&&q)Xp(m,n,c,h);else if(p||q||m!==n)c[h]=!0}return Object.keys(c)}
function Yp(){Mn("tdc",function(){Up&&(x.clearTimeout(Up),Up=void 0);var a=[],b;for(b in Pp)Pp.hasOwnProperty(b)&&a.push(b+"*"+Pp[b].join("."));return a.length?a.join("!"):void 0},!1)};var Zp=function(a,b,c,d,e,f,g,h,m,n,p){this.eventId=a;this.priorityId=b;this.C=c;this.R=d;this.N=e;this.P=f;this.H=g;this.eventMetadata=h;this.onSuccess=m;this.onFailure=n;this.isGtmEvent=p},$p=function(a,b){var c=[];switch(b){case 3:c.push(a.C);c.push(a.R);c.push(a.N);c.push(a.P);c.push(a.H);break;case 2:c.push(a.C);break;case 1:c.push(a.R);c.push(a.N);c.push(a.P);c.push(a.H);break;case 4:c.push(a.C),c.push(a.R),c.push(a.N),c.push(a.P)}return c},N=function(a,b,c,d){for(var e=l($p(a,d===void 0?3:
d)),f=e.next();!f.done;f=e.next()){var g=f.value;if(g[b]!==void 0)return g[b]}return c},aq=function(a){for(var b={},c=$p(a,4),d=l(c),e=d.next();!e.done;e=d.next())for(var f=Object.keys(e.value),g=l(f),h=g.next();!h.done;h=g.next())b[h.value]=1;return Object.keys(b)};
Zp.prototype.getMergedValues=function(a,b,c){function d(n){nd(n)&&tb(n,function(p,q){f=!0;e[p]=q})}b=b===void 0?3:b;var e={},f=!1;c&&d(c);var g=$p(this,b);g.reverse();for(var h=l(g),m=h.next();!m.done;m=h.next())d(m.value[a]);return f?e:void 0};
var bq=function(a){for(var b=[J.m.Ve,J.m.Re,J.m.Se,J.m.Te,J.m.Ue,J.m.We,J.m.Xe],c=$p(a,3),d=l(c),e=d.next();!e.done;e=d.next()){for(var f=e.value,g={},h=!1,m=l(b),n=m.next();!n.done;n=m.next()){var p=n.value;f[p]!==void 0&&(g[p]=f[p],h=!0)}var q=h?g:void 0;if(q)return q}return{}},cq=function(a,b){this.eventId=a;this.priorityId=b;this.H={};this.R={};this.C={};this.N={};this.fa={};this.P={};this.eventMetadata={};this.isGtmEvent=!1;this.onSuccess=function(){};this.onFailure=function(){}},dq=function(a,
b){a.H=b;return a},eq=function(a,b){a.R=b;return a},fq=function(a,b){a.C=b;return a},gq=function(a,b){a.N=b;return a},hq=function(a,b){a.fa=b;return a},iq=function(a,b){a.P=b;return a},jq=function(a,b){a.eventMetadata=b||{};return a},kq=function(a,b){a.onSuccess=b;return a},lq=function(a,b){a.onFailure=b;return a},mq=function(a,b){a.isGtmEvent=b;return a},nq=function(a){return new Zp(a.eventId,a.priorityId,a.H,a.R,a.C,a.N,a.P,a.eventMetadata,a.onSuccess,a.onFailure,a.isGtmEvent)};var Q={A:{Ij:"accept_by_default",qg:"add_tag_timing",Hh:"allow_ad_personalization",Kj:"batch_on_navigation",Mj:"client_id_source",He:"consent_event_id",Ie:"consent_priority_id",Tq:"consent_state",da:"consent_updated",Wc:"conversion_linker_enabled",xa:"cookie_options",tg:"create_dc_join",ug:"create_fpm_geo_join",vg:"create_fpm_signals_join",Od:"create_google_join",Ke:"em_event",Wq:"endpoint_for_debug",Yj:"enhanced_client_id_source",Oh:"enhanced_match_result",ke:"euid_mode_enabled",hb:"event_start_timestamp_ms",
Wk:"event_usage",Wg:"extra_tag_experiment_ids",hr:"add_parameter",wi:"attribution_reporting_experiment",xi:"counting_method",Xg:"send_as_iframe",ir:"parameter_order",Yg:"parsed_target",Yn:"ga4_collection_subdomain",Zk:"gbraid_cookie_marked",ia:"hit_type",ud:"hit_type_override",eo:"is_config_command",Ef:"is_consent_update",Ff:"is_conversion",il:"is_ecommerce",vd:"is_external_event",Ci:"is_fallback_aw_conversion_ping_allowed",Gf:"is_first_visit",jl:"is_first_visit_conversion",Zg:"is_fl_fallback_conversion_flow_allowed",
Hf:"is_fpm_encryption",ah:"is_fpm_split",ne:"is_gcp_conversion",kl:"is_google_signals_allowed",wd:"is_merchant_center",bh:"is_new_to_site",eh:"is_server_side_destination",oe:"is_session_start",nl:"is_session_start_conversion",lr:"is_sgtm_ga_ads_conversion_study_control_group",mr:"is_sgtm_prehit",ol:"is_sgtm_service_worker",Di:"is_split_conversion",fo:"is_syn",If:"join_id",Ei:"join_elapsed",Jf:"join_timer_sec",se:"tunnel_updated",ur:"prehit_for_retry",wr:"promises",xr:"record_aw_latency",yc:"redact_ads_data",
te:"redact_click_ids",qo:"remarketing_only",yl:"send_ccm_parallel_ping",ih:"send_fledge_experiment",zr:"send_ccm_parallel_test_ping",Of:"send_to_destinations",Ii:"send_to_targets",zl:"send_user_data_hit",ib:"source_canonical_id",Ba:"speculative",El:"speculative_in_message",Fl:"suppress_script_load",Gl:"syn_or_mod",Kl:"transient_ecsid",Pf:"transmission_type",jb:"user_data",Cr:"user_data_from_automatic",Dr:"user_data_from_automatic_getter",ve:"user_data_from_code",mh:"user_data_from_manual",Ml:"user_data_mode",
Qf:"user_id_updated"}};var oq={Mm:Number("5"),Ur:Number("")},pq=[],qq=!1;function rq(a){pq.push(a)}var sq="?id="+mg.ctid,tq=void 0,uq={},vq=void 0,wq=new function(){var a=5;oq.Mm>0&&(a=oq.Mm);this.H=a;this.C=0;this.N=[]},xq=1E3;
function yq(a,b){var c=tq;if(c===void 0)if(b)c=Dp();else return"";for(var d=[el("https://www.googletagmanager.com"),"/a",sq],e=l(pq),f=e.next();!f.done;f=e.next())for(var g=f.value,h=g({eventId:c,Nd:!!a}),m=l(h),n=m.next();!n.done;n=m.next()){var p=l(n.value),q=p.next().value,r=p.next().value;d.push("&"+q+"="+r)}d.push("&z=0");return d.join("")}
function zq(){if(Rj.fa&&(vq&&(x.clearTimeout(vq),vq=void 0),tq!==void 0&&Aq)){var a=un(Um.X.Oc);if(qn(a))qq||(qq=!0,sn(a,zq));else{var b;if(!(b=uq[tq])){var c=wq;b=c.C<c.H?!1:Ab()-c.N[c.C%c.H]<1E3}if(b||xq--<=0)L(1),uq[tq]=!0;else{var d=wq,e=d.C++%d.H;d.N[e]=Ab();var f=yq(!0);mm({destinationId:mg.ctid,endpoint:56,eventId:tq},f);qq=Aq=!1}}}}function Bq(){if(kl&&Rj.fa){var a=yq(!0,!0);mm({destinationId:mg.ctid,endpoint:56,eventId:tq},a)}}var Aq=!1;
function Cq(a){uq[a]||(a!==tq&&(zq(),tq=a),Aq=!0,vq||(vq=x.setTimeout(zq,500)),yq().length>=2022&&zq())}var Dq=qb();function Eq(){Dq=qb()}function Fq(){return[["v","3"],["t","t"],["pid",String(Dq)]]};var Gq={};function Hq(a,b,c){kl&&a!==void 0&&(Gq[a]=Gq[a]||[],Gq[a].push(c+b),Cq(a))}function Iq(a){var b=a.eventId,c=a.Nd,d=[],e=Gq[b]||[];e.length&&d.push(["epr",e.join(".")]);c&&delete Gq[b];return d};function Jq(a,b,c,d){var e=Kp(a,!0);e&&Kq.register(e,b,c,d)}function Lq(a,b,c,d){var e=Kp(c,d.isGtmEvent);e&&(dk&&(d.deferrable=!0),Kq.push("event",[b,a],e,d))}function Mq(a,b,c,d){var e=Kp(c,d.isGtmEvent);e&&Kq.push("get",[a,b],e,d)}function Nq(a){var b=Kp(a,!0),c;b?c=Oq(Kq,b).C:c={};return c}function Pq(a,b){var c=Kp(a,!0);c&&Qq(Kq,c,b)}
var Rq=function(){this.R={};this.C={};this.H={};this.fa=null;this.P={};this.N=!1;this.status=1},Sq=function(a,b,c,d){this.H=Ab();this.C=b;this.args=c;this.messageContext=d;this.type=a},Tq=function(){this.destinations={};this.C={};this.commands=[]},Oq=function(a,b){return a.destinations[b.destinationId]=a.destinations[b.destinationId]||new Rq},Uq=function(a,b,c,d){if(d.C){var e=Oq(a,d.C),f=e.fa;if(f){var g=od(c,null),h=od(e.R[d.C.id],null),m=od(e.P,null),n=od(e.C,null),p=od(a.C,null),q={};if(kl)try{q=
od(tk,null)}catch(w){L(72)}var r=d.C.prefix,t=function(w){Hq(d.messageContext.eventId,r,w)},u=nq(mq(lq(kq(jq(hq(gq(iq(fq(eq(dq(new cq(d.messageContext.eventId,d.messageContext.priorityId),g),h),m),n),p),q),d.messageContext.eventMetadata),function(){if(t){var w=t;t=void 0;w("2");if(d.messageContext.onSuccess)d.messageContext.onSuccess()}}),function(){if(t){var w=t;t=void 0;w("3");if(d.messageContext.onFailure)d.messageContext.onFailure()}}),!!d.messageContext.isGtmEvent)),v=function(){try{Hq(d.messageContext.eventId,
r,"1");var w=d.type,y=d.C.id;if(ll&&w==="config"){var A,C=(A=Kp(y))==null?void 0:A.ids;if(!(C&&C.length>1)){var D,G=zc("google_tag_data",{});G.td||(G.td={});D=G.td;var I=od(u.P);od(u.C,I);var M=[],T;for(T in D)D.hasOwnProperty(T)&&Xp(D[T],I).length&&M.push(T);M.length&&(Vp(y,M),fb("TAGGING",Rp[z.readyState]||14));D[y]=I}}f(d.C.id,b,d.H,u)}catch(da){Hq(d.messageContext.eventId,r,"4")}};b==="gtag.get"?v():sn(e.ma,v)}}};
Tq.prototype.register=function(a,b,c,d){var e=Oq(this,a);e.status!==3&&(e.fa=b,e.status=3,e.ma=un(c),Qq(this,a,d||{}),this.flush())};
Tq.prototype.push=function(a,b,c,d){c!==void 0&&(Oq(this,c).status===1&&(Oq(this,c).status=2,this.push("require",[{}],c,{})),Oq(this,c).N&&(d.deferrable=!1),d.eventMetadata||(d.eventMetadata={}),d.eventMetadata[Q.A.Of]||(d.eventMetadata[Q.A.Of]=[c.destinationId]),d.eventMetadata[Q.A.Ii]||(d.eventMetadata[Q.A.Ii]=[c.id]));this.commands.push(new Sq(a,c,b,d));d.deferrable||this.flush()};
Tq.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.commands.length;e={Qc:void 0,rh:void 0}){var f=this.commands[0],g=f.C;if(f.messageContext.deferrable)!g||Oq(this,g).N?(f.messageContext.deferrable=!1,this.commands.push(f)):c.push(f),this.commands.shift();else{switch(f.type){case "require":if(Oq(this,g).status!==3&&!a){this.commands.push.apply(this.commands,c);return}break;case "set":var h=f.args[0];tb(h,function(t,u){od(Hb(t,u),b.C)});Pj(h,!0);break;case "config":var m=Oq(this,g);
e.Qc={};tb(f.args[0],function(t){return function(u,v){od(Hb(u,v),t.Qc)}}(e));var n=!!e.Qc[J.m.pd];delete e.Qc[J.m.pd];var p=g.destinationId===g.id;Pj(e.Qc,!0);n||(p?m.P={}:m.R[g.id]={});m.N&&n||Uq(this,J.m.qa,e.Qc,f);m.N=!0;p?od(e.Qc,m.P):(od(e.Qc,m.R[g.id]),L(70));d=!0;break;case "event":e.rh={};tb(f.args[0],function(t){return function(u,v){od(Hb(u,v),t.rh)}}(e));Pj(e.rh);Uq(this,f.args[1],e.rh,f);break;case "get":var q={},r=(q[J.m.rc]=f.args[0],q[J.m.Ic]=f.args[1],q);Uq(this,J.m.Eb,r,f)}this.commands.shift();
Vq(this,f)}}this.commands.push.apply(this.commands,c);d&&this.flush()};var Vq=function(a,b){if(b.type!=="require")if(b.C)for(var c=Oq(a,b.C).H[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.destinations)if(a.destinations.hasOwnProperty(e)){var f=a.destinations[e];if(f&&f.H)for(var g=f.H[b.type]||[],h=0;h<g.length;h++)g[h]()}},Qq=function(a,b,c){var d=od(c,null);od(Oq(a,b).C,d);Oq(a,b).C=d},Kq=new Tq;function Wq(a,b,c){return typeof a.addEventListener==="function"?(a.addEventListener(b,c,!1),!0):!1}function Xq(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function Yq(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=Ml(a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=sc(g,e);h>=0&&Array.prototype.splice.call(g,h,1)}Xq(e,"load",f);Xq(e,"error",f)};Wq(e,"load",f);Wq(e,"error",f)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}
function Zq(a){var b;b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";Jl(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});$q(c,b)}
function $q(a,b){var c=window,d;b=b===void 0?!1:b;d=d===void 0?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?e.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else Yq(c,a,b===void 0?!1:b,d===void 0?!1:d)};var ar=function(){this.fa=this.fa;this.P=this.P};ar.prototype.fa=!1;ar.prototype.dispose=function(){this.fa||(this.fa=!0,this.N())};ar.prototype[ha.Symbol.dispose]=function(){this.dispose()};ar.prototype.addOnDisposeCallback=function(a,b){this.fa?b!==void 0?a.call(b):a():(this.P||(this.P=[]),b&&(a=a.bind(b)),this.P.push(a))};ar.prototype.N=function(){if(this.P)for(;this.P.length;)this.P.shift()()};function br(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3}
var cr=function(a,b){b=b===void 0?{}:b;ar.call(this);this.C=null;this.ma={};this.rb=0;this.R=null;this.H=a;var c;this.Ua=(c=b.timeoutMs)!=null?c:500;var d;this.Da=(d=b.Jr)!=null?d:!1};va(cr,ar);cr.prototype.N=function(){this.ma={};this.R&&(Xq(this.H,"message",this.R),delete this.R);delete this.ma;delete this.H;delete this.C;ar.prototype.N.call(this)};var er=function(a){return typeof a.H.__tcfapi==="function"||dr(a)!=null};
cr.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.Da},d=nl(function(){return a(c)}),e=0;this.Ua!==-1&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.Ua));var f=function(g,h){clearTimeout(e);g?(c=g,c.internalErrorState=br(c),c.internalBlockOnErrors=b.Da,h&&c.internalErrorState===0||(c.tcString="tcunavailable",h||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{fr(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};cr.prototype.removeEventListener=function(a){a&&a.listenerId&&fr(this,"removeEventListener",null,a.listenerId)};
var hr=function(a,b,c){var d;d=d===void 0?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(f!==void 0){e=f[d===void 0?"755":d];break a}}e=void 0}var g=e;if(g===0)return!1;var h=c;c===2?(h=0,g===2&&(h=1)):c===3&&(h=1,g===1&&(h=0));var m;if(h===0)if(a.purpose&&a.vendor){var n=gr(a.vendor.consents,d===void 0?"755":d);m=n&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:n&&gr(a.purpose.consents,b)}else m=!0;else m=h===1?a.purpose&&a.vendor?gr(a.purpose.legitimateInterests,
b)&&gr(a.vendor.legitimateInterests,d===void 0?"755":d):!0:!0;return m},gr=function(a,b){return!(!a||!a[b])},fr=function(a,b,c,d){c||(c=function(){});var e=a.H;if(typeof e.__tcfapi==="function"){var f=e.__tcfapi;f(b,2,c,d)}else if(dr(a)){ir(a);var g=++a.rb;a.ma[g]=c;if(a.C){var h={};a.C.postMessage((h.__tcfapiCall={command:b,version:2,callId:g,parameter:d},h),"*")}}else c({},!1)},dr=function(a){if(a.C)return a.C;a.C=Kl(a.H,"__tcfapiLocator");return a.C},ir=function(a){if(!a.R){var b=function(c){try{var d;
d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.ma[d.callId](d.returnValue,d.success)}catch(e){}};a.R=b;Wq(a.H,"message",b)}},jr=function(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=br(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(Zq({e:String(a.internalErrorState)}),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0};var kr={1:0,3:0,4:0,7:3,9:3,10:3};function lr(){return Ap("tcf",function(){return{}})}var mr=function(){return new cr(x,{timeoutMs:-1})};
function nr(){var a=lr(),b=mr();er(b)&&!or()&&!pr()&&L(124);if(!a.active&&er(b)){or()&&(a.active=!0,a.purposes={},a.cmpId=0,a.tcfPolicyVersion=0,Vm().active=!0,a.tcString="tcunavailable");tp();try{b.addEventListener(function(c){if(c.internalErrorState!==0)qr(a),up([J.m.U,J.m.La,J.m.V]),Vm().active=!0;else if(a.gdprApplies=c.gdprApplies,a.cmpId=c.cmpId,a.enableAdvertiserConsentMode=c.enableAdvertiserConsentMode,pr()&&(a.active=!0),!rr(c)||or()||pr()){a.tcfPolicyVersion=c.tcfPolicyVersion;var d;if(c.gdprApplies===
!1){var e={},f;for(f in kr)kr.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if(rr(c)){var g={},h;for(h in kr)if(kr.hasOwnProperty(h))if(h==="1"){var m,n=c,p={up:!0};p=p===void 0?{}:p;m=jr(n)?n.gdprApplies===!1?!0:n.tcString==="tcunavailable"?!p.idpcApplies:(p.idpcApplies||n.gdprApplies!==void 0||p.up)&&(p.idpcApplies||typeof n.tcString==="string"&&n.tcString.length)?hr(n,"1",0):!0:!1;g["1"]=m}else g[h]=hr(c,h,kr[h]);d=g}if(d){a.tcString=c.tcString||"tcempty";a.purposes=d;var q={},
r=(q[J.m.U]=a.purposes["1"]?"granted":"denied",q);a.gdprApplies!==!0?(up([J.m.U,J.m.La,J.m.V]),Vm().active=!0):(r[J.m.La]=a.purposes["3"]&&a.purposes["4"]?"granted":"denied",typeof a.tcfPolicyVersion==="number"&&a.tcfPolicyVersion>=4?r[J.m.V]=a.purposes["1"]&&a.purposes["7"]?"granted":"denied":up([J.m.V]),op(r,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:sr()||""}))}}else up([J.m.U,J.m.La,J.m.V])})}catch(c){qr(a),up([J.m.U,J.m.La,J.m.V]),Vm().active=!0}}}
function qr(a){a.type="e";a.tcString="tcunavailable"}function rr(a){return a.eventStatus==="tcloaded"||a.eventStatus==="useractioncomplete"||a.eventStatus==="cmpuishown"}function or(){return x.gtag_enable_tcf_support===!0}function pr(){return lr().enableAdvertiserConsentMode===!0}function sr(){var a=lr();if(a.active)return a.tcString}function tr(){var a=lr();if(a.active&&a.gdprApplies!==void 0)return a.gdprApplies?"1":"0"}
function ur(a){if(!kr.hasOwnProperty(String(a)))return!0;var b=lr();return b.active&&b.purposes?!!b.purposes[String(a)]:!0};var vr=[J.m.U,J.m.ja,J.m.V,J.m.La],wr={},xr=(wr[J.m.U]=1,wr[J.m.ja]=2,wr);function yr(a){if(a===void 0)return 0;switch(N(a,J.m.Ga)){case void 0:return 1;case !1:return 3;default:return 2}}function zr(){return(E(183)?$i.Ap:$i.Bp).indexOf(po())!==-1&&vc.globalPrivacyControl===!0}function Ar(a){if(zr())return!1;var b=yr(a);if(b===3)return!1;switch(dn(J.m.La)){case 1:case 3:return!0;case 2:return!1;case 4:return b===2;case 0:return!0;default:return!1}}
function Br(){return fn()||!cn(J.m.U)||!cn(J.m.ja)}function Cr(){var a={},b;for(b in xr)xr.hasOwnProperty(b)&&(a[xr[b]]=dn(b));return"G1"+ef(a[1]||0)+ef(a[2]||0)}var Dr={},Er=(Dr[J.m.U]=0,Dr[J.m.ja]=1,Dr[J.m.V]=2,Dr[J.m.La]=3,Dr);function Fr(a){switch(a){case void 0:return 1;case !0:return 3;case !1:return 2;default:return 0}}
function Gr(a){for(var b="1",c=0;c<vr.length;c++){var d=b,e,f=vr[c],g=bn.delegatedConsentTypes[f];e=g===void 0?0:Er.hasOwnProperty(g)?12|Er[g]:8;var h=Vm();h.accessedAny=!0;var m=h.entries[f]||{};e=e<<2|Fr(m.implicit);b=d+(""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[e]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Fr(m.declare)<<4|Fr(m.default)<<2|Fr(m.update)])}var n=b,p=(zr()?1:0)<<3,q=(fn()?1:0)<<2,r=yr(a);b=n+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[p|
q|r];return b+=""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[bn.containerScopedDefaults.ad_storage<<4|bn.containerScopedDefaults.analytics_storage<<2|bn.containerScopedDefaults.ad_user_data]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[(bn.usedContainerScopedDefaults?1:0)<<2|bn.containerScopedDefaults.ad_personalization]}
function Hr(){if(!cn(J.m.V))return"-";for(var a=Object.keys(Io),b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=bn.corePlatformServices[e]!==!1}for(var f="",g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;b[m]&&(f+=Io[m])}(bn.usedCorePlatformServices?bn.selectedAllCorePlatformServices:1)&&(f+="o");return f||"-"}function Ir(){return ro()||(or()||pr())&&tr()==="1"?"1":"0"}function Jr(){return(ro()?!0:!(!or()&&!pr())&&tr()==="1")||!cn(J.m.V)}
function Kr(){var a="0",b="0",c;var d=lr();c=d.active?d.cmpId:void 0;typeof c==="number"&&c>=0&&c<=4095&&(a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c>>6&63],b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c&63]);var e="0",f;var g=lr();f=g.active?g.tcfPolicyVersion:void 0;typeof f==="number"&&f>=0&&f<=63&&(e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f]);var h=0;ro()&&(h|=1);tr()==="1"&&(h|=2);or()&&(h|=4);var m;var n=lr();m=n.enableAdvertiserConsentMode!==
void 0?n.enableAdvertiserConsentMode?"1":"0":void 0;m==="1"&&(h|=8);Vm().waitPeriodTimedOut&&(h|=16);return"1"+a+b+e+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[h]}function Lr(){return po()==="US-CO"};var Mr;function Nr(){if(yc===null)return 0;var a=cd();if(!a)return 0;var b=a.getEntriesByName(yc,"resource")[0];if(!b)return 0;switch(b.deliveryType){case "":return 1;case "cache":return 2;case "navigational-prefetch":return 3;default:return 0}}var Or={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7};
function Pr(a){a=a===void 0?{}:a;var b=mg.ctid.split("-")[0].toUpperCase(),c={ctid:mg.ctid,yj:Xj,Cj:Wj,fm:Cm.qe?2:1,Hq:a.Dm,we:mg.canonicalContainerId};if(E(210)){var d;c.yq=(d=Jm())==null?void 0:d.canonicalContainerId}if(E(204)){var e;c.Po=(e=Mr)!=null?e:Mr=Nr()}c.we!==a.Oa&&(c.Oa=a.Oa);var f=Hm();c.qm=f?f.canonicalContainerId:void 0;fk?(c.Uc=Or[b],c.Uc||(c.Uc=0)):c.Uc=gk?13:10;Rj.C?(c.Ah=0,c.Rl=2):c.Ah=Rj.N?1:3;var g={6:!1};Rj.H===2?g[7]=!0:Rj.H===1&&(g[2]=!0);if(yc){var h=Qk(Wk(yc),"host");h&&
(g[8]=h.match(/^(www\.)?googletagmanager\.com$/)===null)}c.Tl=g;return hf(c,a.oh)}
function Qr(){if(!E(192))return Pr();if(E(193))return hf({yj:Xj,Cj:Wj});var a=mg.ctid.split("-")[0].toUpperCase(),b={ctid:mg.ctid,yj:Xj,Cj:Wj,fm:Cm.qe?2:1,we:mg.canonicalContainerId},c=Hm();b.qm=c?c.canonicalContainerId:void 0;fk?(b.Uc=Or[a],b.Uc||(b.Uc=0)):b.Uc=gk?13:10;Rj.C?(b.Ah=0,b.Rl=2):b.Ah=Rj.N?1:3;var d={6:!1};Rj.H===2?d[7]=!0:Rj.H===1&&(d[2]=!0);if(yc){var e=Qk(Wk(yc),"host");e&&(d[8]=e.match(/^(www\.)?googletagmanager\.com$/)===null)}b.Tl=d;return hf(b)};function Rr(a,b,c,d){var e,f=Number(a.Cc!=null?a.Cc:void 0);f!==0&&(e=new Date((b||Ab())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,Ec:d}};var Sr=["ad_storage","ad_user_data"];function Tr(a,b){if(!a)return fb("TAGGING",32),10;if(b===null||b===void 0||b==="")return fb("TAGGING",33),11;var c=Ur(!1);if(c.error!==0)return fb("TAGGING",34),c.error;if(!c.value)return fb("TAGGING",35),2;c.value[a]=b;var d=Vr(c);d!==0&&fb("TAGGING",36);return d}
function Wr(a){if(!a)return fb("TAGGING",27),{error:10};var b=Ur();if(b.error!==0)return fb("TAGGING",29),b;if(!b.value)return fb("TAGGING",30),{error:2};if(!(a in b.value))return fb("TAGGING",31),{value:void 0,error:15};var c=b.value[a];return c===null||c===void 0||c===""?(fb("TAGGING",28),{value:void 0,error:11}):{value:c,error:0}}
function Ur(a){a=a===void 0?!0:a;if(!cn(Sr))return fb("TAGGING",43),{error:3};try{if(!x.localStorage)return fb("TAGGING",44),{error:1}}catch(f){return fb("TAGGING",45),{error:14}}var b={schema:"gcl",version:1},c=void 0;try{c=x.localStorage.getItem("_gcl_ls")}catch(f){return fb("TAGGING",46),{error:13}}try{if(c){var d=JSON.parse(c);if(d&&typeof d==="object")b=d;else return fb("TAGGING",47),{error:12}}}catch(f){return fb("TAGGING",48),{error:8}}if(b.schema!=="gcl")return fb("TAGGING",49),{error:4};
if(b.version!==1)return fb("TAGGING",50),{error:5};try{var e=Xr(b);a&&e&&Vr({value:b,error:0})}catch(f){return fb("TAGGING",48),{error:8}}return{value:b,error:0}}
function Xr(a){if(!a||typeof a!=="object")return!1;if("expires"in a&&"value"in a){var b;typeof a.expires==="number"?b=a.expires:b=typeof a.expires==="string"?Number(a.expires):NaN;if(isNaN(b)||!(Date.now()<=b))return a.value=null,a.error=9,fb("TAGGING",54),!0}else{for(var c=!1,d=l(Object.keys(a)),e=d.next();!e.done;e=d.next())c=Xr(a[e.value])||c;return c}return!1}
function Vr(a){if(a.error)return a.error;if(!a.value)return fb("TAGGING",42),2;var b=a.value,c;try{c=JSON.stringify(b)}catch(d){return fb("TAGGING",52),6}try{x.localStorage.setItem("_gcl_ls",c)}catch(d){return fb("TAGGING",53),7}return 0};var Yr={oj:"value",sb:"conversionCount"},Zr={dm:9,xm:10,oj:"timeouts",sb:"timeouts"},$r=[Yr,Zr];function as(a){if(!bs(a))return{};var b=cs($r),c=b[a.sb];if(c===void 0||c===-1)return b;var d={},e=ma(Object,"assign").call(Object,{},b,(d[a.sb]=c+1,d));return ds(e)?e:b}
function cs(a){var b;a:{var c=Wr("gcl_ctr");if(c.error===0&&c.value&&typeof c.value==="object"){var d=c.value;try{b="value"in d&&typeof d.value==="object"?d.value:void 0;break a}catch(p){}}b=void 0}for(var e=b,f={},g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;if(e&&bs(m)){var n=e[m.oj];n===void 0||Number.isNaN(n)?f[m.sb]=-1:f[m.sb]=Number(n)}else f[m.sb]=-1}return f}
function es(){var a=as(Yr),b=a[Yr.sb];if(b===void 0||b<=0)return"";var c=a[Zr.sb];return c===void 0||c<0?b.toString():[b.toString(),c.toString()].join("~")}function ds(a,b){b=b||{};for(var c=Ab(),d=Rr(b,c,!0),e={},f=l($r),g=f.next();!g.done;g=f.next()){var h=g.value,m=a[h.sb];m!==void 0&&m!==-1&&(e[h.oj]=m)}e.creationTimeMs=c;return Tr("gcl_ctr",{value:e,expires:Number(d.expires)})===0?!0:!1}function bs(a){return cn(["ad_storage","ad_user_data"])?!a.xm||Ra(a.xm):!1}
function fs(a){return cn(["ad_storage","ad_user_data"])?!a.dm||Ra(a.dm):!1};function gs(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;d>=0;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=c!==0?b^c>>21:b;return b};var hs={O:{ro:0,Jj:1,sg:2,Pj:3,Jh:4,Nj:5,Oj:6,Qj:7,Kh:8,Uk:9,Tk:10,ui:11,Vk:12,Vg:13,Yk:14,Lf:15,po:16,ue:17,Ni:18,Oi:19,Pi:20,Il:21,Qi:22,Mh:23,Xj:24}};hs.O[hs.O.ro]="RESERVED_ZERO";hs.O[hs.O.Jj]="ADS_CONVERSION_HIT";hs.O[hs.O.sg]="CONTAINER_EXECUTE_START";hs.O[hs.O.Pj]="CONTAINER_SETUP_END";hs.O[hs.O.Jh]="CONTAINER_SETUP_START";hs.O[hs.O.Nj]="CONTAINER_BLOCKING_END";hs.O[hs.O.Oj]="CONTAINER_EXECUTE_END";hs.O[hs.O.Qj]="CONTAINER_YIELD_END";hs.O[hs.O.Kh]="CONTAINER_YIELD_START";hs.O[hs.O.Uk]="EVENT_EXECUTE_END";
hs.O[hs.O.Tk]="EVENT_EVALUATION_END";hs.O[hs.O.ui]="EVENT_EVALUATION_START";hs.O[hs.O.Vk]="EVENT_SETUP_END";hs.O[hs.O.Vg]="EVENT_SETUP_START";hs.O[hs.O.Yk]="GA4_CONVERSION_HIT";hs.O[hs.O.Lf]="PAGE_LOAD";hs.O[hs.O.po]="PAGEVIEW";hs.O[hs.O.ue]="SNIPPET_LOAD";hs.O[hs.O.Ni]="TAG_CALLBACK_ERROR";hs.O[hs.O.Oi]="TAG_CALLBACK_FAILURE";hs.O[hs.O.Pi]="TAG_CALLBACK_SUCCESS";hs.O[hs.O.Il]="TAG_EXECUTE_END";hs.O[hs.O.Qi]="TAG_EXECUTE_START";hs.O[hs.O.Mh]="CUSTOM_PERFORMANCE_START";hs.O[hs.O.Xj]="CUSTOM_PERFORMANCE_END";var is=[],js={},ks={};var ls=["2"];function ms(a){return a.origin!=="null"};function ns(a,b,c){for(var d=[],e=b.split(";"),f=function(p){return Ra(11)?p.trim():p.replace(/^\s*|\s*$/g,"")},g=0;g<e.length;g++){var h=e[g].split("="),m=f(h[0]);if(m&&m===a){var n=f(h.slice(1).join("="));n&&c&&(n=decodeURIComponent(n));d.push(n)}}return d};var os;function ps(a,b,c,d){return qs(d)?ns(a,String(b||rs()),c):[]}function ss(a,b,c,d,e){if(qs(e)){var f=ts(a,d,e);if(f.length===1)return f[0];if(f.length!==0){f=us(f,function(g){return g.cp},b);if(f.length===1)return f[0];f=us(f,function(g){return g.iq},c);return f[0]}}}function vs(a,b,c,d){var e=rs(),f=window;ms(f)&&(f.document.cookie=a);var g=rs();return e!==g||c!==void 0&&ps(b,g,!1,d).indexOf(c)>=0}
function ws(a,b,c,d){function e(w,y,A){if(A==null)return delete h[y],w;h[y]=A;return w+"; "+y+"="+A}function f(w,y){if(y==null)return w;h[y]=!0;return w+"; "+y}if(!qs(c.Ec))return 2;var g;b==null?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=xs(b),g=a+"="+b);var h={};g=e(g,"path",c.path);var m;c.expires instanceof Date?m=c.expires.toUTCString():c.expires!=null&&(m=""+c.expires);g=e(g,"expires",m);g=e(g,"max-age",c.bq);g=e(g,"samesite",c.zq);c.secure&&
(g=f(g,"secure"));var n=c.domain;if(n&&n.toLowerCase()==="auto"){for(var p=ys(),q=void 0,r=!1,t=0;t<p.length;++t){var u=p[t]!=="none"?p[t]:void 0,v=e(g,"domain",u);v=f(v,c.flags);try{d&&d(a,h)}catch(w){q=w;continue}r=!0;if(!zs(u,c.path)&&vs(v,a,b,c.Ec))return Ra(15)&&(os=u),0}if(q&&!r)throw q;return 1}n&&n.toLowerCase()!=="none"&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,h);return zs(n,c.path)?1:vs(g,a,b,c.Ec)?0:1}
function As(a,b,c){c.path==null&&(c.path="/");c.domain||(c.domain="auto");if(is.includes("2")){var d;(d=cd())==null||d.mark("2-"+hs.O.Mh+"-"+(ks["2"]||0))}var e=ws(a,b,c);if(is.includes("2")){var f="2-"+hs.O.Xj+"-"+(ks["2"]||0),g={start:"2-"+hs.O.Mh+"-"+(ks["2"]||0),end:f},h;(h=cd())==null||h.mark(f);var m,n,p=(n=(m=cd())==null?void 0:m.measure(f,g))==null?void 0:n.duration;p!==void 0&&(ks["2"]=(ks["2"]||0)+1,js["2"]=p+(js["2"]||0))}return e}
function us(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var h=a[g],m=b(h);m===c?d.push(h):f===void 0||m<f?(e=[h],f=m):m===f&&e.push(h)}return d.length>0?d:e}function ts(a,b,c){for(var d=[],e=ps(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),h=g.shift();if(!b||!h||b.indexOf(h)!==-1){var m=g.shift();if(m){var n=m.split("-");d.push({To:e[f],Uo:g.join("."),cp:Number(n[0])||1,iq:Number(n[1])||1})}}}return d}function xs(a){a&&a.length>1200&&(a=a.substring(0,1200));return a}
var Bs=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,Cs=/(^|\.)doubleclick\.net$/i;function zs(a,b){return a!==void 0&&(Cs.test(window.document.location.hostname)||b==="/"&&Bs.test(a))}function Ds(a){if(!a)return 1;var b=a;Ra(6)&&a==="none"&&(b=window.document.location.hostname);b=b.indexOf(".")===0?b.substring(1):b;return b.split(".").length}function Es(a){if(!a||a==="/")return 1;a[0]!=="/"&&(a="/"+a);a[a.length-1]!=="/"&&(a+="/");return a.split("/").length-1}
function Fs(a,b){var c=""+Ds(a),d=Es(b);d>1&&(c+="-"+d);return c}
var rs=function(){return ms(window)?window.document.cookie:""},qs=function(a){return a&&Ra(7)?(Array.isArray(a)?a:[a]).every(function(b){return en(b)&&cn(b)}):!0},ys=function(){var a=os,b=[];a&&b.push(a);var c=window.document.location.hostname.split(".");if(c.length===4){var d=c[c.length-1];if(Number(d).toString()===d)return["none"]}for(var e=c.length-2;e>=0;e--){var f=c.slice(e).join(".");f!==a&&b.push(f)}var g=window.document.location.hostname;Cs.test(g)||Bs.test(g)||b.push("none");return b};function Gs(a){var b=Math.round(Math.random()*2147483647);return a?String(b^gs(a)&2147483647):String(b)}function Hs(a){return[Gs(a),Math.round(Ab()/1E3)].join(".")}function Is(a,b,c,d,e){var f=Ds(b),g;return(g=ss(a,f,Es(c),d,e))==null?void 0:g.Uo};var Js;function Ks(){function a(g){c(g.target||g.srcElement||{})}function b(g){d(g.target||g.srcElement||{})}var c=Ls,d=Ms,e=Ns();if(!e.init){Mc(z,"mousedown",a);Mc(z,"keyup",a);Mc(z,"submit",b);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){d(this);f.call(this)};e.init=!0}}function Os(a,b,c,d,e){var f={callback:a,domains:b,fragment:c===2,placement:c,forms:d,sameHost:e};Ns().decorators.push(f)}
function Ps(a,b,c){for(var d=Ns().decorators,e={},f=0;f<d.length;++f){var g=d[f],h;if(h=!c||g.forms)a:{var m=g.domains,n=a,p=!!g.sameHost;if(m&&(p||n!==z.location.hostname))for(var q=0;q<m.length;q++)if(m[q]instanceof RegExp){if(m[q].test(n)){h=!0;break a}}else if(n.indexOf(m[q])>=0||p&&m[q].indexOf(n)>=0){h=!0;break a}h=!1}if(h){var r=g.placement;r===void 0&&(r=g.fragment?2:1);r===b&&Db(e,g.callback())}}return e}
function Ns(){var a=zc("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var Qs=/(.*?)\*(.*?)\*(.*)/,Rs=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,Ss=/^(?:www\.|m\.|amp\.)+/,Ts=/([^?#]+)(\?[^#]*)?(#.*)?/;function Us(a){var b=Ts.exec(a);if(b)return{uj:b[1],query:b[2],fragment:b[3]}}function Vs(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}
function Ws(a,b){var c=[vc.userAgent,(new Date).getTimezoneOffset(),vc.userLanguage||vc.language,Math.floor(Ab()/60/1E3)-(b===void 0?0:b),a].join("*"),d;if(!(d=Js)){for(var e=Array(256),f=0;f<256;f++){for(var g=f,h=0;h<8;h++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}Js=d;for(var m=4294967295,n=0;n<c.length;n++)m=m>>>8^Js[(m^c.charCodeAt(n))&255];return((m^-1)>>>0).toString(36)}
function Xs(a){return function(b){var c=Wk(x.location.href),d=c.search.replace("?",""),e=Nk(d,"_gl",!1,!0)||"";b.query=Ys(e)||{};var f=Qk(c,"fragment"),g;var h=-1;if(Fb(f,"_gl="))h=4;else{var m=f.indexOf("&_gl=");m>0&&(h=m+3+2)}if(h<0)g=void 0;else{var n=f.indexOf("&",h);g=n<0?f.substring(h):f.substring(h,n)}b.fragment=Ys(g||"")||{};a&&Zs(c,d,f)}}function $s(a,b){var c=Vs(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
function Zs(a,b,c){function d(g,h){var m=$s("_gl",g);m.length&&(m=h+m);return m}if(uc&&uc.replaceState){var e=Vs("_gl");if(e.test(b)||e.test(c)){var f=Qk(a,"path");b=d(b,"?");c=d(c,"#");uc.replaceState({},"",""+f+b+c)}}}function at(a,b){var c=Xs(!!b),d=Ns();d.data||(d.data={query:{},fragment:{}},c(d.data));var e={},f=d.data;f&&(Db(e,f.query),a&&Db(e,f.fragment));return e}
var Ys=function(a){try{var b=bt(a,3);if(b!==void 0){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=db(d[e+1]);c[f]=g}fb("TAGGING",6);return c}}catch(h){fb("TAGGING",8)}};function bt(a,b){if(a){var c;a:{for(var d=a,e=0;e<3;++e){var f=Qs.exec(d);if(f){c=f;break a}d=Pk(d)||""}c=void 0}var g=c;if(g&&g[1]==="1"){var h=g[3],m;a:{for(var n=g[2],p=0;p<b;++p)if(n===Ws(h,p)){m=!0;break a}m=!1}if(m)return h;fb("TAGGING",7)}}}
function ct(a,b,c,d,e){function f(p){p=$s(a,p);var q=p.charAt(p.length-1);p&&q!=="&"&&(p+="&");return p+n}d=d===void 0?!1:d;e=e===void 0?!1:e;var g=Us(c);if(!g)return"";var h=g.query||"",m=g.fragment||"",n=a+"="+b;d?m.substring(1).length!==0&&e||(m="#"+f(m.substring(1))):h="?"+f(h.substring(1));return""+g.uj+h+m}
function dt(a,b){function c(n,p,q){var r;a:{for(var t in n)if(n.hasOwnProperty(t)){r=!0;break a}r=!1}if(r){var u,v=[],w;for(w in n)if(n.hasOwnProperty(w)){var y=n[w];y!==void 0&&y===y&&y!==null&&y.toString()!=="[object Object]"&&(v.push(w),v.push(cb(String(y))))}var A=v.join("*");u=["1",Ws(A),A].join("*");d?(Ra(3)||Ra(1)||!p)&&et("_gl",u,a,p,q):ft("_gl",u,a,p,q)}}var d=(a.tagName||"").toUpperCase()==="FORM",e=Ps(b,1,d),f=Ps(b,2,d),g=Ps(b,4,d),h=Ps(b,3,d);c(e,!1,!1);c(f,!0,!1);Ra(1)&&c(g,!0,!0);for(var m in h)h.hasOwnProperty(m)&&
gt(m,h[m],a)}function gt(a,b,c){c.tagName.toLowerCase()==="a"?ft(a,b,c):c.tagName.toLowerCase()==="form"&&et(a,b,c)}function ft(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f;if(f=c.href){var g;if(!(g=!Ra(4)||d)){var h=x.location.href,m=Us(c.href),n=Us(h);g=!(m&&n&&m.uj===n.uj&&m.query===n.query&&m.fragment)}f=g}if(f){var p=ct(a,b,c.href,d,e);kc.test(p)&&(c.href=p)}}
function et(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;if(c){var f=c.getAttribute("action")||"";if(f){var g=(c.method||"").toLowerCase();if(g!=="get"||d){if(g==="get"||g==="post"){var h=ct(a,b,f,d,e);kc.test(h)&&(c.action=h)}}else{for(var m=c.childNodes||[],n=!1,p=0;p<m.length;p++){var q=m[p];if(q.name===a){q.setAttribute("value",b);n=!0;break}}if(!n){var r=z.createElement("input");r.setAttribute("type","hidden");r.setAttribute("name",a);r.setAttribute("value",b);c.appendChild(r)}}}}}
function Ls(a){try{var b;a:{for(var c=a,d=100;c&&d>0;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;f!=="http:"&&f!=="https:"||dt(e,e.hostname)}}catch(g){}}function Ms(a){try{var b=a.getAttribute("action");if(b){var c=Qk(Wk(b),"host");dt(a,c)}}catch(d){}}function ht(a,b,c,d){Ks();var e=c==="fragment"?2:1;d=!!d;Os(a,b,e,d,!1);e===2&&fb("TAGGING",23);d&&fb("TAGGING",24)}
function it(a,b){Ks();Os(a,[Sk(x.location,"host",!0)],b,!0,!0)}function jt(){var a=z.location.hostname,b=Rs.exec(z.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e=g==="s"?Pk(f[2])||"":Pk(g)||""}else if(d){if(d.indexOf("xn--")===0)return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var h=a.replace(Ss,""),m=e.replace(Ss,""),n;if(!(n=h===m)){var p="."+m;n=h.length>=p.length&&h.substring(h.length-p.length,h.length)===p}return n}
function kt(a,b){return a===!1?!1:a||b||jt()};var lt=["1"],mt={},nt={};function ot(a,b){b=b===void 0?!0:b;var c=pt(a.prefix);if(mt[c])qt(a);else if(rt(c,a.path,a.domain)){var d=nt[pt(a.prefix)]||{id:void 0,zh:void 0};b&&st(a,d.id,d.zh);qt(a)}else{var e=Yk("auiddc");if(e)fb("TAGGING",17),mt[c]=e;else if(b){var f=pt(a.prefix),g=Hs();tt(f,g,a);rt(c,a.path,a.domain);qt(a,!0)}}}
function qt(a,b){if((b===void 0?0:b)&&bs(Yr)){var c=Ur(!1);c.error!==0?fb("TAGGING",38):c.value?"gcl_ctr"in c.value?(delete c.value.gcl_ctr,Vr(c)!==0&&fb("TAGGING",41)):fb("TAGGING",40):fb("TAGGING",39)}if(fs(Yr)&&cs([Yr])[Yr.sb]===-1){for(var d={},e=(d[Yr.sb]=0,d),f=l($r),g=f.next();!g.done;g=f.next()){var h=g.value;h!==Yr&&fs(h)&&(e[h.sb]=0)}ds(e,a)}}
function st(a,b,c){var d=pt(a.prefix),e=mt[d];if(e){var f=e.split(".");if(f.length===2){var g=Number(f[1])||0;if(g){var h=e;b&&(h=e+"."+b+"."+(c?c:Math.floor(Ab()/1E3)));tt(d,h,a,g*1E3)}}}}function tt(a,b,c,d){var e;e=["1",Fs(c.domain,c.path),b].join(".");var f=Rr(c,d);f.Ec=ut();As(a,e,f)}function rt(a,b,c){var d=Is(a,b,c,lt,ut());if(!d)return!1;vt(a,d);return!0}
function vt(a,b){var c=b.split(".");c.length===5?(mt[a]=c.slice(0,2).join("."),nt[a]={id:c.slice(2,4).join("."),zh:Number(c[4])||0}):c.length===3?nt[a]={id:c.slice(0,2).join("."),zh:Number(c[2])||0}:mt[a]=b}function pt(a){return(a||"_gcl")+"_au"}function wt(a){function b(){cn(c)&&a()}var c=ut();jn(function(){b();cn(c)||kn(b,c)},c)}
function xt(a){var b=at(!0),c=pt(a.prefix);wt(function(){var d=b[c];if(d){vt(c,d);var e=Number(mt[c].split(".")[1])*1E3;if(e){fb("TAGGING",16);var f=Rr(a,e);f.Ec=ut();var g=["1",Fs(a.domain,a.path),d].join(".");As(c,g,f)}}})}function zt(a,b,c,d,e){e=e||{};var f=function(){var g={},h=Is(a,e.path,e.domain,lt,ut());h&&(g[a]=h);return g};wt(function(){ht(f,b,c,d)})}function ut(){return["ad_storage","ad_user_data"]};function At(a){for(var b=[],c=z.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({Fj:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,h){return h.timestamp-g.timestamp});return b}
function Bt(a,b){var c=At(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!(f[0]!=="1"||b&&f.length<3||!b&&f.length!==3)&&Number(f[1])){d[c[e].Fj]||(d[c[e].Fj]=[]);var g={version:f[0],timestamp:Number(f[1])*1E3,gclid:f[2]};b&&f.length>3&&(g.labels=f.slice(3));d[c[e].Fj].push(g)}}return d};var Ct={},Dt=(Ct.k={ba:/^[\w-]+$/},Ct.b={ba:/^[\w-]+$/,zj:!0},Ct.i={ba:/^[1-9]\d*$/},Ct.h={ba:/^\d+$/},Ct.t={ba:/^[1-9]\d*$/},Ct.d={ba:/^[A-Za-z0-9_-]+$/},Ct.j={ba:/^\d+$/},Ct.u={ba:/^[1-9]\d*$/},Ct.l={ba:/^[01]$/},Ct.o={ba:/^[1-9]\d*$/},Ct.g={ba:/^[01]$/},Ct.s={ba:/^.+$/},Ct);var Et={},It=(Et[5]={Gh:{2:Ft},nj:"2",ph:["k","i","b","u"]},Et[4]={Gh:{2:Ft,GCL:Gt},nj:"2",ph:["k","i","b"]},Et[2]={Gh:{GS2:Ft,GS1:Ht},nj:"GS2",ph:"sogtjlhd".split("")},Et);function Jt(a,b,c){var d=It[b];if(d){var e=a.split(".")[0];c==null||c(e);if(e){var f=d.Gh[e];if(f)return f(a,b)}}}
function Ft(a,b){var c=a.split(".");if(c.length===3){var d=c[2];if(d.indexOf("$")===-1&&d.indexOf("%24")!==-1)try{d=decodeURIComponent(d)}catch(t){}var e={},f=It[b];if(f){for(var g=f.ph,h=l(d.split("$")),m=h.next();!m.done;m=h.next()){var n=m.value,p=n[0];if(g.indexOf(p)!==-1)try{var q=decodeURIComponent(n.substring(1)),r=Dt[p];r&&(r.zj?(e[p]=e[p]||[],e[p].push(q)):e[p]=q)}catch(t){}}return e}}}function Kt(a,b,c){var d=It[b];if(d)return[d.nj,c||"1",Lt(a,b)].join(".")}
function Lt(a,b){var c=It[b];if(c){for(var d=[],e=l(c.ph),f=e.next();!f.done;f=e.next()){var g=f.value,h=Dt[g];if(h){var m=a[g];if(m!==void 0)if(h.zj&&Array.isArray(m))for(var n=l(m),p=n.next();!p.done;p=n.next())d.push(encodeURIComponent(""+g+p.value));else d.push(encodeURIComponent(""+g+m))}}return d.join("$")}}function Gt(a){var b=a.split(".");b.shift();var c=b.shift(),d=b.shift(),e={};return e.k=d,e.i=c,e.b=b,e}
function Ht(a){var b=a.split(".").slice(2);if(!(b.length<5||b.length>7)){var c={};return c.s=b[0],c.o=b[1],c.g=b[2],c.t=b[3],c.j=b[4],c.l=b[5],c.h=b[6],c}};var Mt=new Map([[5,"ad_storage"],[4,["ad_storage","ad_user_data"]],[2,"analytics_storage"]]);function Nt(a,b,c){if(It[b]){for(var d=[],e=ps(a,void 0,void 0,Mt.get(b)),f=l(e),g=f.next();!g.done;g=f.next()){var h=Jt(g.value,b,c);h&&d.push(Ot(h))}return d}}function Pt(a,b,c,d,e){d=d||{};var f=Fs(d.domain,d.path),g=Kt(b,c,f);if(!g)return 1;var h=Rr(d,e,void 0,Mt.get(c));return As(a,g,h)}function Qt(a,b){var c=b.ba;return typeof c==="function"?c(a):c.test(a)}
function Ot(a){for(var b=l(Object.keys(a)),c=b.next(),d={};!c.done;d={Tf:void 0},c=b.next()){var e=c.value,f=a[e];d.Tf=Dt[e];d.Tf?d.Tf.zj?a[e]=Array.isArray(f)?f.filter(function(g){return function(h){return Qt(h,g.Tf)}}(d)):void 0:typeof f==="string"&&Qt(f,d.Tf)||(a[e]=void 0):a[e]=void 0}return a};var Rt=function(){this.value=0};Rt.prototype.set=function(a){return this.value|=1<<a};var St=function(a,b){b<=0||(a.value|=1<<b-1)};Rt.prototype.get=function(){return this.value};Rt.prototype.clear=function(a){this.value&=~(1<<a)};Rt.prototype.clearAll=function(){this.value=0};Rt.prototype.equals=function(a){return this.value===a.value};function Tt(a){if(a)try{return new Uint8Array(atob(a.replace(/-/g,"+").replace(/_/g,"/")).split("").map(function(b){return b.charCodeAt(0)}))}catch(b){}}function Ut(a,b){var c=0,d=0,e,f=b;do{if(f>=a.length)return;e=a[f++];c|=(e&127)<<d;d+=7}while(e&128);return[c,f]};function Vt(){var a=String,b=x.location.hostname,c=x.location.pathname,d=b=Ob(b);d.split(".").length>2&&(d=d.replace(/^(www[0-9]*|web|ftp|wap|home|m|w|amp|mobile)\./,""));b=d;c=Ob(c);var e=c.split(";")[0];e=e.replace(/\/(ar|slp|web|index)?\/?$/,"");return a(gs((""+b+e).toLowerCase()))};var Wt={},Xt=(Wt.gclid=!0,Wt.dclid=!0,Wt.gbraid=!0,Wt.wbraid=!0,Wt),Yt=/^\w+$/,Zt=/^[\w-]+$/,$t={},au=($t.aw="_aw",$t.dc="_dc",$t.gf="_gf",$t.gp="_gp",$t.gs="_gs",$t.ha="_ha",$t.ag="_ag",$t.gb="_gb",$t),bu=/^(?:www\.)?google(?:\.com?)?(?:\.[a-z]{2}t?)?$/,cu=/^www\.googleadservices\.com$/;function du(){return["ad_storage","ad_user_data"]}function eu(a){return!Ra(7)||cn(a)}function fu(a,b){function c(){var d=eu(b);d&&a();return d}jn(function(){c()||kn(c,b)},b)}
function gu(a){return hu(a).map(function(b){return b.gclid})}function iu(a){return ju(a).filter(function(b){return b.gclid}).map(function(b){return b.gclid})}function ju(a){var b=ku(a.prefix),c=lu("gb",b),d=lu("ag",b);if(!d||!c)return[];var e=function(h){return function(m){m.type=h;return m}},f=hu(c).map(e("gb")),g=mu(d).map(e("ag"));return f.concat(g).sort(function(h,m){return m.timestamp-h.timestamp})}
function nu(a,b,c,d,e,f){var g=pb(a,function(h){return h.gclid===c});g?(g.timestamp<d&&(g.timestamp=d,g.Fd=f),g.labels=ou(g.labels||[],e||[])):a.push({version:b,gclid:c,timestamp:d,labels:e,Fd:f})}function mu(a){for(var b=Nt(a,5)||[],c=[],d=l(b),e=d.next();!e.done;e=d.next()){var f=e.value,g=f,h=pu(f);h&&nu(c,"2",g.k,h,g.b||[],f.u)}return c.sort(function(m,n){return n.timestamp-m.timestamp})}
function hu(a){for(var b=[],c=ps(a,z.cookie,void 0,du()),d=l(c),e=d.next();!e.done;e=d.next()){var f=qu(e.value);if(f!=null){var g=f;nu(b,g.version,g.gclid,g.timestamp,g.labels)}}b.sort(function(h,m){return m.timestamp-h.timestamp});return ru(b)}function su(a,b){for(var c=[],d=l(a),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)||c.push(f)}for(var g=l(b),h=g.next();!h.done;h=g.next()){var m=h.value;c.includes(m)||c.push(m)}return c}
function tu(a,b,c){c=c===void 0?!1:c;for(var d,e,f=l(a),g=f.next();!g.done;g=f.next()){var h=g.value;if(h.gclid===b.gclid){d=h;break}h.Ka&&b.Ka&&h.Ka.equals(b.Ka)&&(e=h)}if(d){var m,n,p=(m=d.Ka)!=null?m:new Rt,q=(n=b.Ka)!=null?n:new Rt;p.value|=q.value;d.Ka=p;d.timestamp<b.timestamp&&(d.timestamp=b.timestamp,d.Fd=b.Fd);d.labels=su(d.labels||[],b.labels||[]);d.Db=su(d.Db||[],b.Db||[])}else c&&e?ma(Object,"assign").call(Object,e,b):a.push(b)}
function uu(a){if(!a)return new Rt;var b=new Rt;if(a===1)return St(b,2),St(b,3),b;St(b,a);return b}
function vu(){var a=Wr("gclid");if(!a||a.error||!a.value||typeof a.value!=="object")return null;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return null;var c=b.value,d=c.value;if(!d||!d.match(Zt))return null;var e=c.linkDecorationSource,f=c.linkDecorationSources,g=new Rt;typeof e==="number"?g=uu(e):typeof f==="number"&&(g.value=f);return{version:"",gclid:d,timestamp:Number(c.creationTimeMs)||0,labels:[],Ka:g,Db:[2]}}catch(h){return null}}
function wu(){var a=Wr("gcl_aw");if(a.error!==0)return null;try{return a.value.reduce(function(b,c){if(!c.value||typeof c.value!=="object")return b;var d=c.value,e=d.value;if(!e||!e.match(Zt))return b;var f=new Rt,g=d.linkDecorationSources;typeof g==="number"&&(f.value=g);b.push({version:"",gclid:e,timestamp:Number(d.creationTimeMs)||0,expires:Number(c.expires)||0,labels:[],Ka:f,Db:[2]});return b},[])}catch(b){return null}}
function xu(a){for(var b=[],c=ps(a,z.cookie,void 0,du()),d=l(c),e=d.next();!e.done;e=d.next()){var f=qu(e.value);f!=null&&(f.Fd=void 0,f.Ka=new Rt,f.Db=[1],tu(b,f))}var g=vu();g&&(g.Fd=void 0,g.Db=g.Db||[2],tu(b,g));if(Ra(13)){var h=wu();if(h)for(var m=l(h),n=m.next();!n.done;n=m.next()){var p=n.value;p.Fd=void 0;p.Db=p.Db||[2];tu(b,p)}}b.sort(function(q,r){return r.timestamp-q.timestamp});return ru(b)}
function ou(a,b){if(!a.length)return b;if(!b.length)return a;var c={};return a.concat(b).filter(function(d){return c.hasOwnProperty(d)?!1:c[d]=!0})}function ku(a){return a&&typeof a==="string"&&a.match(Yt)?a:"_gcl"}function yu(a,b){if(a){var c={value:a,Ka:new Rt};St(c.Ka,b);return c}}
function zu(a,b,c){var d=Wk(a),e=Qk(d,"query",!1,void 0,"gclsrc"),f=yu(Qk(d,"query",!1,void 0,"gclid"),c?4:2);if(b&&(!f||!e)){var g=d.hash.replace("#","");f||(f=yu(Nk(g,"gclid",!1),3));e||(e=Nk(g,"gclsrc",!1))}return!f||e!==void 0&&e!=="aw"&&e!=="aw.ds"?[]:[f]}
function Au(a,b){var c=Wk(a),d=Qk(c,"query",!1,void 0,"gclid"),e=Qk(c,"query",!1,void 0,"gclsrc"),f=Qk(c,"query",!1,void 0,"wbraid");f=Mb(f);var g=Qk(c,"query",!1,void 0,"gbraid"),h=Qk(c,"query",!1,void 0,"gad_source"),m=Qk(c,"query",!1,void 0,"dclid");if(b&&!(d&&e&&f&&g)){var n=c.hash.replace("#","");d=d||Nk(n,"gclid",!1);e=e||Nk(n,"gclsrc",!1);f=f||Nk(n,"wbraid",!1);g=g||Nk(n,"gbraid",!1);h=h||Nk(n,"gad_source",!1)}return Bu(d,e,m,f,g,h)}function Cu(){return Au(x.location.href,!0)}
function Bu(a,b,c,d,e,f){var g={},h=function(m,n){g[n]||(g[n]=[]);g[n].push(m)};g.gclid=a;g.gclsrc=b;g.dclid=c;if(a!==void 0&&a.match(Zt))switch(b){case void 0:h(a,"aw");break;case "aw.ds":h(a,"aw");h(a,"dc");break;case "ds":h(a,"dc");break;case "3p.ds":h(a,"dc");break;case "gf":h(a,"gf");break;case "ha":h(a,"ha")}c&&h(c,"dc");d!==void 0&&Zt.test(d)&&(g.wbraid=d,h(d,"gb"));e!==void 0&&Zt.test(e)&&(g.gbraid=e,h(e,"ag"));f!==void 0&&Zt.test(f)&&(g.gad_source=f,h(f,"gs"));return g}
function Du(a){for(var b=Cu(),c=!0,d=l(Object.keys(b)),e=d.next();!e.done;e=d.next())if(b[e.value]!==void 0){c=!1;break}c&&(b=Au(x.document.referrer,!1),b.gad_source=void 0);Eu(b,!1,a)}
function Fu(a){Du(a);var b=zu(x.location.href,!0,!1);b.length||(b=zu(x.document.referrer,!1,!0));a=a||{};Gu(a);if(b.length){var c=b[0],d=Ab(),e=Rr(a,d,!0),f=du(),g=function(){eu(f)&&e.expires!==void 0&&Tr("gclid",{value:{value:c.value,creationTimeMs:d,linkDecorationSources:c.Ka.get()},expires:Number(e.expires)})};jn(function(){g();eu(f)||kn(g,f)},f)}}
function Gu(a){var b;if(b=Ra(14)){var c=Hu();b=bu.test(c)||cu.test(c)||Iu()}if(b){var d;a:{for(var e=Wk(x.location.href),f=Ok(Qk(e,"query")),g=l(Object.keys(f)),h=g.next();!h.done;h=g.next()){var m=h.value;if(!Xt[m]){var n=f[m][0]||"",p;if(!n||n.length<50||n.length>200)p=!1;else{var q=Tt(n),r;if(q)c:{var t=q;if(t&&t.length!==0){var u=0;try{for(;u<t.length;){var v=Ut(t,u);if(v===void 0)break;var w=l(v),y=w.next().value,A=w.next().value,C=y,D=A,G=C&7;if(C>>3===16382){if(G!==0)break;var I=Ut(t,D);if(I===
void 0)break;r=l(I).next().value===1;break c}var M;d:{var T=void 0,da=t,O=D;switch(G){case 0:M=(T=Ut(da,O))==null?void 0:T[1];break d;case 1:M=O+8;break d;case 2:var V=Ut(da,O);if(V===void 0)break;var ia=l(V),ka=ia.next().value;M=ia.next().value+ka;break d;case 5:M=O+4;break d}M=void 0}if(M===void 0||M>t.length)break;u=M}}catch(Y){}}r=!1}else r=!1;p=r}if(p){d=n;break a}}}d=void 0}var X=d;X&&Ju(X,7,a)}}
function Ju(a,b,c){c=c||{};var d=Ab(),e=Rr(c,d,!0),f=du(),g=function(){if(eu(f)&&e.expires!==void 0){var h=wu()||[];tu(h,{version:"",gclid:a,timestamp:d,expires:Number(e.expires),Ka:uu(b)},!0);Tr("gcl_aw",h.map(function(m){return{value:{value:m.gclid,creationTimeMs:m.timestamp,linkDecorationSources:m.Ka?m.Ka.get():0},expires:Number(m.expires)}}))}};jn(function(){eu(f)?g():kn(g,f)},f)}
function Eu(a,b,c,d,e){c=c||{};e=e||[];var f=ku(c.prefix),g=d||Ab(),h=Math.round(g/1E3),m=du(),n=!1,p=!1,q=function(){if(eu(m)){var r=Rr(c,g,!0);r.Ec=m;for(var t=function(T,da){var O=lu(T,f);O&&(As(O,da,r),T!=="gb"&&(n=!0))},u=function(T){var da=["GCL",h,T];e.length>0&&da.push(e.join("."));return da.join(".")},v=l(["aw","dc","gf","ha","gp"]),w=v.next();!w.done;w=v.next()){var y=w.value;a[y]&&t(y,u(a[y][0]))}if(!n&&a.gb){var A=a.gb[0],C=lu("gb",f);!b&&hu(C).some(function(T){return T.gclid===A&&T.labels&&
T.labels.length>0})||t("gb",u(A))}}if(!p&&a.gbraid&&eu("ad_storage")&&(p=!0,!n)){var D=a.gbraid,G=lu("ag",f);if(b||!mu(G).some(function(T){return T.gclid===D&&T.labels&&T.labels.length>0})){var I={},M=(I.k=D,I.i=""+h,I.b=e,I);Pt(G,M,5,c,g)}}Ku(a,f,g,c)};jn(function(){q();eu(m)||kn(q,m)},m)}
function Ku(a,b,c,d){if(a.gad_source!==void 0&&eu("ad_storage")){var e=bd();if(e!=="r"&&e!=="h"){var f=a.gad_source,g=lu("gs",b);if(g){var h=Math.floor((Ab()-(ad()||0))/1E3),m,n=Vt(),p={};m=(p.k=f,p.i=""+h,p.u=n,p);Pt(g,m,5,d,c)}}}}
function Lu(a,b){var c=at(!0);fu(function(){for(var d=ku(b.prefix),e=0;e<a.length;++e){var f=a[e];if(au[f]!==void 0){var g=lu(f,d),h=c[g];if(h){var m=Math.min(Mu(h),Ab()),n;b:{for(var p=m,q=ps(g,z.cookie,void 0,du()),r=0;r<q.length;++r)if(Mu(q[r])>p){n=!0;break b}n=!1}if(!n){var t=Rr(b,m,!0);t.Ec=du();As(g,h,t)}}}}Eu(Bu(c.gclid,c.gclsrc),!1,b)},du())}
function Nu(a){var b=["ag"],c=at(!0),d=ku(a.prefix);fu(function(){for(var e=0;e<b.length;++e){var f=lu(b[e],d);if(f){var g=c[f];if(g){var h=Jt(g,5);if(h){var m=pu(h);m||(m=Ab());var n;a:{for(var p=m,q=Nt(f,5),r=0;r<q.length;++r)if(pu(q[r])>p){n=!0;break a}n=!1}if(n)break;h.i=""+Math.round(m/1E3);Pt(f,h,5,a,m)}}}}},["ad_storage"])}function lu(a,b){var c=au[a];if(c!==void 0)return b+c}function Mu(a){return Ou(a.split(".")).length!==0?(Number(a.split(".")[1])||0)*1E3:0}
function pu(a){return a?(Number(a.i)||0)*1E3:0}function qu(a){var b=Ou(a.split("."));return b.length===0?null:{version:b[0],gclid:b[2],timestamp:(Number(b[1])||0)*1E3,labels:b.slice(3)}}function Ou(a){return a.length<3||a[0]!=="GCL"&&a[0]!=="1"||!/^\d+$/.test(a[1])||!Zt.test(a[2])?[]:a}
function Pu(a,b,c,d,e){if(Array.isArray(b)&&ms(x)){var f=ku(e),g=function(){for(var h={},m=0;m<a.length;++m){var n=lu(a[m],f);if(n){var p=ps(n,z.cookie,void 0,du());p.length&&(h[n]=p.sort()[p.length-1])}}return h};fu(function(){ht(g,b,c,d)},du())}}
function Qu(a,b,c,d){if(Array.isArray(a)&&ms(x)){var e=["ag"],f=ku(d),g=function(){for(var h={},m=0;m<e.length;++m){var n=lu(e[m],f);if(!n)return{};var p=Nt(n,5);if(p.length){var q=p.sort(function(r,t){return pu(t)-pu(r)})[0];h[n]=Kt(q,5)}}return h};fu(function(){ht(g,a,b,c)},["ad_storage"])}}function ru(a){return a.filter(function(b){return Zt.test(b.gclid)})}
function Ru(a,b){if(ms(x)){for(var c=ku(b.prefix),d={},e=0;e<a.length;e++)au[a[e]]&&(d[a[e]]=au[a[e]]);fu(function(){tb(d,function(f,g){var h=ps(c+g,z.cookie,void 0,du());h.sort(function(t,u){return Mu(u)-Mu(t)});if(h.length){var m=h[0],n=Mu(m),p=Ou(m.split(".")).length!==0?m.split(".").slice(3):[],q={},r;r=Ou(m.split(".")).length!==0?m.split(".")[2]:void 0;q[f]=[r];Eu(q,!0,b,n,p)}})},du())}}
function Su(a){var b=["ag"],c=["gbraid"];fu(function(){for(var d=ku(a.prefix),e=0;e<b.length;++e){var f=lu(b[e],d);if(!f)break;var g=Nt(f,5);if(g.length){var h=g.sort(function(q,r){return pu(r)-pu(q)})[0],m=pu(h),n=h.b,p={};p[c[e]]=h.k;Eu(p,!0,a,m,n)}}},["ad_storage"])}function Tu(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
function Uu(a){function b(h,m,n){n&&(h[m]=n)}if(fn()){var c=Cu(),d;a.includes("gad_source")&&(d=c.gad_source!==void 0?c.gad_source:at(!1)._gs);if(Tu(c,a)||d){var e={};b(e,"gclid",c.gclid);b(e,"dclid",c.dclid);b(e,"gclsrc",c.gclsrc);b(e,"wbraid",c.wbraid);b(e,"gbraid",c.gbraid);it(function(){return e},3);var f={},g=(f._up="1",f);b(g,"_gs",d);it(function(){return g},1)}}}function Iu(){var a=Wk(x.location.href);return Qk(a,"query",!1,void 0,"gad_source")}
function Vu(a){if(!Ra(1))return null;var b=at(!0).gad_source;if(b!=null)return x.location.hash="",b;if(Ra(2)){b=Iu();if(b!=null)return b;var c=Cu();if(Tu(c,a))return"0"}return null}function Wu(a){var b=Vu(a);b!=null&&it(function(){var c={};return c.gad_source=b,c},4)}function Xu(a,b,c){var d=[];if(b.length===0)return d;for(var e={},f=0;f<b.length;f++){var g=b[f],h=g.type?g.type:"gcl";(g.labels||[]).indexOf(c)===-1?(a.push(0),e[h]||d.push(g)):a.push(1);e[h]=!0}return d}
function Yu(a,b,c,d){var e=[];c=c||{};if(!eu(du()))return e;var f=hu(a),g=Xu(e,f,b);if(g.length&&!d)for(var h=l(g),m=h.next();!m.done;m=h.next()){var n=m.value,p=n.timestamp,q=[n.version,Math.round(p/1E3),n.gclid].concat(n.labels||[],[b]).join("."),r=Rr(c,p,!0);r.Ec=du();As(a,q,r)}return e}
function Zu(a,b){var c=[];b=b||{};var d=ju(b),e=Xu(c,d,a);if(e.length)for(var f=l(e),g=f.next();!g.done;g=f.next()){var h=g.value,m=ku(b.prefix),n=lu(h.type,m);if(!n)break;var p=h,q=p.version,r=p.gclid,t=p.labels,u=p.timestamp,v=Math.round(u/1E3);if(h.type==="ag"){var w={},y=(w.k=r,w.i=""+v,w.b=(t||[]).concat([a]),w);Pt(n,y,5,b,u)}else if(h.type==="gb"){var A=[q,v,r].concat(t||[],[a]).join("."),C=Rr(b,u,!0);C.Ec=du();As(n,A,C)}}return c}
function $u(a,b){var c=ku(b),d=lu(a,c);if(!d)return 0;var e;e=a==="ag"?mu(d):hu(d);for(var f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function av(a){for(var b=0,c=l(Object.keys(a)),d=c.next();!d.done;d=c.next())for(var e=a[d.value],f=0;f<e.length;f++)b=Math.max(b,Number(e[f].timestamp));return b}function bv(a){var b=Math.max($u("aw",a),av(eu(du())?Bt():{})),c=Math.max($u("gb",a),av(eu(du())?Bt("_gac_gb",!0):{}));c=Math.max(c,$u("ag",a));return c>b}
function Hu(){return z.referrer?Qk(Wk(z.referrer),"host"):""};function qv(){return Ap("dedupe_gclid",function(){return Hs()})};var rv=/^(www\.)?google(\.com?)?(\.[a-z]{2}t?)?$/,sv=/^www.googleadservices.com$/;function tv(a){a||(a=uv());return a.Pq?!1:a.Ip||a.Kp||a.Np||a.Lp||a.Yf||a.tp||a.Mp||a.yp?!0:!1}function uv(){var a={},b=at(!0);a.Pq=!!b._up;var c=Cu();a.Ip=c.aw!==void 0;a.Kp=c.dc!==void 0;a.Np=c.wbraid!==void 0;a.Lp=c.gbraid!==void 0;a.Mp=c.gclsrc==="aw.ds";a.Yf=ev().Yf;var d=z.referrer?Qk(Wk(z.referrer),"host"):"";a.yp=rv.test(d);a.tp=sv.test(d);return a};function vv(a){var b=window,c=b.webkit;delete b.webkit;a(b.webkit);b.webkit=c}function wv(a){var b={action:"gcl_setup"};if("CWVWebViewMessage"in a.messageHandlers)return a.messageHandlers.CWVWebViewMessage.postMessage({command:"awb",payload:b}),!0;var c=a.messageHandlers.awb;return c?(c.postMessage(b),!0):!1};function xv(){return["ad_storage","ad_user_data"]}function yv(a){if(E(38)&&!zn(vn.Z.pl)&&"webkit"in window&&window.webkit.messageHandlers){var b=function(){try{vv(function(c){c&&("CWVWebViewMessage"in c.messageHandlers||"awb"in c.messageHandlers)&&(yn(vn.Z.pl,function(d){d.gclid&&Ju(d.gclid,5,a)}),wv(c)||L(178))})}catch(c){L(177)}};jn(function(){eu(xv())?b():kn(b,xv())},xv())}};var zv=["https://www.google.com","https://www.youtube.com","https://m.youtube.com"];function Av(a){a.data.action==="gcl_transfer"&&a.data.gadSource?yn(vn.Z.Nf,{gadSource:a.data.gadSource}):L(173)}
function Bv(a,b){if(E(a)){if(zn(vn.Z.Nf))return L(176),vn.Z.Nf;if(zn(vn.Z.sl))return L(170),vn.Z.Nf;var c=Nl();if(!c)L(171);else if(c.opener){var d=function(g){if(zv.includes(g.origin)){a===119?Av(g):a===200&&(Av(g),g.data.gclid&&Ju(String(g.data.gclid),6,b));var h;(h=g.stopImmediatePropagation)==null||h.call(g);Xq(c,"message",d)}else L(172)};if(Wq(c,"message",d)){yn(vn.Z.sl,!0);for(var e=l(zv),f=e.next();!f.done;f=e.next())c.opener.postMessage({action:"gcl_setup"},f.value);L(174);return vn.Z.Nf}L(175)}}}
;var Cv=function(){this.C=this.gppString=void 0};Cv.prototype.reset=function(){this.C=this.gppString=void 0};var Dv=new Cv;var Ev=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),Fv=/^~?[\w-]+(?:\.~?[\w-]+)*$/,Gv=/^\d+\.fls\.doubleclick\.net$/,Hv=/;gac=([^;?]+)/,Iv=/;gacgb=([^;?]+)/;
function Jv(a,b){if(Gv.test(z.location.host)){var c=z.location.href.match(b);return c&&c.length===2&&c[1].match(Ev)?Pk(c[1])||"":""}for(var d=[],e=l(Object.keys(a)),f=e.next();!f.done;f=e.next()){for(var g=f.value,h=[],m=a[g],n=0;n<m.length;n++)h.push(m[n].gclid);d.push(g+":"+h.join(","))}return d.length>0?d.join(";"):""}
function Kv(a,b,c){for(var d=eu(du())?Bt("_gac_gb",!0):{},e=[],f=!1,g=l(Object.keys(d)),h=g.next();!h.done;h=g.next()){var m=h.value,n=Yu("_gac_gb_"+m,a,b,c);f=f||n.length!==0&&n.some(function(p){return p===1});e.push(m+":"+n.join(","))}return{rp:f?e.join(";"):"",qp:Jv(d,Iv)}}function Lv(a){var b=z.location.href.match(new RegExp(";"+a+"=([^;?]+)"));return b&&b.length===2&&b[1].match(Fv)?b[1]:void 0}
function Mv(a){var b={},c,d,e;Gv.test(z.location.host)&&(c=Lv("gclgs"),d=Lv("gclst"),e=Lv("gcllp"));if(c&&d&&e)b.sh=c,b.uh=d,b.th=e;else{var f=Ab(),g=mu((a||"_gcl")+"_gs"),h=g.map(function(p){return p.gclid}),m=g.map(function(p){return f-p.timestamp}),n=g.map(function(p){return p.Fd});h.length>0&&m.length>0&&n.length>0&&(b.sh=h.join("."),b.uh=m.join("."),b.th=n.join("."))}return b}
function Nv(a,b,c,d){d=d===void 0?!1:d;if(Gv.test(z.location.host)){var e=Lv(c);if(e){if(d){var f=new Rt;St(f,2);St(f,3);return e.split(".").map(function(h){return{gclid:h,Ka:f,Db:[1]}})}return e.split(".").map(function(h){return{gclid:h}})}}else{if(b==="gclid"){var g=(a||"_gcl")+"_aw";return d?xu(g):hu(g)}if(b==="wbraid")return hu((a||"_gcl")+"_gb");if(b==="braids")return ju({prefix:a})}return[]}function Ov(a){return Gv.test(z.location.host)?!(Lv("gclaw")||Lv("gac")):bv(a)}
function Pv(a,b,c){var d;d=c?Zu(a,b):Yu((b&&b.prefix||"_gcl")+"_gb",a,b);return d.length===0||d.every(function(e){return e===0})?"":d.join(".")};function Qv(){var a=x.__uspapi;if(lb(a)){var b="";try{a("getUSPData",1,function(c,d){if(d&&c){var e=c.uspString;e&&RegExp("^[\\da-zA-Z-]{1,20}$").test(e)&&(b=e)}})}catch(c){}return b}};function cw(a){var b=N(a.D,J.m.Lc),c=N(a.D,J.m.Kc);b&&!c?(a.eventName!==J.m.qa&&a.eventName!==J.m.Td&&L(131),a.isAborted=!0):!b&&c&&(L(132),a.isAborted=!0)}function dw(a){var b=P(J.m.U)?zp.pscdl:"denied";b!=null&&U(a,J.m.Fg,b)}function ew(a){var b=Ll(!0);U(a,J.m.Jc,b)}function fw(a){Lr()&&U(a,J.m.be,1)}
function Uv(){var a=z.title;if(a===void 0||a==="")return"";a=encodeURIComponent(a);for(var b=256;b>0&&Pk(a.substring(0,b))===void 0;)b--;return Pk(a.substring(0,b))||""}function gw(a){hw(a,Hp.Bf.Um,N(a.D,J.m.pb))}function hw(a,b,c){Tv(a,J.m.sd)||U(a,J.m.sd,{});Tv(a,J.m.sd)[b]=c}function iw(a){S(a,Q.A.Pf,Um.X.Fa)}function jw(a){var b=ib("GTAG_EVENT_FEATURE_CHANNEL");b&&(U(a,J.m.jf,b),gb())}function kw(a){var b=a.D.getMergedValues(J.m.sc);b&&a.mergeHitDataForKey(J.m.sc,b)}
function lw(a,b){b=b===void 0?!1:b;var c=R(a,Q.A.Of);if(c)if(c.indexOf(a.target.destinationId)<0){if(S(a,Q.A.Ij,!1),b||!mw(a,"custom_event_accept_rules",!1))a.isAborted=!0}else S(a,Q.A.Ij,!0)}function nw(a){ll&&(Zn=!0,a.eventName===J.m.qa?fo(a.D,a.target.id):(R(a,Q.A.Ke)||(bo[a.target.id]=!0),Gp(R(a,Q.A.ib))))};function xw(a,b,c,d){var e=Ic(),f;if(e===1)a:{var g=ik;g=g.toLowerCase();for(var h="https://"+g,m="http://"+g,n=1,p=z.getElementsByTagName("script"),q=0;q<p.length&&q<100;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(r.indexOf(m)===0){f=3;break a}n===1&&r.indexOf(h)===0&&(n=2)}}f=n}else f=e;return(f===2||d||"http:"!==x.location.protocol?a:b)+c};function Jw(a){return{getDestinationId:function(){return a.target.destinationId},getEventName:function(){return a.eventName},setEventName:function(b){a.eventName=b},getHitData:function(b){return Tv(a,b)},setHitData:function(b,c){U(a,b,c)},setHitDataIfNotDefined:function(b,c){Tv(a,b)===void 0&&U(a,b,c)},copyToHitData:function(b,c){a.copyToHitData(b,c)},getMetadata:function(b){return R(a,b)},setMetadata:function(b,c){S(a,b,c)},isAborted:function(){return a.isAborted},abort:function(){a.isAborted=!0},
getFromEventContext:function(b){return N(a.D,b)},Bb:function(){return a},getHitKeys:function(){return Object.keys(a.C)},getMergedValues:function(b){return a.D.getMergedValues(b,3)},mergeHitDataForKey:function(b,c){return nd(c)?a.mergeHitDataForKey(b,c):!1}}};function Ow(a,b){return arguments.length===1?Pw("set",a):Pw("set",a,b)}function Qw(a,b){return arguments.length===1?Pw("config",a):Pw("config",a,b)}function Rw(a,b,c){c=c||{};c[J.m.md]=a;return Pw("event",b,c)}function Pw(){return arguments};var Tw=function(){this.messages=[];this.C=[]};Tw.prototype.enqueue=function(a,b,c){var d=this.messages.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;var e=ma(Object,"assign").call(Object,{},c,{eventId:b,priorityId:d,fromContainerExecution:!0}),f={message:a,notBeforeEventId:b,priorityId:d,messageContext:e};this.messages.push(f);for(var g=0;g<this.C.length;g++)try{this.C[g](f)}catch(h){}};Tw.prototype.listen=function(a){this.C.push(a)};
Tw.prototype.get=function(){for(var a={},b=0;b<this.messages.length;b++){var c=this.messages[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};Tw.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.messages.length;d++){var e=this.messages[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.messages=c;return b};function Uw(a,b,c){c.eventMetadata=c.eventMetadata||{};c.eventMetadata[Q.A.ib]=mg.canonicalContainerId;Vw().enqueue(a,b,c)}
function Ww(){var a=Xw;Vw().listen(a)}function Vw(){return Ap("mb",function(){return new Tw})};var Yw,Zw=!1;function $w(){Zw=!0;Yw=Yw||{}}function ax(a){Zw||$w();return Yw[a]};function bx(){var a=x.screen;return{width:a?a.width:0,height:a?a.height:0}}
function cx(a){if(z.hidden)return!0;var b=a.getBoundingClientRect();if(b.top===b.bottom||b.left===b.right||!x.getComputedStyle)return!0;var c=x.getComputedStyle(a,null);if(c.visibility==="hidden")return!0;for(var d=a,e=c;d;){if(e.display==="none")return!0;var f=e.opacity,g=e.filter;if(g){var h=g.indexOf("opacity(");h>=0&&(g=g.substring(h+8,g.indexOf(")",h)),g.charAt(g.length-1)==="%"&&(g=g.substring(0,g.length-1)),f=String(Math.min(Number(g),Number(f))))}if(f!==void 0&&Number(f)<=0)return!0;(d=d.parentElement)&&
(e=x.getComputedStyle(d,null))}return!1}var ig;var py=Number('')||5,qy=Number('')||50,ry=qb();
var ty=function(a,b){a&&(sy("sid",a.targetId,b),sy("cc",a.clientCount,b),sy("tl",a.totalLifeMs,b),sy("hc",a.heartbeatCount,b),sy("cl",a.clientLifeMs,b))},sy=function(a,b,c){b!=null&&c.push(a+"="+b)},uy=function(){var a=z.referrer;if(a){var b;return Qk(Wk(a),"host")===((b=x.location)==null?void 0:b.host)?1:2}return 0},vy="https://"+Ui(21,"www.googletagmanager.com")+"/a?",xy=function(){this.R=wy;this.N=0};xy.prototype.H=function(a,b,c,d){var e=uy(),f,
g=[];f=x===x.top&&e!==0&&b?(b==null?void 0:b.clientCount)>1?e===2?1:2:e===2?0:3:4;a&&sy("si",a.gg,g);sy("m",0,g);sy("iss",f,g);sy("if",c,g);ty(b,g);d&&sy("fm",encodeURIComponent(d.substring(0,qy)),g);this.P(g);};xy.prototype.C=function(a,b,c,d,e){var f=[];sy("m",1,f);sy("s",a,f);sy("po",uy(),f);b&&(sy("st",b.state,f),sy("si",b.gg,f),sy("sm",b.mg,f));ty(c,f);sy("c",d,f);e&&sy("fm",encodeURIComponent(e.substring(0,
qy)),f);this.P(f);};xy.prototype.P=function(a){a=a===void 0?[]:a;!kl||this.N>=py||(sy("pid",ry,a),sy("bc",++this.N,a),a.unshift("ctid="+mg.ctid+"&t=s"),this.R(""+vy+a.join("&")))};var yy=Number('')||500,zy=Number('')||5E3,Ay=Number('20')||10,By=Number('')||5E3;function Cy(a){return a.performance&&a.performance.now()||Date.now()}
var Dy=function(a,b){var c=x,d;var e=function(f,g,h){h=h===void 0?{km:function(){},lm:function(){},jm:function(){},onFailure:function(){}}:h;this.yo=f;this.C=g;this.N=h;this.fa=this.ma=this.heartbeatCount=this.wo=0;this.gh=!1;this.H={};this.id=String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()));this.state=0;this.gg=Cy(this.C);this.mg=Cy(this.C);this.R=10};e.prototype.init=function(){this.P(1);this.Da()};e.prototype.getState=function(){return{state:this.state,
gg:Math.round(Cy(this.C)-this.gg),mg:Math.round(Cy(this.C)-this.mg)}};e.prototype.P=function(f){this.state!==f&&(this.state=f,this.mg=Cy(this.C))};e.prototype.Hl=function(){return String(this.wo++)};e.prototype.Da=function(){var f=this;this.heartbeatCount++;this.Ua({type:0,clientId:this.id,requestId:this.Hl(),maxDelay:this.hh()},function(g){if(g.type===0){var h;if(((h=g.failure)==null?void 0:h.failureType)!=null)if(g.stats&&(f.stats=g.stats),f.fa++,g.isDead||f.fa>Ay){var m=g.isDead&&g.failure.failureType;
f.R=m||10;f.P(4);f.vo();var n,p;(p=(n=f.N).jm)==null||p.call(n,{failureType:m||10,data:g.failure.data})}else f.P(3),f.Ll();else{if(f.heartbeatCount>g.stats.heartbeatCount+Ay){f.heartbeatCount=g.stats.heartbeatCount;var q,r;(r=(q=f.N).onFailure)==null||r.call(q,{failureType:13})}f.stats=g.stats;var t=f.state;f.P(2);if(t!==2)if(f.gh){var u,v;(v=(u=f.N).lm)==null||v.call(u)}else{f.gh=!0;var w,y;(y=(w=f.N).km)==null||y.call(w)}f.fa=0;f.zo();f.Ll()}}})};e.prototype.hh=function(){return this.state===2?
zy:yy};e.prototype.Ll=function(){var f=this;this.C.setTimeout(function(){f.Da()},Math.max(0,this.hh()-(Cy(this.C)-this.ma)))};e.prototype.Do=function(f,g,h){var m=this;this.Ua({type:1,clientId:this.id,requestId:this.Hl(),command:f},function(n){if(n.type===1)if(n.result)g(n.result);else{var p,q,r,t={failureType:(r=(p=n.failure)==null?void 0:p.failureType)!=null?r:12,data:(q=n.failure)==null?void 0:q.data},u,v;(v=(u=m.N).onFailure)==null||v.call(u,t);h(t)}})};e.prototype.Ua=function(f,g){var h=this;
if(this.state===4)f.failure={failureType:this.R},g(f);else{var m=this.state!==2&&f.type!==0,n=f.requestId,p,q=this.C.setTimeout(function(){var t=h.H[n];t&&h.Kf(t,7)},(p=f.maxDelay)!=null?p:By),r={request:f,Bm:g,wm:m,aq:q};this.H[n]=r;m||this.sendRequest(r)}};e.prototype.sendRequest=function(f){this.ma=Cy(this.C);f.wm=!1;this.yo(f.request)};e.prototype.zo=function(){for(var f=l(Object.keys(this.H)),g=f.next();!g.done;g=f.next()){var h=this.H[g.value];h.wm&&this.sendRequest(h)}};e.prototype.vo=function(){for(var f=
l(Object.keys(this.H)),g=f.next();!g.done;g=f.next())this.Kf(this.H[g.value],this.R)};e.prototype.Kf=function(f,g){this.rb(f);var h=f.request;h.failure={failureType:g};f.Bm(h)};e.prototype.rb=function(f){delete this.H[f.request.requestId];this.C.clearTimeout(f.aq)};e.prototype.Gp=function(f){this.ma=Cy(this.C);var g=this.H[f.requestId];if(g)this.rb(g),g.Bm(f);else{var h,m;(m=(h=this.N).onFailure)==null||m.call(h,{failureType:14})}};d=new e(a,c,b);return d};var Ey;
var Fy=function(){Ey||(Ey=new xy);return Ey},wy=function(a){sn(un(Um.X.Oc),function(){Lc(a)})},Gy=function(a){var b=a.substring(0,a.indexOf("/_/service_worker"));return"&1p=1"+(b?"&path="+encodeURIComponent(b):"")},Hy=function(a){var b=a,c=Rj.Da;b?(b.charAt(b.length-1)!=="/"&&(b+="/"),a=b+c):a="https://www.googletagmanager.com/static/service_worker/"+c+"/";var d;try{d=new URL(a)}catch(e){return null}return d.protocol!=="https:"?null:d},Iy=function(a){var b=zn(vn.Z.Al);return b&&b[a]},Jy=function(a,
b,c,d,e){var f=this;this.H=d;this.R=this.P=!1;this.fa=null;this.initTime=c;this.C=15;this.N=this.Wo(a);x.setTimeout(function(){f.initialize()},1E3);Oc(function(){f.Rp(a,b,e)})};k=Jy.prototype;k.delegate=function(a,b,c){this.getState()!==2?(this.H.C(this.C,{state:this.getState(),gg:this.initTime,mg:Math.round(Ab())-this.initTime},void 0,a.commandType),c({failureType:this.C})):this.N.Do(a,b,c)};k.getState=function(){return this.N.getState().state};k.Rp=function(a,b,c){var d=x.location.origin,e=this,
f=Jc();try{var g=f.contentDocument.createElement("iframe"),h=a.pathname,m=h[h.length-1]==="/"?a.toString():a.toString()+"/",n=b?Gy(h):"",p;E(133)&&(p={sandbox:"allow-same-origin allow-scripts"});Jc(m+"sw_iframe.html?origin="+encodeURIComponent(d)+n+(c?"&e=1":""),void 0,p,void 0,g);var q=function(){f.contentDocument.body.appendChild(g);g.addEventListener("load",function(){e.fa=g.contentWindow;f.contentWindow.addEventListener("message",function(r){r.origin===a.origin&&e.N.Gp(r.data)});e.initialize()})};
f.contentDocument.readyState==="complete"?q():f.contentWindow.addEventListener("load",function(){q()})}catch(r){f.parentElement.removeChild(f),this.C=11,this.H.H(void 0,void 0,this.C,r.toString())}};k.Wo=function(a){var b=this,c=Dy(function(d){var e;(e=b.fa)==null||e.postMessage(d,a.origin)},{km:function(){b.P=!0;b.H.H(c.getState(),c.stats)},lm:function(){},jm:function(d){b.P?(b.C=(d==null?void 0:d.failureType)||10,b.H.C(b.C,c.getState(),c.stats,void 0,d==null?void 0:d.data)):(b.C=(d==null?void 0:
d.failureType)||4,b.H.H(c.getState(),c.stats,b.C,d==null?void 0:d.data))},onFailure:function(d){b.C=d.failureType;b.H.C(b.C,c.getState(),c.stats,d.command,d.data)}});return c};k.initialize=function(){this.R||this.N.init();this.R=!0};function Ky(){var a=lg(ig.C,"",function(){return{}});try{return a("internal_sw_allowed"),!0}catch(b){return!1}}
function Ly(a,b){var c=Math.round(Ab());b=b===void 0?!1:b;var d=x.location.origin;if(!d||!Ky()||E(168))return;qk()&&(a=""+d+pk()+"/_/service_worker");var e=Hy(a);if(e===null||Iy(e.origin))return;if(!wc()){Fy().H(void 0,void 0,6);return}var f=new Jy(e,!!a,c||Math.round(Ab()),Fy(),b);An(vn.Z.Al)[e.origin]=f;}
var My=function(a,b,c,d){var e;if((e=Iy(a))==null||!e.delegate){var f=wc()?16:6;Fy().C(f,void 0,void 0,b.commandType);d({failureType:f});return}Iy(a).delegate(b,c,d);};
function Ny(a,b,c,d,e){var f=Hy();if(f===null){d(wc()?16:6);return}var g,h=(g=Iy(f.origin))==null?void 0:g.initTime,m=Math.round(Ab()),n={commandType:0,params:{url:a,method:0,templates:b,body:"",processResponse:!1,sinceInit:h?m-h:void 0}};e&&(n.params.encryptionKeyString=e);My(f.origin,n,function(p){c(p)},function(p){d(p.failureType)});}
function Oy(a,b,c,d){var e=Hy(a);if(e===null){d("_is_sw=f"+(wc()?16:6)+"te");return}var f=b?1:0,g=Math.round(Ab()),h,m=(h=Iy(e.origin))==null?void 0:h.initTime,n=m?g-m:void 0,p=!1;E(169)&&(p=!0);My(e.origin,{commandType:0,params:{url:a,method:f,templates:c,body:b||"",processResponse:!0,suppressSuccessCallback:p,sinceInit:n,attributionReporting:!0,referer:x.location.href}},function(){},function(q){var r="_is_sw=f"+q.failureType,t,u=(t=Iy(e.origin))==
null?void 0:t.getState();u!==void 0&&(r+="s"+u);d(n?r+("t"+n):r+"te")});};function Py(a){if(E(10)||qk()||Rj.N||dl(a.D)||E(168))return;Ly(void 0,E(131));};var Qy="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Ry(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function Sy(a){var b=a.google_tag_data,c;if(b!=null&&b.uach){var d=b.uach,e=ma(Object,"assign").call(Object,{},d);d.fullVersionList&&(e.fullVersionList=d.fullVersionList.slice(0));c=e}else c=null;return c}function Ty(a){var b,c;return(c=(b=a.google_tag_data)==null?void 0:b.uach_promise)!=null?c:null}
function Uy(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function Vy(a){if(!Uy(a))return null;var b=Ry(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(Qy).then(function(d){b.uach!=null||(b.uach=d);return d});return b.uach_promise=c};function az(a){var b=a.location.href;if(a===a.top)return{url:b,Wp:!0};var c=!1,d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));var e=a.location.ancestorOrigins;if(e){var f=e[e.length-1];f&&b.indexOf(f)===-1&&(c=!1,b=f)}return{url:b,Wp:c}};function Rz(a,b){var c=!!qk();switch(a){case 45:return"https://www.google.com/ccm/collect";case 46:return c?pk()+"/gs/ccm/collect":"https://pagead2.googlesyndication.com/ccm/collect";case 51:return"https://www.google.com/travel/flights/click/conversion";case 9:return"https://googleads.g.doubleclick.net/pagead/viewthroughconversion";case 17:return c?E(90)&&so()?Pz():""+pk()+"/ag/g/c":Pz();case 16:return c?E(90)&&so()?Qz():""+pk()+"/ga/g/c":Qz();case 1:return"https://ad.doubleclick.net/activity;";case 2:return c?
pk()+"/ddm/activity/":"https://ade.googlesyndication.com/ddm/activity/";case 33:return"https://ad.doubleclick.net/activity;register_conversion=1;";case 11:return c?pk()+"/d/pagead/form-data":E(141)?"https://www.google.com/pagead/form-data":"https://google.com/pagead/form-data";case 3:return"https://"+b.Eo+".fls.doubleclick.net/activityi;";case 5:return"https://www.googleadservices.com/pagead/conversion";case 6:return c?pk()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";
case 8:return"https://www.google.com/pagead/1p-conversion";case 22:return c?pk()+"/as/d/ccm/conversion":"https://www.googleadservices.com/ccm/conversion";case 60:return c?pk()+"/gs/ccm/conversion":"https://pagead2.googlesyndication.com/ccm/conversion";case 23:return c?pk()+"/g/d/ccm/conversion":"https://www.google.com/ccm/conversion";case 55:return c?pk()+"/gs/measurement/conversion/":"https://pagead2.googlesyndication.com/measurement/conversion/";case 54:return E(205)?"https://www.google.com/measurement/conversion/":
c?pk()+"/g/measurement/conversion/":"https://www.google.com/measurement/conversion/";case 21:return c?pk()+"/d/ccm/form-data":E(141)?"https://www.google.com/ccm/form-data":"https://google.com/ccm/form-data";case 7:case 52:case 53:case 39:case 38:case 40:case 37:case 49:case 48:case 14:case 24:case 19:case 27:case 30:case 36:case 26:case 29:case 32:case 35:case 57:case 58:case 50:case 12:case 13:case 20:case 18:case 59:case 47:case 44:case 43:case 15:case 0:case 61:case 56:case 25:case 28:case 31:case 34:throw Error("Unsupported endpoint");
default:nc(a,"Unknown endpoint")}};function Sz(a){a=a===void 0?[]:a;return Sj(a).join("~")}function Tz(){if(!E(118))return"";var a,b;return(((a=Im(xm()))==null?void 0:(b=a.context)==null?void 0:b.loadExperiments)||[]).join("~")};function Uz(a,b){b&&tb(b,function(c,d){typeof d!=="object"&&d!==void 0&&(a["1p."+c]=String(d))})};var bA={};bA.O=hs.O;var cA={nr:"L",so:"S",Er:"Y",Sq:"B",gr:"E",kr:"I",Br:"TC",jr:"HTC"},dA={so:"S",er:"V",Vq:"E",Ar:"tag"},eA={},fA=(eA[bA.O.Oi]="6",eA[bA.O.Pi]="5",eA[bA.O.Ni]="7",eA);function gA(){function a(c,d){var e=ib(d);e&&b.push([c,e])}var b=[];a("u","GTM");a("ut","TAGGING");a("h","HEALTH");return b};var hA=!1;
function AA(a){}function BA(a){}
function CA(){}function DA(a){}
function EA(a){}function FA(a){}
function GA(){}function HA(a,b){}
function IA(a,b,c){}
function JA(){};var KA=Object.freeze({cache:"no-store",credentials:"include",method:"GET",keepalive:!0,redirect:"follow"});
function LA(a,b,c,d,e,f,g,h){var m=ma(Object,"assign").call(Object,{},KA);c&&(m.body=c,m.method="POST");ma(Object,"assign").call(Object,m,e);h==null||cm(h);x.fetch(b,m).then(function(n){h==null||dm(h);if(!n.ok)g==null||g();else if(n.body){var p=n.body.getReader(),q=new TextDecoder;return new Promise(function(r){function t(){p.read().then(function(u){var v;v=u.done;var w=q.decode(u.value,{stream:!v});MA(d,w);v?(f==null||f(),r()):t()}).catch(function(){r()})}t()})}}).catch(function(){h==null||dm(h);
g?g():E(128)&&(b+="&_z=retryFetch",c?lm(a,b,c):km(a,b))})};var NA=function(a){this.P=a;this.C=""},OA=function(a,b){a.H=b;return a},PA=function(a,b){a.N=b;return a},MA=function(a,b){b=a.C+b;for(var c=b.indexOf("\n\n");c!==-1;){var d=a,e;a:{var f=l(b.substring(0,c).split("\n")),g=f.next().value,h=f.next().value;if(g.indexOf("event: message")===0&&h.indexOf("data: ")===0)try{e=JSON.parse(h.substring(h.indexOf(":")+1));break a}catch(m){}e=void 0}QA(d,e);b=b.substring(c+2);c=b.indexOf("\n\n")}a.C=b},RA=function(a,b){return function(){if(b.fallback_url&&b.fallback_url_method){var c=
{};QA(a,(c[b.fallback_url_method]=[b.fallback_url],c.options={},c))}}},QA=function(a,b){b&&(SA(b.send_pixel,b.options,a.P),SA(b.create_iframe,b.options,a.H),SA(b.fetch,b.options,a.N))};function TA(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")}function SA(a,b,c){if(a&&c){var d=a||[];if(Array.isArray(d))for(var e=nd(b)?b:{},f=l(d),g=f.next();!g.done;g=f.next())c(g.value,e)}};var UA=function(a,b){this.gq=a;this.timeoutMs=b;this.Xa=void 0},cm=function(a){a.Xa||(a.Xa=setTimeout(function(){a.gq();a.Xa=void 0},a.timeoutMs))},dm=function(a){a.Xa&&(clearTimeout(a.Xa),a.Xa=void 0)};var KB=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),LB={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},MB={cl:["ecl"],customPixels:["customScripts",
"html"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},NB="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" ");
function OB(){var a=wk("gtm.allowlist")||wk("gtm.whitelist");a&&L(9);fk&&(a=["google","gtagfl","lcl","zone","cmpPartners"]);KB.test(x.location&&x.location.hostname)&&(fk?L(116):(L(117),PB&&(a=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728."))));var b=a&&Eb(xb(a),LB),c=wk("gtm.blocklist")||wk("gtm.blacklist");c||(c=wk("tagTypeBlacklist"))&&L(3);c?L(8):c=[];KB.test(x.location&&x.location.hostname)&&(c=xb(c),c.push("nonGooglePixels","nonGoogleScripts","sandboxedScripts"));
xb(c).indexOf("google")>=0&&L(2);var d=c&&Eb(xb(c),MB),e={};return function(f){var g=f&&f[jf.Ta];if(!g||typeof g!=="string")return!0;g=g.replace(/^_*/,"");if(e[g]!==void 0)return e[g];var h=mk[g]||[],m=!0;if(a){var n;if(n=m)a:{if(b.indexOf(g)<0){if(fk&&h.indexOf("cmpPartners")>=0){n=!0;break a}if(h&&h.length>0)for(var p=0;p<h.length;p++){if(b.indexOf(h[p])<0){L(11);n=!1;break a}}else{n=!1;break a}}n=!0}m=n}var q=!1;if(c){var r=d.indexOf(g)>=0;if(r)q=r;else{var t=rb(d,h||[]);t&&L(10);q=t}}var u=!m||
q;!u&&(h.indexOf("sandboxedScripts")===-1?0:fk&&h.indexOf("cmpPartners")>=0?!QB():b&&b.indexOf("sandboxedScripts")!==-1?0:rb(d,NB))&&(u=!0);return e[g]=u}}function QB(){var a=lg(ig.C,mg.ctid,function(){return{}});try{return a("inject_cmp_banner"),!0}catch(b){return!1}}var PB=!1;PB=!0;function RB(a,b,c,d,e){if(!Nm(a)){d.loadExperiments=Tj();wm(a,d,e);var f=SB(a),g=function(){ym().container[a]&&(ym().container[a].state=3);TB()},h={destinationId:a,endpoint:0};if(qk())om(h,pk()+"/"+f,void 0,g);else{var m=Fb(a,"GTM-"),n=cl(),p=c?"/gtag/js":"/gtm.js",q=bl(b,p+f);if(!q){var r=Vj.wg+p;n&&yc&&m&&(r=yc.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);q=xw("https://","http://",r+f)}om(h,q,void 0,g)}}}function TB(){Pm()||tb(Qm(),function(a,b){UB(a,b.transportUrl,b.context);L(92)})}
function UB(a,b,c,d){if(!Om(a))if(c.loadExperiments||(c.loadExperiments=Tj()),Pm()){var e;(e=ym().destination)[a]!=null||(e[a]={state:0,transportUrl:b,context:c,parent:xm()});ym().destination[a].state=0;zm({ctid:a,isDestination:!0},d);L(91)}else{var f;(f=ym().destination)[a]!=null||(f[a]={context:c,state:1,parent:xm()});ym().destination[a].state=1;zm({ctid:a,isDestination:!0},d);var g={destinationId:a,endpoint:0};if(qk())om(g,pk()+("/gtd"+SB(a,!0)));else{var h="/gtag/destination"+SB(a,!0),m=bl(b,
h);m||(m=xw("https://","http://",Vj.wg+h));om(g,m)}}}function SB(a,b){b=b===void 0?!1:b;var c="?id="+encodeURIComponent(a);Yj!=="dataLayer"&&(c+="&l="+Yj);if(!Fb(a,"GTM-")||b)c=E(130)?c+(qk()?"&sc=1":"&cx=c"):c+"&cx=c";c+="&gtm="+Qr();cl()&&(c+="&sign="+Vj.Ki);var d=Rj.H;d===1?c+="&fps=fc":d===2&&(c+="&fps=fe");!E(191)&&Tj().join("~")&&(c+="&tag_exp="+Tj().join("~"));return c};var VB=function(){this.H=0;this.C={}};VB.prototype.addListener=function(a,b,c){var d=++this.H;this.C[a]=this.C[a]||{};this.C[a][String(d)]={listener:b,Fe:c};return d};VB.prototype.removeListener=function(a,b){var c=this.C[a],d=String(b);if(!c||!c[d])return!1;delete c[d];return!0};var XB=function(a,b){var c=[];tb(WB.C[a],function(d,e){c.indexOf(e.listener)<0&&(e.Fe===void 0||b.indexOf(e.Fe)>=0)&&c.push(e.listener)});return c};function YB(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:mg.ctid}};function ZB(a,b){if(data.entities){var c=data.entities[a];if(c)return c[b]}};var aC=function(a,b){this.C=!1;this.P=[];this.eventData={tags:[]};this.R=!1;this.H=this.N=0;$B(this,a,b)},bC=function(a,b,c,d){if(ak.hasOwnProperty(b)||b==="__zone")return-1;var e={};nd(d)&&(e=od(d,e));e.id=c;e.status="timeout";return a.eventData.tags.push(e)-1},cC=function(a,b,c,d){var e=a.eventData.tags[b];e&&(e.status=c,e.executionTime=d)},dC=function(a){if(!a.C){for(var b=a.P,c=0;c<b.length;c++)b[c]();a.C=!0;a.P.length=0}},$B=function(a,b,c){b!==void 0&&a.Rf(b);c&&x.setTimeout(function(){dC(a)},
Number(c))};aC.prototype.Rf=function(a){var b=this,c=Cb(function(){Oc(function(){a(mg.ctid,b.eventData)})});this.C?c():this.P.push(c)};var eC=function(a){a.N++;return Cb(function(){a.H++;a.R&&a.H>=a.N&&dC(a)})},fC=function(a){a.R=!0;a.H>=a.N&&dC(a)};var gC={};function hC(){return x[iC()]}
function iC(){return x.GoogleAnalyticsObject||"ga"}function lC(){var a=mg.ctid;}
function mC(a,b){return function(){var c=hC(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),h=f.get("hitCallback"),m=g.indexOf("&tid="+b)<0;m&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);m&&(f.set("hitPayload",g,!0),f.set("hitCallback",h,!0),f.set("_x_19",void 0,!0),e(f))})}}};var sC=["es","1"],tC={},uC={};function vC(a,b){if(kl){var c;c=b.match(/^(gtm|gtag)\./)?encodeURIComponent(b):"*";tC[a]=[["e",c],["eid",a]];Cq(a)}}function wC(a){var b=a.eventId,c=a.Nd;if(!tC[b])return[];var d=[];uC[b]||d.push(sC);d.push.apply(d,xa(tC[b]));c&&(uC[b]=!0);return d};var xC={},yC={},zC={};function AC(a,b,c,d){kl&&E(120)&&((d===void 0?0:d)?(zC[b]=zC[b]||0,++zC[b]):c!==void 0?(yC[a]=yC[a]||{},yC[a][b]=Math.round(c)):(xC[a]=xC[a]||{},xC[a][b]=(xC[a][b]||0)+1))}function BC(a){var b=a.eventId,c=a.Nd,d=xC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete xC[b];return e.length?[["md",e.join(".")]]:[]}
function CC(a){var b=a.eventId,c=a.Nd,d=yC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete yC[b];return e.length?[["mtd",e.join(".")]]:[]}function DC(){for(var a=[],b=l(Object.keys(zC)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(""+d+zC[d])}return a.length?[["mec",a.join(".")]]:[]};var EC={},FC={};function GC(a,b,c){if(kl&&b){var d=gl(b);EC[a]=EC[a]||[];EC[a].push(c+d);var e=b[jf.Ta];if(!e)throw Error("Error: No function name given for function call.");var f=(Lf[e]?"1":"2")+d;FC[a]=FC[a]||[];FC[a].push(f);Cq(a)}}function HC(a){var b=a.eventId,c=a.Nd,d=[],e=EC[b]||[];e.length&&d.push(["tr",e.join(".")]);var f=FC[b]||[];f.length&&d.push(["ti",f.join(".")]);c&&(delete EC[b],delete FC[b]);return d};function IC(a,b,c){c=c===void 0?!1:c;JC().addRestriction(0,a,b,c)}function KC(a,b,c){c=c===void 0?!1:c;JC().addRestriction(1,a,b,c)}function LC(){var a=Fm();return JC().getRestrictions(1,a)}var MC=function(){this.container={};this.C={}},NC=function(a,b){var c=a.container[b];c||(c={_entity:{internal:[],external:[]},_event:{internal:[],external:[]}},a.container[b]=c);return c};
MC.prototype.addRestriction=function(a,b,c,d){d=d===void 0?!1:d;if(!d||!this.C[b]){var e=NC(this,b);a===0?d?e._entity.external.push(c):e._entity.internal.push(c):a===1&&(d?e._event.external.push(c):e._event.internal.push(c))}};
MC.prototype.getRestrictions=function(a,b){var c=NC(this,b);if(a===0){var d,e;return[].concat(xa((c==null?void 0:(d=c._entity)==null?void 0:d.internal)||[]),xa((c==null?void 0:(e=c._entity)==null?void 0:e.external)||[]))}if(a===1){var f,g;return[].concat(xa((c==null?void 0:(f=c._event)==null?void 0:f.internal)||[]),xa((c==null?void 0:(g=c._event)==null?void 0:g.external)||[]))}return[]};
MC.prototype.getExternalRestrictions=function(a,b){var c=NC(this,b),d,e;return a===0?(c==null?void 0:(d=c._entity)==null?void 0:d.external)||[]:(c==null?void 0:(e=c._event)==null?void 0:e.external)||[]};MC.prototype.removeExternalRestrictions=function(a){var b=NC(this,a);b._event&&(b._event.external=[]);b._entity&&(b._entity.external=[]);this.C[a]=!0};function JC(){return Ap("r",function(){return new MC})};function OC(a,b,c,d){var e=Jf[a],f=PC(a,b,c,d);if(!f)return null;var g=Yf(e[jf.Bl],c,[]);if(g&&g.length){var h=g[0];f=OC(h.index,{onSuccess:f,onFailure:h.Xl===1?b.terminate:f,terminate:b.terminate},c,d)}return f}
function PC(a,b,c,d){function e(){function w(){go(3);var M=Ab()-I;GC(c.id,f,"7");cC(c.Pc,D,"exception",M);E(109)&&IA(c,f,bA.O.Ni);G||(G=!0,h())}if(f[jf.lo])h();else{var y=Xf(f,c,[]),A=y[jf.Rm];if(A!=null)for(var C=0;C<A.length;C++)if(!P(A[C])){h();return}var D=bC(c.Pc,String(f[jf.Ta]),Number(f[jf.kh]),y[jf.METADATA]),G=!1;y.vtp_gtmOnSuccess=function(){if(!G){G=!0;var M=Ab()-I;GC(c.id,Jf[a],"5");cC(c.Pc,D,"success",M);E(109)&&IA(c,f,bA.O.Pi);g()}};y.vtp_gtmOnFailure=function(){if(!G){G=!0;var M=Ab()-
I;GC(c.id,Jf[a],"6");cC(c.Pc,D,"failure",M);E(109)&&IA(c,f,bA.O.Oi);h()}};y.vtp_gtmTagId=f.tag_id;y.vtp_gtmEventId=c.id;c.priorityId&&(y.vtp_gtmPriorityId=c.priorityId);GC(c.id,f,"1");E(109)&&HA(c,f);var I=Ab();try{Zf(y,{event:c,index:a,type:1})}catch(M){w(M)}E(109)&&IA(c,f,bA.O.Il)}}var f=Jf[a],g=b.onSuccess,h=b.onFailure,m=b.terminate;if(c.isBlocked(f))return null;var n=Yf(f[jf.Jl],c,[]);if(n&&n.length){var p=n[0],q=OC(p.index,{onSuccess:g,onFailure:h,terminate:m},c,d);if(!q)return null;g=q;h=p.Xl===
2?m:q}if(f[jf.ql]||f[jf.no]){var r=f[jf.ql]?Kf:c.Jq,t=g,u=h;if(!r[a]){var v=QC(a,r,Cb(e));g=v.onSuccess;h=v.onFailure}return function(){r[a](t,u)}}return e}function QC(a,b,c){var d=[],e=[];b[a]=RC(d,e,c);return{onSuccess:function(){b[a]=SC;for(var f=0;f<d.length;f++)d[f]()},onFailure:function(){b[a]=TC;for(var f=0;f<e.length;f++)e[f]()}}}function RC(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function SC(a){a()}function TC(a,b){b()};var WC=function(a,b){for(var c=[],d=0;d<Jf.length;d++)if(a[d]){var e=Jf[d];var f=eC(b.Pc);try{var g=OC(d,{onSuccess:f,onFailure:f,terminate:f},b,d);if(g){var h=e[jf.Ta];if(!h)throw Error("Error: No function name given for function call.");var m=Lf[h];c.push({Hm:d,priorityOverride:(m?m.priorityOverride||0:0)||ZB(e[jf.Ta],1)||0,execute:g})}else UC(d,b),f()}catch(p){f()}}c.sort(VC);for(var n=0;n<c.length;n++)c[n].execute();
return c.length>0};function XC(a,b){if(!WB)return!1;var c=a["gtm.triggers"]&&String(a["gtm.triggers"]),d=XB(a.event,c?String(c).split(","):[]);if(!d.length)return!1;for(var e=0;e<d.length;++e){var f=eC(b);try{d[e](a,f)}catch(g){f()}}return!0}function VC(a,b){var c,d=b.priorityOverride,e=a.priorityOverride;c=d>e?1:d<e?-1:0;var f;if(c!==0)f=c;else{var g=a.Hm,h=b.Hm;f=g>h?1:g<h?-1:0}return f}
function UC(a,b){if(kl){var c=function(d){var e=b.isBlocked(Jf[d])?"3":"4",f=Yf(Jf[d][jf.Bl],b,[]);f&&f.length&&c(f[0].index);GC(b.id,Jf[d],e);var g=Yf(Jf[d][jf.Jl],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var YC=!1,WB;function ZC(){WB||(WB=new VB);return WB}
function $C(a){var b=a["gtm.uniqueEventId"],c=a["gtm.priorityId"],d=a.event;if(E(109)){}if(d==="gtm.js"){if(YC)return!1;YC=!0}var e=!1,f=LC(),g=od(a,null);if(!f.every(function(t){return t({originalEventData:g})})){if(d!=="gtm.js"&&d!=="gtm.init"&&d!=="gtm.init_consent")return!1;e=!0}vC(b,d);var h=a.eventCallback,m=
a.eventTimeout,n={id:b,priorityId:c,name:d,isBlocked:aD(g,e),Jq:[],logMacroError:function(){L(6);go(0)},cachedModelValues:bD(),Pc:new aC(function(){if(E(109)){}h&&h.apply(h,Array.prototype.slice.call(arguments,0))},m),
originalEventData:g};E(120)&&kl&&(n.reportMacroDiscrepancy=AC);E(109)&&EA(n.id);var p=dg(n);E(109)&&FA(n.id);e&&(p=cD(p));E(109)&&DA(b);var q=WC(p,n),r=XC(a,n.Pc);fC(n.Pc);d!=="gtm.js"&&d!=="gtm.sync"||lC();return dD(p,q)||r}function bD(){var a={};a.event=Bk("event",1);a.ecommerce=Bk("ecommerce",1);a.gtm=Bk("gtm");a.eventModel=Bk("eventModel");return a}
function aD(a,b){var c=OB();return function(d){if(c(d))return!0;var e=d&&d[jf.Ta];if(!e||typeof e!=="string")return!0;e=e.replace(/^_*/,"");var f,g=Fm();f=JC().getRestrictions(0,g);var h=a;b&&(h=od(a,null),h["gtm.uniqueEventId"]=Number.MAX_SAFE_INTEGER);for(var m=mk[e]||[],n=l(f),p=n.next();!p.done;p=n.next()){var q=p.value;try{if(!q({entityId:e,securityGroups:m,originalEventData:h}))return!0}catch(r){return!0}}return!1}}
function cD(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(Jf[c][jf.Ta]);if(Zj[d]||Jf[c][jf.oo]!==void 0||ZB(d,2))b[c]=!0}return b}function dD(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&Jf[c]&&!ak[String(Jf[c][jf.Ta])])return!0;return!1};function eD(){ZC().addListener("gtm.init",function(a,b){Rj.fa=!0;Qn();b()})};var fD=!1,gD=0,hD=[];function iD(a){if(!fD){var b=z.createEventObject,c=z.readyState==="complete",d=z.readyState==="interactive";if(!a||a.type!=="readystatechange"||c||!b&&d){fD=!0;for(var e=0;e<hD.length;e++)Oc(hD[e])}hD.push=function(){for(var f=Ca.apply(0,arguments),g=0;g<f.length;g++)Oc(f[g]);return 0}}}function jD(){if(!fD&&gD<140){gD++;try{var a,b;(b=(a=z.documentElement).doScroll)==null||b.call(a,"left");iD()}catch(c){x.setTimeout(jD,50)}}}
function kD(){var a=x;fD=!1;gD=0;if(z.readyState==="interactive"&&!z.createEventObject||z.readyState==="complete")iD();else{Mc(z,"DOMContentLoaded",iD);Mc(z,"readystatechange",iD);if(z.createEventObject&&z.documentElement.doScroll){var b=!0;try{b=!a.frameElement}catch(c){}b&&jD()}Mc(a,"load",iD)}}function lD(a){fD?a():hD.push(a)};var mD={},nD={};function oD(a,b){for(var c=[],d=[],e={},f=0;f<a.length;e={wj:void 0,cj:void 0},f++){var g=a[f];if(g.indexOf("-")>=0){if(e.wj=Kp(g,b),e.wj){var h=Em();pb(h,function(r){return function(t){return r.wj.destinationId===t}}(e))?c.push(g):d.push(g)}}else{var m=mD[g]||[];e.cj={};m.forEach(function(r){return function(t){r.cj[t]=!0}}(e));for(var n=Gm(),p=0;p<n.length;p++)if(e.cj[n[p]]){c=c.concat(Em());break}var q=nD[g]||[];q.length&&(c=c.concat(q))}}return{qj:c,cq:d}}
function pD(a){tb(mD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})}function qD(a){tb(nD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})};var rD=!1,sD=!1;function tD(a,b){var c={},d=(c.event=a,c);b&&(d.eventModel=od(b,null),b[J.m.df]&&(d.eventCallback=b[J.m.df]),b[J.m.Kg]&&(d.eventTimeout=b[J.m.Kg]));return d}function uD(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:Dp()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
function vD(a,b){var c=a&&a[J.m.md];c===void 0&&(c=wk(J.m.md,2),c===void 0&&(c="default"));if(mb(c)||Array.isArray(c)){var d;d=b.isGtmEvent?mb(c)?[c]:c:c.toString().replace(/\s+/g,"").split(",");var e=oD(d,b.isGtmEvent),f=e.qj,g=e.cq;if(g.length)for(var h=wD(a),m=0;m<g.length;m++){var n=Kp(g[m],b.isGtmEvent);if(n){var p=n.destinationId,q=n.destinationId,r=ym().destination[q];r&&r.state===0||UB(p,h,{source:3,fromContainerExecution:b.fromContainerExecution})}}var t=f.concat(g);return{qj:Lp(f,b.isGtmEvent),
Ho:Lp(t,b.isGtmEvent)}}}var xD=void 0,yD=void 0;function zD(a,b,c){var d=od(a,null);d.eventId=void 0;d.inheritParentConfig=void 0;Object.keys(b).some(function(f){return b[f]!==void 0})&&L(136);var e=od(b,null);od(c,e);Uw(Qw(Gm()[0],e),a.eventId,d)}function wD(a){for(var b=l([J.m.nd,J.m.vc]),c=b.next();!c.done;c=b.next()){var d=c.value,e=a&&a[d]||Kq.C[d];if(e)return e}}
var AD={config:function(a,b){var c=uD(a,b);if(!(a.length<2)&&mb(a[1])){var d={};if(a.length>2){if(a[2]!==void 0&&!nd(a[2])||a.length>3)return;d=a[2]}var e=Kp(a[1],b.isGtmEvent);if(e){var f,g,h;a:{if(!Cm.qe){var m=Im(xm());if(Rm(m)){var n=m.parent,p=n.isDestination;h={hq:Im(n),Yp:p};break a}}h=void 0}var q=h;q&&(f=q.hq,g=q.Yp);vC(c.eventId,"gtag.config");var r=e.destinationId,t=e.id!==r;if(t?Em().indexOf(r)===-1:Gm().indexOf(r)===-1){if(!b.inheritParentConfig&&!d[J.m.Lc]){var u=wD(d);if(t)UB(r,u,{source:2,
fromContainerExecution:b.fromContainerExecution});else if(f!==void 0&&f.containers.indexOf(r)!==-1){var v=d;xD?zD(b,v,xD):yD||(yD=od(v,null))}else RB(r,u,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(f&&(L(128),g&&L(130),b.inheritParentConfig)){var w;var y=d;yD?(zD(b,yD,y),w=!1):(!y[J.m.pd]&&ck&&xD||(xD=od(y,null)),w=!0);w&&f.containers&&f.containers.join(",");return}ll&&(Fp===1&&(In.mcc=!1),Fp=2);if(ck&&!t&&!d[J.m.pd]){var A=sD;sD=!0;if(A)return}rD||L(43);if(!b.noTargetGroup)if(t){qD(e.id);
var C=e.id,D=d[J.m.Ng]||"default";D=String(D).split(",");for(var G=0;G<D.length;G++){var I=nD[D[G]]||[];nD[D[G]]=I;I.indexOf(C)<0&&I.push(C)}}else{pD(e.id);var M=e.id,T=d[J.m.Ng]||"default";T=T.toString().split(",");for(var da=0;da<T.length;da++){var O=mD[T[da]]||[];mD[T[da]]=O;O.indexOf(M)<0&&O.push(M)}}delete d[J.m.Ng];var V=b.eventMetadata||{};V.hasOwnProperty(Q.A.vd)||(V[Q.A.vd]=!b.fromContainerExecution);b.eventMetadata=V;delete d[J.m.df];for(var ia=t?[e.id]:Em(),ka=0;ka<ia.length;ka++){var X=
d,Y=ia[ka],ja=od(b,null),ya=Kp(Y,ja.isGtmEvent);ya&&Kq.push("config",[X],ya,ja)}}}}},consent:function(a,b){if(a.length===3){L(39);var c=uD(a,b),d=a[1],e={},f=Jo(a[2]),g;for(g in f)if(f.hasOwnProperty(g)){var h=f[g];e[g]=g===J.m.rg?Array.isArray(h)?NaN:Number(h):g===J.m.hc?(Array.isArray(h)?h:[h]).map(Ko):Lo(h)}b.fromContainerExecution||(e[J.m.V]&&L(139),e[J.m.La]&&L(140));d==="default"?mp(e):d==="update"?op(e,c):d==="declare"&&b.fromContainerExecution&&lp(e)}},event:function(a,b){var c=a[1];if(!(a.length<
2)&&mb(c)){var d=void 0;if(a.length>2){if(!nd(a[2])&&a[2]!==void 0||a.length>3)return;d=a[2]}var e=tD(c,d),f=uD(a,b),g=f.eventId,h=f.priorityId;e["gtm.uniqueEventId"]=g;h&&(e["gtm.priorityId"]=h);if(c==="optimize.callback")return e.eventModel=e.eventModel||{},e;var m=vD(d,b);if(m){for(var n=m.qj,p=m.Ho,q=p.map(function(M){return M.id}),r=p.map(function(M){return M.destinationId}),t=n.map(function(M){return M.id}),u=l(Em()),v=u.next();!v.done;v=u.next()){var w=v.value;r.indexOf(w)<0&&t.push(w)}vC(g,
c);for(var y=l(t),A=y.next();!A.done;A=y.next()){var C=A.value,D=od(b,null),G=od(d,null);delete G[J.m.df];var I=D.eventMetadata||{};I.hasOwnProperty(Q.A.vd)||(I[Q.A.vd]=!D.fromContainerExecution);I[Q.A.Ii]=q.slice();I[Q.A.Of]=r.slice();D.eventMetadata=I;Lq(c,G,C,D)}e.eventModel=e.eventModel||{};q.length>0?e.eventModel[J.m.md]=q.join(","):delete e.eventModel[J.m.md];rD||L(43);b.noGtmEvent===void 0&&b.eventMetadata&&b.eventMetadata[Q.A.Gl]&&(b.noGtmEvent=!0);e.eventModel[J.m.Kc]&&(b.noGtmEvent=!0);
return b.noGtmEvent?void 0:e}}},get:function(a,b){L(53);if(a.length===4&&mb(a[1])&&mb(a[2])&&lb(a[3])){var c=Kp(a[1],b.isGtmEvent),d=String(a[2]),e=a[3];if(c){rD||L(43);var f=wD();if(pb(Em(),function(h){return c.destinationId===h})){uD(a,b);var g={};od((g[J.m.rc]=d,g[J.m.Ic]=e,g),null);Mq(d,function(h){Oc(function(){e(h)})},c.id,b)}else UB(c.destinationId,f,{source:4,fromContainerExecution:b.fromContainerExecution})}}},js:function(a,b){if(a.length===2&&a[1].getTime){rD=!0;var c=uD(a,b),d=c.eventId,
e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(a.length===3&&mb(a[1])&&lb(a[2])){if(jg(a[1],a[2]),L(74),a[1]==="all"){L(75);var b=!1;try{b=a[2](mg.ctid,"unknown",{})}catch(c){}b||L(76)}}else L(73)},set:function(a,b){var c=void 0;a.length===2&&nd(a[1])?c=od(a[1],null):a.length===3&&mb(a[1])&&(c={},nd(a[2])||Array.isArray(a[2])?c[a[1]]=od(a[2],null):c[a[1]]=a[2]);if(c){var d=uD(a,b),e=d.eventId,f=d.priorityId;
od(c,null);var g=od(c,null);Kq.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=e;f&&(c["gtm.priorityId"]=f);delete c.event;b.overwriteModelFields=!0;return c}}},BD={policy:!0};var DD=function(a){if(CD(a))return a;this.value=a};DD.prototype.getUntrustedMessageValue=function(){return this.value};var CD=function(a){return!a||ld(a)!=="object"||nd(a)?!1:"getUntrustedMessageValue"in a};DD.prototype.getUntrustedMessageValue=DD.prototype.getUntrustedMessageValue;var ED=!1,FD=[];function GD(){if(!ED){ED=!0;for(var a=0;a<FD.length;a++)Oc(FD[a])}}function HD(a){ED?Oc(a):FD.push(a)};var ID=0,JD={},KD=[],LD=[],MD=!1,ND=!1;function OD(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}function PD(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return QD(a)}function RD(a,b){if(!nb(b)||b<0)b=0;var c=zp[Yj],d=0,e=!1,f=void 0;f=x.setTimeout(function(){e||(e=!0,a());f=void 0},b);return function(){var g=c?c.subscribers:1;++d===g&&(f&&(x.clearTimeout(f),f=void 0),e||(a(),e=!0))}}
function SD(a){if(a==null||typeof a!=="object")return!1;if(a.event)return!0;if(ub(a)){var b=a[0];if(b==="config"||b==="event"||b==="js"||b==="get")return!0}return!1}
function TD(){var a;if(LD.length)a=LD.shift();else if(KD.length)a=KD.shift();else return;var b;var c=a;if(MD||!SD(c.message))b=c;else{MD=!0;var d=c.message["gtm.uniqueEventId"],e,f;typeof d==="number"?(e=d-2,f=d-1):(e=Dp(),f=Dp(),c.message["gtm.uniqueEventId"]=Dp());var g={},h={message:(g.event="gtm.init_consent",g["gtm.uniqueEventId"]=e,g),messageContext:{eventId:e}},m={},n={message:(m.event="gtm.init",m["gtm.uniqueEventId"]=f,m),messageContext:{eventId:f}};KD.unshift(n,c);b=h}return b}
function UD(){for(var a=!1,b;!ND&&(b=TD());){ND=!0;delete tk.eventModel;vk();var c=b,d=c.message,e=c.messageContext;if(d==null)ND=!1;else{e.fromContainerExecution&&Ak();try{if(lb(d))try{d.call(xk)}catch(G){}else if(Array.isArray(d)){if(mb(d[0])){var f=d[0].split("."),g=f.pop(),h=d.slice(1),m=wk(f.join("."),2);if(m!=null)try{m[g].apply(m,h)}catch(G){}}}else{var n=void 0;if(ub(d))a:{if(d.length&&mb(d[0])){var p=AD[d[0]];if(p&&(!e.fromContainerExecution||!BD[d[0]])){n=p(d,e);break a}}n=void 0}else n=
d;if(n){var q;for(var r=n,t=r._clear||e.overwriteModelFields,u=l(Object.keys(r)),v=u.next();!v.done;v=u.next()){var w=v.value;w!=="_clear"&&(t&&zk(w),zk(w,r[w]))}jk||(jk=r["gtm.start"]);var y=r["gtm.uniqueEventId"];r.event?(typeof y!=="number"&&(y=Dp(),r["gtm.uniqueEventId"]=y,zk("gtm.uniqueEventId",y)),q=$C(r)):q=!1;a=q||a}}}finally{e.fromContainerExecution&&vk(!0);var A=d["gtm.uniqueEventId"];if(typeof A==="number"){for(var C=JD[String(A)]||[],D=0;D<C.length;D++)LD.push(VD(C[D]));C.length&&LD.sort(OD);
delete JD[String(A)];A>ID&&(ID=A)}ND=!1}}}return!a}
function WD(){if(E(109)){var a=!Rj.ma;}var c=UD();if(E(109)){}try{var e=mg.ctid,f=x[Yj].hide;if(f&&f[e]!==void 0&&f.end){f[e]=
!1;var g=!0,h;for(h in f)if(f.hasOwnProperty(h)&&f[h]===!0){g=!1;break}g&&(f.end(),f.end=null)}}catch(m){}return c}function Xw(a){if(ID<a.notBeforeEventId){var b=String(a.notBeforeEventId);JD[b]=JD[b]||[];JD[b].push(a)}else LD.push(VD(a)),LD.sort(OD),Oc(function(){ND||UD()})}function VD(a){return{message:a.message,messageContext:a.messageContext}}
function XD(){function a(f){var g={};if(CD(f)){var h=f;f=CD(h)?h.getUntrustedMessageValue():void 0;g.fromContainerExecution=!0}return{message:f,messageContext:g}}var b=zc(Yj,[]),c=zp[Yj]=zp[Yj]||{};c.pruned===!0&&L(83);JD=Vw().get();Ww();lD(function(){if(!c.gtmDom){c.gtmDom=!0;var f={};b.push((f.event="gtm.dom",f))}});HD(function(){if(!c.gtmLoad){c.gtmLoad=!0;var f={};b.push((f.event="gtm.load",f))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var f;if(zp.SANDBOXED_JS_SEMAPHORE>
0){f=[];for(var g=0;g<arguments.length;g++)f[g]=new DD(arguments[g])}else f=[].slice.call(arguments,0);var h=f.map(function(q){return a(q)});KD.push.apply(KD,h);var m=d.apply(b,f),n=Math.max(100,Number("1000")||300);if(this.length>n)for(L(4),c.pruned=!0;this.length>n;)this.shift();var p=typeof m!=="boolean"||m;return UD()&&p};var e=b.slice(0).map(function(f){return a(f)});KD.push.apply(KD,e);if(!Rj.ma){if(E(109)){}Oc(WD)}}var QD=function(a){return x[Yj].push(a)};function YD(a){QD(a)};function ZD(){var a,b=Wk(x.location.href);(a=b.hostname+b.pathname)&&Mn("dl",encodeURIComponent(a));var c;var d=mg.ctid;if(d){var e=Cm.qe?1:0,f,g=Im(xm());f=g&&g.context;c=d+";"+mg.canonicalContainerId+";"+(f&&f.fromContainerExecution?1:0)+";"+(f&&f.source||0)+";"+e}else c=void 0;var h=c;h&&Mn("tdp",h);var m=Ll(!0);m!==void 0&&Mn("frm",String(m))};function $D(){(Wo()||ll)&&x.addEventListener("securitypolicyviolation",function(a){if(a.disposition==="enforce"){L(179);var b=jm(a.effectiveDirective);if(b){var c;var d=hm(b,a.blockedURI);c=d?fm[b][d]:void 0;if(c){var e;a:{try{var f=new URL(a.blockedURI),g=f.pathname.indexOf(";");e=g>=0?f.origin+f.pathname.substring(0,g):f.origin+f.pathname;break a}catch(v){}e=void 0}var h=e;if(h){for(var m=l(c),n=m.next();!n.done;n=m.next()){var p=n.value;if(!p.Am){p.Am=!0;if(E(59)){var q={eventId:p.eventId,priorityId:p.priorityId};
if(Wo()){var r=q,t={type:1,blockedUrl:h,endpoint:p.endpoint,violation:a.effectiveDirective};if(Wo()){var u=bp("TAG_DIAGNOSTICS",{eventId:r==null?void 0:r.eventId,priorityId:r==null?void 0:r.priorityId});u.tagDiagnostics=t;Vo(u)}}}Sn(p.endpoint)}}im(b,a.blockedURI)}}}}})};function aE(){var a;var b=Hm();if(b)if(b.canonicalContainerId)a=b.canonicalContainerId;else{var c,d=b.scriptContainerId||((c=b.destinations)==null?void 0:c[0]);a=d?"_"+d:void 0}else a=void 0;var e=a;e&&Mn("pcid",e)};var bE=/^(https?:)?\/\//;
function cE(){var a=Jm();if(a){var b;a:{var c,d=(c=a.scriptElement)==null?void 0:c.src;if(d){var e;try{var f;e=(f=cd())==null?void 0:f.getEntriesByType("resource")}catch(q){}if(e){for(var g=-1,h=l(e),m=h.next();!m.done;m=h.next()){var n=m.value;if(n.initiatorType==="script"&&(g+=1,n.name.replace(bE,"")===d.replace(bE,""))){b=g;break a}}L(146)}else L(145)}b=void 0}var p=b;p!==void 0&&(a.canonicalContainerId&&Mn("rtg",String(a.canonicalContainerId)),Mn("slo",String(p)),Mn("hlo",a.htmlLoadOrder||"-1"),
Mn("lst",String(a.loadScriptType||"0")))}else L(144)};function dE(){var a=[],b=Number('')||0,c=Number('')||0;c||(c=b/100);var d=function(){var t=!1;return t}();a.push({Fh:219,studyId:219,experimentId:104948811,
controlId:104948812,controlId2:0,probability:c,active:d,Uf:0});var e=Number('')||0,f=Number('')||0;f||(f=e/100);var g=function(){var t=!1;
return t}();a.push({Fh:220,studyId:220,experimentId:104948813,controlId:104948814,controlId2:0,probability:f,active:g,Uf:0});var h=Number('')||0,m=Number('')||0;m||(m=h/100);var n=function(){var t=!1;return t}();a.push({Fh:195,studyId:195,experimentId:104527906,controlId:104527907,controlId2:104898015,
probability:m,active:n,Uf:1});var p=Number('')||0,q=Number('')||0;q||(q=p/100);var r=function(){var t=!1;return t}();a.push({Fh:196,studyId:196,experimentId:104528500,controlId:104528501,controlId2:104898016,probability:q,active:r,Uf:0});return a};var eE={};function fE(a){for(var b=l(Object.keys(a.exp||{})),c=b.next();!c.done;c=b.next())Rj.R.H.add(Number(c.value))}function gE(a){var b=An(vn.Z.rl);return!!ni[a].active||ni[a].probability>.5||!!(b.exp||{})[ni[a].experimentId]||!!ni[a].active||ni[a].probability>.5||!!(eE.exp||{})[ni[a].experimentId]}
function hE(){for(var a=l(dE()),b=a.next();!b.done;b=a.next()){var c=b.value,d=c.Fh;ni[d]=c;if(c.Uf===1){var e=d,f=An(vn.Z.rl);ri(f,e);fE(f);gE(e)&&B(e)}else if(c.Uf===0){var g=d,h=eE;ri(h,g);fE(h);gE(g)&&B(g)}}};
function CE(){};var DE=function(){};DE.prototype.toString=function(){return"undefined"};var EE=new DE;function LE(){E(212)&&fk&&jg("all",function(a,b,c){var d=c.options;switch(b){case "detect_link_click_events":case "detect_form_submit_events":return(d==null?void 0:d.waitForTags)!==!0;case "detect_youtube_activity_events":return(d==null?void 0:d.fixMissingApi)!==!0;default:return!0}})};function ME(a,b){function c(g){var h=Wk(g),m=Qk(h,"protocol"),n=Qk(h,"host",!0),p=Qk(h,"port"),q=Qk(h,"path").toLowerCase().replace(/\/$/,"");if(m===void 0||m==="http"&&p==="80"||m==="https"&&p==="443")m="web",p="default";return[m,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0}
function NE(a){return OE(a)?1:0}
function OE(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=od(a,{});od({arg1:c[d],any_of:void 0},e);if(NE(e))return!0}return!1}switch(a["function"]){case "_cn":return Sg(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<Ng.length;g++){var h=Ng[g];if(b[h]!=null){f=b[h](c);break a}}}catch(m){}f=!1}return f;case "_ew":return Og(b,c);case "_eq":return Tg(b,c);case "_ge":return Ug(b,c);case "_gt":return Wg(b,c);case "_lc":return Pg(b,c);case "_le":return Vg(b,
c);case "_lt":return Xg(b,c);case "_re":return Rg(b,c,a.ignore_case);case "_sw":return Yg(b,c);case "_um":return ME(b,c)}return!1};[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});var PE=function(a,b,c,d){ar.call(this);this.gh=b;this.Kf=c;this.rb=d;this.Ua=new Map;this.hh=0;this.ma=new Map;this.Da=new Map;this.R=void 0;this.H=a};va(PE,ar);PE.prototype.N=function(){delete this.C;this.Ua.clear();this.ma.clear();this.Da.clear();this.R&&(Xq(this.H,"message",this.R),delete this.R);delete this.H;delete this.rb;ar.prototype.N.call(this)};
var QE=function(a){if(a.C)return a.C;a.Kf&&a.Kf(a.H)?a.C=a.H:a.C=Kl(a.H,a.gh);var b;return(b=a.C)!=null?b:null},SE=function(a,b,c){if(QE(a))if(a.C===a.H){var d=a.Ua.get(b);d&&d(a.C,c)}else{var e=a.ma.get(b);if(e&&e.pj){RE(a);var f=++a.hh;a.Da.set(f,{Dh:e.Dh,ap:e.gm(c),persistent:b==="addEventListener"});a.C.postMessage(e.pj(c,f),"*")}}},RE=function(a){a.R||(a.R=function(b){try{var c;c=a.rb?a.rb(b):void 0;if(c){var d=c.kq,e=a.Da.get(d);if(e){e.persistent||a.Da.delete(d);var f;(f=e.Dh)==null||f.call(e,
e.ap,c.payload)}}}catch(g){}},Wq(a.H,"message",a.R))};var TE=function(a,b){var c=b.listener,d=(0,a.__gpp)("addEventListener",c);d&&c(d,!0)},UE=function(a,b){(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},VE={gm:function(a){return a.listener},pj:function(a,b){var c={};return c.__gppCall={callId:b,command:"addEventListener",version:"1.1"},c},Dh:function(a,b){var c=b.__gppReturn;a(c.returnValue,c.success)}},WE={gm:function(a){return a.listener},pj:function(a,b){var c={};return c.__gppCall={callId:b,command:"removeEventListener",version:"1.1",
parameter:a.listenerId},c},Dh:function(a,b){var c=b.__gppReturn,d=c.returnValue.data;a==null||a(d,c.success)}};function XE(a){var b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,kq:b.__gppReturn.callId}}
var YE=function(a,b){var c;c=(b===void 0?{}:b).timeoutMs;ar.call(this);this.caller=new PE(a,"__gppLocator",function(d){return typeof d.__gpp==="function"},XE);this.caller.Ua.set("addEventListener",TE);this.caller.ma.set("addEventListener",VE);this.caller.Ua.set("removeEventListener",UE);this.caller.ma.set("removeEventListener",WE);this.timeoutMs=c!=null?c:500};va(YE,ar);YE.prototype.N=function(){this.caller.dispose();ar.prototype.N.call(this)};
YE.prototype.addEventListener=function(a){var b=this,c=nl(function(){a(ZE,!0)}),d=this.timeoutMs===-1?void 0:setTimeout(function(){c()},this.timeoutMs);SE(this.caller,"addEventListener",{listener:function(e,f){clearTimeout(d);try{var g;var h;((h=e.pingData)==null?void 0:h.gppVersion)===void 0||e.pingData.gppVersion==="1"||e.pingData.gppVersion==="1.0"?(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",
applicableSections:[-1]}}):Array.isArray(e.pingData.applicableSections)?g=e:(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY",applicableSections:[-1]}});a(g,f)}catch(m){if(e==null?0:e.listenerId)try{b.removeEventListener(e.listenerId)}catch(n){a($E,!0);return}a(aF,!0)}}})};
YE.prototype.removeEventListener=function(a){SE(this.caller,"removeEventListener",{listener:function(){},listenerId:a})};
var aF={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},ZE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},$E={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1};function bF(a){var b;if(!(b=a.pingData.signalStatus==="ready")){var c=a.pingData.applicableSections;b=!c||c.length===1&&c[0]===-1}if(b){Dv.gppString=a.pingData.gppString;var d=a.pingData.applicableSections.join(",");Dv.C=d}}function cF(){try{var a=new YE(x,{timeoutMs:-1});QE(a.caller)&&a.addEventListener(bF)}catch(b){}};function dF(){var a=[["cv",Vi(1)],["rv",Wj],["tc",Jf.filter(function(b){return b}).length]];Xj&&a.push(["x",Xj]);ok()&&a.push(["tag_exp",ok()]);return a};var eF={};function Yi(a){eF[a]=(eF[a]||0)+1}function fF(){for(var a=[],b=l(Object.keys(eF)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(d+"."+eF[d])}return a.length===0?[]:[["bdm",a.join("~")]]};var gF={},hF={};function iF(a){var b=a.eventId,c=a.Nd,d=[],e=gF[b]||[];e.length&&d.push(["hf",e.join(".")]);var f=hF[b]||[];f.length&&d.push(["ht",f.join(".")]);c&&(delete gF[b],delete hF[b]);return d};function jF(){return!1}function kF(){var a={};return function(b,c,d){}};function lF(){var a=mF;return function(b,c,d){var e=d&&d.event;nF(c);var f=Dh(b)?void 0:1,g=new Xa;tb(c,function(r,t){var u=Ed(t,void 0,f);u===void 0&&t!==void 0&&L(44);g.set(r,u)});a.Nb(bg());var h={Ql:qg(b),eventId:e==null?void 0:e.id,priorityId:e!==void 0?e.priorityId:void 0,Rf:e!==void 0?function(r){e.Pc.Rf(r)}:void 0,Jb:function(){return b},log:function(){},np:{index:d==null?void 0:d.index,type:d==null?void 0:d.type,name:d==null?void 0:d.name},uq:!!ZB(b,3),originalEventData:e==null?void 0:e.originalEventData};
e&&e.cachedModelValues&&(h.cachedModelValues={gtm:e.cachedModelValues.gtm,ecommerce:e.cachedModelValues.ecommerce});if(jF()){var m=kF(),n,p;h.xb={Ej:[],Sf:{},bc:function(r,t,u){t===1&&(n=r);t===7&&(p=u);m(r,t,u)},Bh:Vh()};h.log=function(r){var t=Ca.apply(1,arguments);n&&m(n,4,{level:r,source:p,message:t})}}var q=$e(a,h,[b,g]);a.Nb();q instanceof Fa&&(q.type==="return"?q=q.data:q=void 0);return Dd(q,void 0,f)}}function nF(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;lb(b)&&(a.gtmOnSuccess=function(){Oc(b)});lb(c)&&(a.gtmOnFailure=function(){Oc(c)})};function oF(a){}oF.M="internal.addAdsClickIds";function pF(a,b){var c=this;}pF.publicName="addConsentListener";var qF=!1;function rF(a){for(var b=0;b<a.length;++b)if(qF)try{a[b]()}catch(c){L(77)}else a[b]()}function sF(a,b,c){var d=this,e;return e}sF.M="internal.addDataLayerEventListener";function tF(a,b,c){}tF.publicName="addDocumentEventListener";function uF(a,b,c,d){}uF.publicName="addElementEventListener";function vF(a){return a.K.ub()};function wF(a){}wF.publicName="addEventCallback";
function LF(a){}LF.M="internal.addFormAbandonmentListener";function MF(a,b,c,d){}
MF.M="internal.addFormData";var NF={},OF=[],PF={},QF=0,RF=0;
function YF(a,b){}YF.M="internal.addFormInteractionListener";
function eG(a,b){}eG.M="internal.addFormSubmitListener";
function jG(a){}jG.M="internal.addGaSendListener";function kG(a){if(!a)return{};var b=a.np;return YB(b.type,b.index,b.name)}function lG(a){return a?{originatingEntity:kG(a)}:{}};function tG(a){var b=zp.zones;return b?b.getIsAllowedFn(Gm(),a):function(){return!0}}function uG(){var a=zp.zones;a&&a.unregisterChild(Gm())}
function vG(){KC(Fm(),function(a){var b=zp.zones;return b?b.isActive(Gm(),a.originalEventData["gtm.uniqueEventId"]):!0});IC(Fm(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return tG(Number(a.originalEventData["gtm.uniqueEventId"]))(b,c)})};var wG=function(a,b){this.tagId=a;this.we=b};
function xG(a,b){var c=this;return a}xG.M="internal.loadGoogleTag";function yG(a){return new vd("",function(b){var c=this.evaluate(b);if(c instanceof vd)return new vd("",function(){var d=Ca.apply(0,arguments),e=this,f=od(vF(this),null);f.eventId=a.eventId;f.priorityId=a.priorityId;f.originalEventData=a.originalEventData;var g=d.map(function(m){return e.evaluate(m)}),h=this.K.tb();h.Ld(f);return c.Lb.apply(c,[h].concat(xa(g)))})})};function zG(a,b,c){var d=this;}zG.M="internal.addGoogleTagRestriction";var AG={},BG=[];
function IG(a,b){}
IG.M="internal.addHistoryChangeListener";function JG(a,b,c){}JG.publicName="addWindowEventListener";function KG(a,b){return!0}KG.publicName="aliasInWindow";function LG(a,b,c){}LG.M="internal.appendRemoteConfigParameter";function MG(a){var b;return b}
MG.publicName="callInWindow";function NG(a){}NG.publicName="callLater";function OG(a){}OG.M="callOnDomReady";function PG(a){}PG.M="callOnWindowLoad";function QG(a,b){var c;return c}QG.M="internal.computeGtmParameter";function RG(a,b){var c=this;}RG.M="internal.consentScheduleFirstTry";function SG(a,b){var c=this;}SG.M="internal.consentScheduleRetry";function TG(a){var b;return b}TG.M="internal.copyFromCrossContainerData";function UG(a,b){var c;if(!oh(a)||!th(b)&&b!==null&&!jh(b))throw F(this.getName(),["string","number|undefined"],arguments);H(this,"read_data_layer",a);c=(b||2)!==2?wk(a,1):yk(a,[x,z]);var d=Ed(c,this.K,Dh(vF(this).Jb())?2:1);d===void 0&&c!==void 0&&L(45);return d}UG.publicName="copyFromDataLayer";
function VG(a){var b=void 0;return b}VG.M="internal.copyFromDataLayerCache";function WG(a){var b;return b}WG.publicName="copyFromWindow";function XG(a){var b=void 0;return Ed(b,this.K,1)}XG.M="internal.copyKeyFromWindow";var YG=function(a){return a===Um.X.Fa&&mn[a]===Tm.Ia.pe&&!P(J.m.U)};var ZG=function(){return"0"},$G=function(a){if(typeof a!=="string")return"";var b=["gclid","dclid","wbraid","_gl"];E(102)&&b.push("gbraid");return Xk(a,b,"0")};var aH={},bH={},cH={},dH={},eH={},fH={},gH={},hH={},iH={},jH={},kH={},lH={},mH={},nH={},oH={},pH={},qH={},rH={},sH={},tH={},uH={},vH={},wH={},xH={},yH={},zH={},AH=(zH[J.m.Na]=(aH[2]=[YG],aH),zH[J.m.qf]=(bH[2]=[YG],bH),zH[J.m.ef]=(cH[2]=[YG],cH),zH[J.m.mi]=(dH[2]=[YG],dH),zH[J.m.ni]=(eH[2]=[YG],eH),zH[J.m.oi]=(fH[2]=[YG],fH),zH[J.m.ri]=(gH[2]=[YG],gH),zH[J.m.si]=(hH[2]=[YG],hH),zH[J.m.wc]=(iH[2]=[YG],iH),zH[J.m.rf]=(jH[2]=[YG],jH),zH[J.m.tf]=(kH[2]=[YG],kH),zH[J.m.uf]=(lH[2]=[YG],lH),zH[J.m.vf]=(mH[2]=
[YG],mH),zH[J.m.wf]=(nH[2]=[YG],nH),zH[J.m.xf]=(oH[2]=[YG],oH),zH[J.m.yf]=(pH[2]=[YG],pH),zH[J.m.zf]=(qH[2]=[YG],qH),zH[J.m.mb]=(rH[1]=[YG],rH),zH[J.m.Zc]=(sH[1]=[YG],sH),zH[J.m.gd]=(tH[1]=[YG],tH),zH[J.m.Xd]=(uH[1]=[YG],uH),zH[J.m.Pe]=(vH[1]=[function(a){return E(102)&&YG(a)}],vH),zH[J.m.hd]=(wH[1]=[YG],wH),zH[J.m.Ca]=(xH[1]=[YG],xH),zH[J.m.Ya]=(yH[1]=[YG],yH),zH),BH={},CH=(BH[J.m.mb]=ZG,BH[J.m.Zc]=ZG,BH[J.m.gd]=ZG,BH[J.m.Xd]=ZG,BH[J.m.Pe]=ZG,BH[J.m.hd]=function(a){if(!nd(a))return{};var b=od(a,
null);delete b.match_id;return b},BH[J.m.Ca]=$G,BH[J.m.Ya]=$G,BH),DH={},EH={},FH=(EH[Q.A.jb]=(DH[2]=[YG],DH),EH),GH={};var HH=function(a,b,c,d){this.C=a;this.N=b;this.P=c;this.R=d};HH.prototype.getValue=function(a){a=a===void 0?Um.X.Hb:a;if(!this.N.some(function(b){return b(a)}))return this.P.some(function(b){return b(a)})?this.R(this.C):this.C};HH.prototype.H=function(){return ld(this.C)==="array"||nd(this.C)?od(this.C,null):this.C};
var IH=function(){},JH=function(a,b){this.conditions=a;this.C=b},KH=function(a,b,c){var d,e=((d=a.conditions[b])==null?void 0:d[2])||[],f,g=((f=a.conditions[b])==null?void 0:f[1])||[];return new HH(c,e,g,a.C[b]||IH)},LH,MH;var NH=function(a,b,c){this.eventName=b;this.D=c;this.C={};this.isAborted=!1;this.target=a;this.metadata={};for(var d=c.eventMetadata||{},e=l(Object.keys(d)),f=e.next();!f.done;f=e.next()){var g=f.value;S(this,g,d[g])}},Tv=function(a,b){var c,d;return(c=a.C[b])==null?void 0:(d=c.getValue)==null?void 0:d.call(c,R(a,Q.A.Pf))},U=function(a,b,c){var d=a.C,e;c===void 0?e=void 0:(LH!=null||(LH=new JH(AH,CH)),e=KH(LH,b,c));d[b]=e};
NH.prototype.mergeHitDataForKey=function(a,b){var c,d,e;c=(d=this.C[a])==null?void 0:(e=d.H)==null?void 0:e.call(d);if(!c)return U(this,a,b),!0;if(!nd(c))return!1;U(this,a,ma(Object,"assign").call(Object,c,b));return!0};var OH=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.C)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.C[e])==null?void 0:(h=(g=f).H)==null?void 0:h.call(g)}return b};
NH.prototype.copyToHitData=function(a,b,c){var d=N(this.D,a);d===void 0&&(d=b);if(d!==void 0&&c!==void 0&&mb(d)&&E(92))try{d=c(d)}catch(e){}d!==void 0&&U(this,a,d)};
var R=function(a,b){var c=a.metadata[b];if(b===Q.A.Pf){var d;return c==null?void 0:(d=c.H)==null?void 0:d.call(c)}var e;return c==null?void 0:(e=c.getValue)==null?void 0:e.call(c,R(a,Q.A.Pf))},S=function(a,b,c){var d=a.metadata,e;c===void 0?e=void 0:(MH!=null||(MH=new JH(FH,GH)),e=KH(MH,b,c));d[b]=e},PH=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.metadata)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.metadata[e])==null?void 0:(h=(g=f).H)==null?void 0:
h.call(g)}return b},mw=function(a,b,c){var d=ax(a.target.destinationId);return d&&d[b]!==void 0?d[b]:c};function QH(a,b){var c;return c}QH.M="internal.copyPreHit";function RH(a,b){var c=null;return Ed(c,this.K,2)}RH.publicName="createArgumentsQueue";function SH(a){return Ed(function(c){var d=hC();if(typeof c==="function")d(function(){c(function(f,g,h){var m=
hC(),n=m&&m.getByName&&m.getByName(f);return(new x.gaplugins.Linker(n)).decorate(g,h)})});else if(Array.isArray(c)){var e=String(c[0]).split(".");b[e.length===1?e[0]:e[1]]&&d.apply(null,c)}else if(c==="isLoaded")return!!d.loaded},this.K,1)}SH.M="internal.createGaCommandQueue";function TH(a){return Ed(function(){if(!lb(e.push))throw Error("Object at "+a+" in window is not an array.");e.push.apply(e,Array.prototype.slice.call(arguments,0))},this.K,
Dh(vF(this).Jb())?2:1)}TH.publicName="createQueue";function UH(a,b){var c=null;if(!oh(a)||!ph(b))throw F(this.getName(),["string","string|undefined"],arguments);try{var d=(b||"").split("").filter(function(e){return"ig".indexOf(e)>=0}).join("");c=new Ad(new RegExp(a,d))}catch(e){}return c}UH.M="internal.createRegex";function VH(a){}VH.M="internal.declareConsentState";function WH(a){var b="";return b}WH.M="internal.decodeUrlHtmlEntities";function XH(a,b,c){var d;return d}XH.M="internal.decorateUrlWithGaCookies";function YH(){}YH.M="internal.deferCustomEvents";function ZH(a){var b;return b}ZH.M="internal.detectUserProvidedData";
function dI(a,b){return f}dI.M="internal.enableAutoEventOnClick";
function lI(a,b){return p}lI.M="internal.enableAutoEventOnElementVisibility";function mI(){}mI.M="internal.enableAutoEventOnError";var nI={},oI=[],pI={},qI=0,rI=0;
function xI(a,b){var c=this;return d}xI.M="internal.enableAutoEventOnFormInteraction";
function CI(a,b){var c=this;return f}CI.M="internal.enableAutoEventOnFormSubmit";
function HI(){var a=this;}HI.M="internal.enableAutoEventOnGaSend";var II={},JI=[];
function QI(a,b){var c=this;return f}QI.M="internal.enableAutoEventOnHistoryChange";var RI=["http://","https://","javascript:","file://"];
function VI(a,b){var c=this;return h}VI.M="internal.enableAutoEventOnLinkClick";var WI,XI;
function hJ(a,b){var c=this;return d}hJ.M="internal.enableAutoEventOnScroll";function iJ(a){return function(){if(a.limit&&a.sj>=a.limit)a.yh&&x.clearInterval(a.yh);else{a.sj++;var b=Ab();QD({event:a.eventName,"gtm.timerId":a.yh,"gtm.timerEventNumber":a.sj,"gtm.timerInterval":a.interval,"gtm.timerLimit":a.limit,"gtm.timerStartTime":a.Gm,"gtm.timerCurrentTime":b,"gtm.timerElapsedTime":b-a.Gm,"gtm.triggers":a.Oq})}}}
function jJ(a,b){
return f}jJ.M="internal.enableAutoEventOnTimer";var pc=Aa(["data-gtm-yt-inspected-"]),lJ=["www.youtube.com","www.youtube-nocookie.com"],mJ,nJ=!1;
function xJ(a,b){var c=this;return e}xJ.M="internal.enableAutoEventOnYouTubeActivity";nJ=!1;function yJ(a,b){if(!oh(a)||!ih(b))throw F(this.getName(),["string","Object|undefined"],arguments);var c=b?Dd(b):{},d=a,e=!1;return e}yJ.M="internal.evaluateBooleanExpression";var zJ;function AJ(a){var b=!1;return b}AJ.M="internal.evaluateMatchingRules";function jK(){return ur(7)&&ur(9)&&ur(10)};function eL(a,b,c,d){}eL.M="internal.executeEventProcessor";function fL(a){var b;return Ed(b,this.K,1)}fL.M="internal.executeJavascriptString";function gL(a){var b;return b};function hL(a){var b="";return b}hL.M="internal.generateClientId";function iL(a){var b={};return Ed(b)}iL.M="internal.getAdsCookieWritingOptions";function jL(a,b){var c=!1;return c}jL.M="internal.getAllowAdPersonalization";function kL(){var a;return a}kL.M="internal.getAndResetEventUsage";function lL(a,b){b=b===void 0?!0:b;var c;return c}lL.M="internal.getAuid";var mL=null;
function nL(){var a=new Xa;return a}
nL.publicName="getContainerVersion";function oL(a,b){b=b===void 0?!0:b;var c;return c}oL.publicName="getCookieValues";function pL(){var a="";return a}pL.M="internal.getCorePlatformServicesParam";function qL(){return oo()}qL.M="internal.getCountryCode";function rL(){var a=[];return Ed(a)}rL.M="internal.getDestinationIds";function sL(a){var b=new Xa;return b}sL.M="internal.getDeveloperIds";function tL(a){var b;return b}tL.M="internal.getEcsidCookieValue";function uL(a,b){var c=null;return c}uL.M="internal.getElementAttribute";function vL(a){var b=null;return b}vL.M="internal.getElementById";function wL(a){var b="";return b}wL.M="internal.getElementInnerText";function xL(a,b){var c=null;return Ed(c)}xL.M="internal.getElementProperty";function yL(a){var b;return b}yL.M="internal.getElementValue";function zL(a){var b=0;return b}zL.M="internal.getElementVisibilityRatio";function AL(a){var b=null;return b}AL.M="internal.getElementsByCssSelector";
function BL(a){var b;if(!oh(a))throw F(this.getName(),["string"],arguments);H(this,"read_event_data",a);var c;a:{var d=a,e=vF(this).originalEventData;if(e){for(var f=e,g={},h={},m={},n=[],p=d.split("\\\\"),q=0;q<p.length;q++){for(var r=p[q].split("\\."),t=0;t<r.length;t++){for(var u=r[t].split("."),v=0;v<u.length;v++)n.push(u[v]),v!==u.length-1&&n.push(m);t!==r.length-1&&n.push(h)}q!==p.length-1&&n.push(g)}for(var w=[],y="",A=l(n),C=A.next();!C.done;C=
A.next()){var D=C.value;D===m?(w.push(y),y=""):y=D===g?y+"\\":D===h?y+".":y+D}y&&w.push(y);for(var G=l(w),I=G.next();!I.done;I=G.next()){if(f==null){c=void 0;break a}f=f[I.value]}c=f}else c=void 0}b=Ed(c,this.K,1);return b}BL.M="internal.getEventData";var CL={};CL.disableUserDataWithoutCcd=E(223);CL.enableDecodeUri=E(92);CL.enableGaAdsConversions=E(122);CL.enableGaAdsConversionsClientId=E(121);CL.enableOverrideAdsCps=E(170);CL.enableUrlDecodeEventUsage=E(139);function DL(){return Ed(CL)}DL.M="internal.getFlags";function EL(){var a;return a}EL.M="internal.getGsaExperimentId";function FL(){return new Ad(EE)}FL.M="internal.getHtmlId";function GL(a){var b;return b}GL.M="internal.getIframingState";function HL(a,b){var c={};return Ed(c)}HL.M="internal.getLinkerValueFromLocation";function IL(){var a=new Xa;return a}IL.M="internal.getPrivacyStrings";function JL(a,b){var c;return c}JL.M="internal.getProductSettingsParameter";function KL(a,b){var c;return c}KL.publicName="getQueryParameters";function LL(a,b){var c;return c}LL.publicName="getReferrerQueryParameters";function ML(a){var b="";if(!ph(a))throw F(this.getName(),["string|undefined"],arguments);H(this,"get_referrer",a);b=Sk(Wk(z.referrer),a);return b}ML.publicName="getReferrerUrl";function NL(){return po()}NL.M="internal.getRegionCode";function OL(a,b){var c;return c}OL.M="internal.getRemoteConfigParameter";function PL(){var a=new Xa;a.set("width",0);a.set("height",0);return a}PL.M="internal.getScreenDimensions";function QL(){var a="";return a}QL.M="internal.getTopSameDomainUrl";function RL(){var a="";return a}RL.M="internal.getTopWindowUrl";function SL(a){var b="";if(!ph(a))throw F(this.getName(),["string|undefined"],arguments);H(this,"get_url",a);b=Qk(Wk(x.location.href),a);return b}SL.publicName="getUrl";function TL(){H(this,"get_user_agent");return vc.userAgent}TL.M="internal.getUserAgent";function UL(){var a;return a?Ed(Wy(a)):a}UL.M="internal.getUserAgentClientHints";function bM(){var a=x;return a.gaGlobal=a.gaGlobal||{}}function cM(){var a=bM();a.hid=a.hid||qb();return a.hid}function dM(a,b){var c=bM();if(c.vid===void 0||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};
function BM(a){(ny(a)||qk())&&U(a,J.m.Rk,po()||oo());!ny(a)&&qk()&&U(a,J.m.fl,"::")}function CM(a){if(qk()&&!ny(a)&&(so()||U(a,J.m.Fk,!0),E(78))){gw(a);hw(a,Hp.Bf.Wm,Mo(N(a.D,J.m.eb)));var b=Hp.Bf.Xm;var c=N(a.D,J.m.Hc);hw(a,b,c===!0?1:c===!1?0:void 0);hw(a,Hp.Bf.Vm,Mo(N(a.D,J.m.zb)));hw(a,Hp.Bf.Tm,Fs(Lo(N(a.D,J.m.ob)),Lo(N(a.D,J.m.Sb))))}};var XM={AW:vn.Z.Nm,G:vn.Z.Zn,DC:vn.Z.Xn};function YM(a){var b=fj(a);return""+gs(b.map(function(c){return c.value}).join("!"))}function ZM(a){var b=Kp(a);return b&&XM[b.prefix]}function $M(a,b){var c=a[b];c&&(c.clearTimerId&&x.clearTimeout(c.clearTimerId),c.clearTimerId=x.setTimeout(function(){delete a[b]},36E5))};var EN=window,FN=document,GN=function(a){var b=EN._gaUserPrefs;if(b&&b.ioo&&b.ioo()||FN.documentElement.hasAttribute("data-google-analytics-opt-out")||a&&EN["ga-disable-"+a]===!0)return!0;try{var c=EN.external;if(c&&c._gaUserPrefs&&c._gaUserPrefs=="oo")return!0}catch(p){}for(var d=[],e=String(FN.cookie).split(";"),f=0;f<e.length;f++){var g=e[f].split("="),h=g[0].replace(/^\s*|\s*$/g,"");if(h&&h=="AMP_TOKEN"){var m=g.slice(1).join("=").replace(/^\s*|\s*$/g,"");m&&(m=decodeURIComponent(m));d.push(m)}}for(var n=
0;n<d.length;n++)if(d[n]=="$OPT_OUT")return!0;return FN.getElementById("__gaOptOutExtension")?!0:!1};
function SN(a){tb(a,function(c){c.charAt(0)==="_"&&delete a[c]});var b=a[J.m.Xb]||{};tb(b,function(c){c.charAt(0)==="_"&&delete b[c]})};function AO(a,b){}function BO(a,b){var c=function(){};return c}
function CO(a,b,c){};var DO=BO;function FO(a,b,c){var d=this;}FO.M="internal.gtagConfig";
function HO(a,b){}
HO.publicName="gtagSet";function IO(){var a={};return a};function JO(a){}JO.M="internal.initializeServiceWorker";function KO(a,b){}KO.publicName="injectHiddenIframe";var LO=function(){var a=0;return function(b){switch(b){case 1:a|=1;break;case 2:a|=2;break;case 3:a|=4}return a}}();
function MO(a,b,c,d,e){}MO.M="internal.injectHtml";var QO={};
function SO(a,b,c,d){}var TO={dl:1,id:1},UO={};
function VO(a,b,c,d){}E(160)?VO.publicName="injectScript":SO.publicName="injectScript";VO.M="internal.injectScript";function WO(){return to()}WO.M="internal.isAutoPiiEligible";function XO(a){var b=!0;return b}XO.publicName="isConsentGranted";function YO(a){var b=!1;return b}YO.M="internal.isDebugMode";function ZO(){return ro()}ZO.M="internal.isDmaRegion";function $O(a){var b=!1;return b}$O.M="internal.isEntityInfrastructure";function aP(a){var b=!1;return b}aP.M="internal.isFeatureEnabled";function bP(){var a=!1;return a}bP.M="internal.isFpfe";function cP(){var a=!1;return a}cP.M="internal.isGcpConversion";function dP(){var a=!1;return a}dP.M="internal.isLandingPage";function eP(){var a=!1;return a}eP.M="internal.isOgt";function fP(){var a;return a}fP.M="internal.isSafariPcmEligibleBrowser";function gP(){var a=Qh(function(b){vF(this).log("error",b)});a.publicName="JSON";return a};function hP(a){var b=void 0;if(!oh(a))throw F(this.getName(),["string"],arguments);b=Wk(a);return Ed(b)}hP.M="internal.legacyParseUrl";function iP(){return!1}
var jP={getItem:function(a){var b=null;return b},setItem:function(a,b){return!1},removeItem:function(a){}};function kP(){}kP.publicName="logToConsole";function lP(a,b){}lP.M="internal.mergeRemoteConfig";function mP(a,b,c){c=c===void 0?!0:c;var d=[];return Ed(d)}mP.M="internal.parseCookieValuesFromString";function nP(a){var b=void 0;if(typeof a!=="string")return;a&&Fb(a,"//")&&(a=z.location.protocol+a);if(typeof URL==="function"){var c;a:{var d;try{d=new URL(a)}catch(w){c=void 0;break a}for(var e={},f=Array.from(d.searchParams),g=0;g<f.length;g++){var h=f[g][0],m=f[g][1];e.hasOwnProperty(h)?typeof e[h]==="string"?e[h]=[e[h],m]:e[h].push(m):e[h]=m}c=Ed({href:d.href,origin:d.origin,protocol:d.protocol,username:d.username,password:d.password,host:d.host,
hostname:d.hostname,port:d.port,pathname:d.pathname,search:d.search,searchParams:e,hash:d.hash})}return c}var n;try{n=Wk(a)}catch(w){return}if(!n.protocol||!n.host)return;var p={};if(n.search)for(var q=n.search.replace("?","").split("&"),r=0;r<q.length;r++){var t=q[r].split("="),u=t[0],v=Pk(t.splice(1).join("="))||"";v=v.replace(/\+/g," ");p.hasOwnProperty(u)?typeof p[u]==="string"?p[u]=[p[u],v]:p[u].push(v):p[u]=v}n.searchParams=p;n.origin=n.protocol+"//"+n.host;n.username="";n.password="";b=Ed(n);
return b}nP.publicName="parseUrl";function oP(a){}oP.M="internal.processAsNewEvent";function pP(a,b,c){var d;return d}pP.M="internal.pushToDataLayer";function qP(a){var b=Ca.apply(1,arguments),c=!1;return c}qP.publicName="queryPermission";function rP(a){var b=this;}rP.M="internal.queueAdsTransmission";function sP(a,b){var c=void 0;return c}sP.publicName="readAnalyticsStorage";function tP(){var a="";return a}tP.publicName="readCharacterSet";function uP(){return Yj}uP.M="internal.readDataLayerName";function vP(){var a="";return a}vP.publicName="readTitle";function wP(a,b){var c=this;}wP.M="internal.registerCcdCallback";function xP(a,b){return!0}xP.M="internal.registerDestination";var yP=["config","event","get","set"];function zP(a,b,c){}zP.M="internal.registerGtagCommandListener";function AP(a,b){var c=!1;return c}AP.M="internal.removeDataLayerEventListener";function BP(a,b){}
BP.M="internal.removeFormData";function CP(){}CP.publicName="resetDataLayer";function DP(a,b,c){var d=void 0;return d}DP.M="internal.scrubUrlParams";function EP(a){}EP.M="internal.sendAdsHit";function FP(a,b,c,d){}FP.M="internal.sendGtagEvent";function GP(a,b,c){}GP.publicName="sendPixel";function HP(a,b){}HP.M="internal.setAnchorHref";function IP(a){}IP.M="internal.setContainerConsentDefaults";function JP(a,b,c,d){var e=this;d=d===void 0?!0:d;var f=!1;
return f}JP.publicName="setCookie";function KP(a){}KP.M="internal.setCorePlatformServices";function LP(a,b){}LP.M="internal.setDataLayerValue";function MP(a){}MP.publicName="setDefaultConsentState";function NP(a,b){}NP.M="internal.setDelegatedConsentType";function OP(a,b){}OP.M="internal.setFormAction";function PP(a,b,c){c=c===void 0?!1:c;}PP.M="internal.setInCrossContainerData";function QP(a,b,c){return!1}QP.publicName="setInWindow";function RP(a,b,c){}RP.M="internal.setProductSettingsParameter";function SP(a,b,c){}SP.M="internal.setRemoteConfigParameter";function TP(a,b){}TP.M="internal.setTransmissionMode";function UP(a,b,c,d){var e=this;}UP.publicName="sha256";function VP(a,b,c){}
VP.M="internal.sortRemoteConfigParameters";function WP(a){}WP.M="internal.storeAdsBraidLabels";function XP(a,b){var c=void 0;return c}XP.M="internal.subscribeToCrossContainerData";var YP={},ZP={};YP.getItem=function(a){var b=null;return b};YP.setItem=function(a,b){};
YP.removeItem=function(a){};YP.clear=function(){};YP.publicName="templateStorage";function $P(a,b){var c=!1;return c}$P.M="internal.testRegex";function aQ(a){var b;return b};function bQ(a,b){var c;return c}bQ.M="internal.unsubscribeFromCrossContainerData";function cQ(a){}cQ.publicName="updateConsentState";function dQ(a){var b=!1;return b}dQ.M="internal.userDataNeedsEncryption";var eQ;function fQ(a,b,c){eQ=eQ||new ai;eQ.add(a,b,c)}function gQ(a,b){var c=eQ=eQ||new ai;if(c.C.hasOwnProperty(a))throw Error("Attempting to add a private function which already exists: "+a+".");if(c.contains(a))throw Error("Attempting to add a private function with an existing API name: "+a+".");c.C[a]=lb(b)?wh(a,b):xh(a,b)}
function hQ(){return function(a){var b;var c=eQ;if(c.contains(a))b=c.get(a,this);else{var d;if(d=c.C.hasOwnProperty(a)){var e=this.K.ub();if(e){var f=!1,g=e.Jb();if(g){Dh(g)||(f=!0);}d=f}else d=!0}if(d){var h=c.C.hasOwnProperty(a)?c.C[a]:void 0;
b=h}else throw Error(a+" is not a valid API name.");}return b}};function iQ(){var a=function(c){return void gQ(c.M,c)},b=function(c){return void fQ(c.publicName,c)};b(pF);b(wF);b(KG);b(MG);b(NG);b(UG);b(WG);b(RH);b(gP());b(TH);b(nL);b(oL);b(KL);b(LL);b(ML);b(SL);b(HO);b(KO);b(XO);b(kP);b(nP);b(qP);b(tP);b(vP);b(GP);b(JP);b(MP);b(QP);b(UP);b(YP);b(cQ);fQ("Math",Bh());fQ("Object",Zh);fQ("TestHelper",ci());fQ("assertApi",yh);fQ("assertThat",zh);fQ("decodeUri",Eh);fQ("decodeUriComponent",Fh);fQ("encodeUri",Gh);fQ("encodeUriComponent",Hh);fQ("fail",Mh);fQ("generateRandom",
Nh);fQ("getTimestamp",Oh);fQ("getTimestampMillis",Oh);fQ("getType",Ph);fQ("makeInteger",Rh);fQ("makeNumber",Sh);fQ("makeString",Th);fQ("makeTableMap",Uh);fQ("mock",Xh);fQ("mockObject",Yh);fQ("fromBase64",gL,!("atob"in x));fQ("localStorage",jP,!iP());fQ("toBase64",aQ,!("btoa"in x));a(oF);a(sF);a(MF);a(YF);a(eG);a(jG);a(zG);a(IG);a(LG);a(OG);a(PG);a(QG);a(RG);a(SG);a(TG);a(VG);a(XG);a(QH);a(SH);a(UH);a(VH);a(WH);a(XH);a(YH);a(ZH);a(dI);a(lI);a(mI);a(xI);a(CI);a(HI);a(QI);a(VI);a(hJ);a(jJ);a(xJ);a(yJ);
a(AJ);a(eL);a(fL);a(hL);a(iL);a(jL);a(kL);a(lL);a(qL);a(rL);a(sL);a(tL);a(uL);a(vL);a(wL);a(xL);a(yL);a(zL);a(AL);a(BL);a(DL);a(EL);a(FL);a(GL);a(HL);a(IL);a(JL);a(NL);a(OL);a(PL);a(QL);a(RL);a(UL);a(FO);a(JO);a(MO);a(VO);a(WO);a(YO);a(ZO);a($O);a(aP);a(bP);a(cP);a(dP);a(eP);a(fP);a(hP);a(xG);a(lP);a(mP);a(oP);a(pP);a(rP);a(uP);a(wP);a(xP);a(zP);a(AP);a(BP);a(DP);a(EP);a(FP);a(HP);a(IP);a(KP);a(LP);a(NP);a(OP);a(PP);a(RP);a(SP);a(TP);a(VP);a(WP);a(XP);a($P);a(bQ);a(dQ);gQ("internal.IframingStateSchema",
IO());
E(104)&&a(pL);E(160)?b(VO):b(SO);E(177)&&b(sP);return hQ()};var mF;
function jQ(){var a=data.sandboxed_scripts,b=data.security_groups;a:{var c=data.runtime||[],d=data.runtime_lines;mF=new Ye;kQ();Ff=lF();var e=mF,f=iQ(),g=new wd("require",f);g.Wa();e.C.C.set("require",g);Sa.set("require",g);for(var h=[],m=0;m<c.length;m++){var n=c[m];if(!Array.isArray(n)||n.length<3){if(n.length===0)continue;break a}d&&d[m]&&d[m].length&&ag(n,d[m]);try{mF.execute(n),E(120)&&kl&&n[0]===50&&h.push(n[1])}catch(r){}}E(120)&&(Tf=h)}if(a&&a.length)for(var p=0;p<a.length;p++){var q=a[p].replace(/^_*/,
"");mk[q]=["sandboxedScripts"]}lQ(b)}function kQ(){mF.Vc(function(a,b,c){zp.SANDBOXED_JS_SEMAPHORE=zp.SANDBOXED_JS_SEMAPHORE||0;zp.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{zp.SANDBOXED_JS_SEMAPHORE--}})}function lQ(a){a&&tb(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");mk[e]=mk[e]||[];mk[e].push(b)}})};function mQ(a){Uw(Ow("developer_id."+a,!0),0,{})};var nQ=Array.isArray;function oQ(a,b){return od(a,b||null)}function W(a){return window.encodeURIComponent(a)}function pQ(a,b,c){Lc(a,b,c)}
function qQ(a){var b=["veinteractive.com","ve-interactive.cn"];if(!a)return!1;var c=Qk(Wk(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;f>0&&e.charAt(0)!=="."&&(f--,e="."+e);if(f>=0&&c.indexOf(e,f)===f)return!0}}return!1}function rQ(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null}
function sQ(a,b){var c={};if(a)for(var d in a)a.hasOwnProperty(d)&&(c[d]=a[d]);if(b){var e=rQ(b,"parameter","parameterValue");e&&(c=oQ(e,c))}return c}function tQ(a,b,c){return a===void 0||a===c?b:a}function uQ(a,b,c){return Hc(a,b,c,void 0)}function vQ(){return x.location.href}function wQ(a,b){return wk(a,b||2)}function xQ(a,b){x[a]=b}function yQ(a,b,c){var d=x;b&&(d[a]===void 0||c&&!d[a])&&(d[a]=b);return d[a]}

var zQ={};var Z={securityGroups:{}};

Z.securityGroups.get_referrer=["google"],function(){function a(b,c,d){return{component:c,queryKey:d}}(function(b){Z.__get_referrer=b;Z.__get_referrer.F="get_referrer";Z.__get_referrer.isVendorTemplate=!0;Z.__get_referrer.priorityOverride=0;Z.__get_referrer.isInfrastructure=!1;Z.__get_referrer["5"]=!1})(function(b){var c=b.vtp_urlParts==="any"?null:[];c&&(b.vtp_protocol&&c.push("protocol"),b.vtp_host&&c.push("host"),b.vtp_port&&c.push("port"),b.vtp_path&&c.push("path"),b.vtp_extension&&c.push("extension"),
b.vtp_query&&c.push("query"));var d=c&&b.vtp_queriesAllowed!=="any"?b.vtp_queryKeys||[]:null,e=b.vtp_createPermissionError;return{assert:function(f,g,h){if(g){if(!mb(g))throw e(f,{},"URL component must be a string.");if(c&&c.indexOf(g)<0)throw e(f,{},"Prohibited URL component: "+g);if(g==="query"&&d){if(!h)throw e(f,{},"Prohibited from getting entire URL query when query keys are specified.");if(!mb(h))throw e(f,{},"Query key must be a string.");if(d.indexOf(h)<0)throw e(f,{},"Prohibited query key: "+
h);}}else if(c)throw e(f,{},"Prohibited from getting entire URL when components are specified.");},T:a}})}();
Z.securityGroups.read_event_data=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_event_data=b;Z.__read_event_data.F="read_event_data";Z.__read_event_data.isVendorTemplate=!0;Z.__read_event_data.priorityOverride=0;Z.__read_event_data.isInfrastructure=!1;Z.__read_event_data["5"]=!1})(function(b){var c=b.vtp_eventDataAccess,d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(g!=null&&!mb(g))throw e(f,{key:g},"Key must be a string.");if(c!=="any"){try{if(c===
"specific"&&g!=null&&Mg(g,d))return}catch(h){throw e(f,{key:g},"Invalid key filter.");}throw e(f,{key:g},"Prohibited read from event data.");}},T:a}})}();

Z.securityGroups.read_data_layer=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_data_layer=b;Z.__read_data_layer.F="read_data_layer";Z.__read_data_layer.isVendorTemplate=!0;Z.__read_data_layer.priorityOverride=0;Z.__read_data_layer.isInfrastructure=!1;Z.__read_data_layer["5"]=!1})(function(b){var c=b.vtp_allowedKeys||"specific",d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(!mb(g))throw e(f,{},"Keys must be strings.");if(c!=="any"){try{if(Mg(g,
d))return}catch(h){throw e(f,{},"Invalid key filter.");}throw e(f,{},"Prohibited read from data layer variable: "+g+".");}},T:a}})}();









Z.securityGroups.get_url=["google"],function(){function a(b,c,d){return{component:c,queryKey:d}}(function(b){Z.__get_url=b;Z.__get_url.F="get_url";Z.__get_url.isVendorTemplate=!0;Z.__get_url.priorityOverride=0;Z.__get_url.isInfrastructure=!1;Z.__get_url["5"]=!1})(function(b){var c=b.vtp_urlParts==="any"?null:[];c&&(b.vtp_protocol&&c.push("protocol"),b.vtp_host&&c.push("host"),b.vtp_port&&c.push("port"),b.vtp_path&&c.push("path"),b.vtp_extension&&c.push("extension"),b.vtp_query&&c.push("query"),b.vtp_fragment&&
c.push("fragment"));var d=c&&b.vtp_queriesAllowed!=="any"?b.vtp_queryKeys||[]:null,e=b.vtp_createPermissionError;return{assert:function(f,g,h){if(g){if(!mb(g))throw e(f,{},"URL component must be a string.");if(c&&c.indexOf(g)<0)throw e(f,{},"Prohibited URL component: "+g);if(g==="query"&&d){if(!h)throw e(f,{},"Prohibited from getting entire URL query when query keys are specified.");if(!mb(h))throw e(f,{},"Query key must be a string.");if(d.indexOf(h)<0)throw e(f,{},"Prohibited query key: "+h);}}else if(c)throw e(f,
{},"Prohibited from getting entire URL when components are specified.");},T:a}})}();










var Cp={dataLayer:xk,callback:function(a){lk.hasOwnProperty(a)&&lb(lk[a])&&lk[a]();delete lk[a]},bootstrap:0};
function AQ(){Bp();Lm();TB();Db(mk,Z.securityGroups);var a=Im(xm()),b,c=a==null?void 0:(b=a.context)==null?void 0:b.source;ap(c,a==null?void 0:a.parent);c!==2&&c!==4&&c!==3||L(142);Sf={So:gg}}var BQ=!1;
function lo(){try{if(BQ||!Sm()){Uj();Rj.P=Ui(18,"");
Rj.rb="ad_storage|analytics_storage|ad_user_data|ad_personalization";Rj.Ua="ad_storage|analytics_storage|ad_user_data";Rj.Da="57f0";Rj.Da="57f0";if(E(109)){}Pa[7]=!0;var a=Ap("debugGroupId",function(){return String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()))});hp(a);yp();cF();nr();Ep();if(Mm()){uG();JC().removeExternalRestrictions(Fm());}else{Qf();Lf=Z;Nf=NE;ig=new pg;jQ();AQ();LE();jo||(io=no());
vp();XD();kD();ED=!1;z.readyState==="complete"?GD():Mc(x,"load",GD);eD();kl&&(rq(Fq),x.setInterval(Eq,864E5),rq(dF),rq(wC),rq(gA),rq(Iq),rq(iF),rq(HC),E(120)&&(rq(BC),rq(CC),rq(DC)),eF={},rq(fF),Xi());ll&&(Wn(),Yp(),ZD(),cE(),aE(),Mn("bt",String(Rj.C?2:Rj.N?1:0)),Mn("ct",String(Rj.C?0:Rj.N?1:3)),$D());
CE();go(1);vG();hE();kk=Ab();Cp.bootstrap=kk;Rj.ma&&WD();E(109)&&CA();E(134)&&(typeof x.name==="string"&&Fb(x.name,"web-pixel-sandbox-CUSTOM")&&dd()?mQ("dMDg0Yz"):x.Shopify&&(mQ("dN2ZkMj"),dd()&&mQ("dNTU0Yz")))}}}catch(b){go(4),Bq()}}
(function(a){function b(){n=z.documentElement.getAttribute("data-tag-assistant-present");Oo(n)&&(m=h.Xk)}function c(){m&&yc?g(m):a()}if(!x[Ui(37,"__TAGGY_INSTALLED")]){var d=!1;if(z.referrer){var e=Wk(z.referrer);d=Sk(e,"host")===Ui(38,"cct.google")}if(!d){var f=ps(Ui(39,"googTaggyReferrer"));d=!(!f.length||!f[0].length)}d&&(x[Ui(37,"__TAGGY_INSTALLED")]=!0,Hc(Ui(40,"https://cct.google/taggy/agent.js")))}var g=function(u){var v="GTM",w="GTM";fk&&(v="OGT",w="GTAG");
var y=Ui(23,"google.tagmanager.debugui2.queue"),A=x[y];A||(A=[],x[y]=A,Hc("https://"+Vj.wg+"/debug/bootstrap?id="+mg.ctid+"&src="+w+"&cond="+String(u)+"&gtm="+Pr()));var C={messageType:"CONTAINER_STARTING",data:{scriptSource:yc,containerProduct:v,debug:!1,id:mg.ctid,targetRef:{ctid:mg.ctid,isDestination:Dm()},aliases:Gm(),destinations:Em()}};C.data.resume=function(){a()};Vj.Sm&&(C.data.initialPublish=!0);A.push(C)},h={co:1,al:2,wl:3,Wj:4,Xk:5};h[h.co]="GTM_DEBUG_LEGACY_PARAM";h[h.al]="GTM_DEBUG_PARAM";h[h.wl]="REFERRER";
h[h.Wj]="COOKIE";h[h.Xk]="EXTENSION_PARAM";var m=void 0,n=void 0,p=Qk(x.location,"query",!1,void 0,"gtm_debug");Oo(p)&&(m=h.al);if(!m&&z.referrer){var q=Wk(z.referrer);Sk(q,"host")===Ui(24,"tagassistant.google.com")&&(m=h.wl)}if(!m){var r=ps("__TAG_ASSISTANT");r.length&&r[0].length&&(m=h.Wj)}m||b();if(!m&&No(n)){var t=!1;Mc(z,"TADebugSignal",function(){t||(t=!0,b(),c())},!1);x.setTimeout(function(){t||(t=!0,b(),c())},200)}else c()})(function(){E(83)&&BQ&&!no()["0"]?ko():lo()});

})()

