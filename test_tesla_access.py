#!/usr/bin/env python3
"""
Test Tesla website access
Quick test to see what's happening with Tesla website access
"""

import requests
import time

def test_tesla_access():
    """Test different approaches to access Tesla"""
    
    print("🧪 Testing Tesla Website Access")
    print("=" * 50)
    
    # Test 1: Basic access to main Tesla site
    print("\n1. Testing main Tesla website...")
    try:
        response = requests.get("https://www.tesla.com", timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Main site accessible")
        else:
            print(f"   ⚠️  Main site returned: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Main site failed: {e}")
    
    # Test 2: Turkey inventory page
    print("\n2. Testing Turkey inventory page...")
    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"
        }
        
        url = "https://www.tesla.com/tr_TR/inventory/new/my"
        response = requests.get(url, headers=headers, timeout=15)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Turkey inventory page accessible")
            content_length = len(response.text)
            print(f"   📄 Page content length: {content_length} characters")
            
            # Check for some key content
            content = response.text.lower()
            if "model y" in content:
                print("   ✅ Found 'Model Y' in page")
            if "inventory" in content:
                print("   ✅ Found 'inventory' in page")
            if "tesla" in content:
                print("   ✅ Found 'tesla' in page")
                
        else:
            print(f"   ❌ Turkey inventory failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Turkey inventory failed: {e}")
    
    # Test 3: API endpoint
    print("\n3. Testing API endpoint...")
    try:
        api_headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "tr-TR,tr;q=0.9,en;q=0.8",
            "Referer": "https://www.tesla.com/tr_TR/inventory/new/my"
        }
        
        api_url = "https://www.tesla.com/coinorder/api/v4/inventory-results"
        params = {
            "model": "my",
            "condition": "new",
            "market": "TR",
            "language": "tr"
        }
        
        response = requests.get(api_url, params=params, headers=api_headers, timeout=15)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ API endpoint accessible")
            try:
                data = response.json()
                print(f"   📊 API returned data: {type(data)}")
                if isinstance(data, dict) and "results" in data:
                    results = data["results"]
                    print(f"   🚗 Found {len(results)} vehicles in API")
                else:
                    print("   ⚠️  Unexpected API response format")
            except:
                print("   ⚠️  API response not valid JSON")
        else:
            print(f"   ❌ API failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ API failed: {e}")
    
    # Test 4: Check if we're being blocked
    print("\n4. Testing for blocking...")
    try:
        # Try multiple requests to see if we get blocked
        for i in range(3):
            response = requests.get("https://www.tesla.com", timeout=5)
            print(f"   Request {i+1}: {response.status_code}")
            time.sleep(2)
            
    except Exception as e:
        print(f"   ❌ Blocking test failed: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 Test completed")
    
    # Recommendations
    print("\n💡 Recommendations:")
    print("   - If main site works but API fails → Use HTML scraping approach")
    print("   - If getting 403 errors → Tesla is blocking automated requests")
    print("   - If timeouts → Network or Tesla server issues")
    print("   - Consider using the simple monitor version")

if __name__ == "__main__":
    test_tesla_access()
